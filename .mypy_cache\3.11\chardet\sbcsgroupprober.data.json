{".class": "MypyFile", "_fullname": "chardet.sbcsgroupprober", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CharSetGroupProber": {".class": "SymbolTableNode", "cross_ref": "chardet.charsetgroupprober.CharSetGroupProber", "kind": "Gdef"}, "HebrewProber": {".class": "SymbolTableNode", "cross_ref": "chardet.hebrewprober.HebrewProber", "kind": "Gdef"}, "IBM855_RUSSIAN_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langrussianmodel.IBM855_RUSSIAN_MODEL", "kind": "Gdef"}, "IBM866_RUSSIAN_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langrussianmodel.IBM866_RUSSIAN_MODEL", "kind": "Gdef"}, "ISO_8859_5_BULGARIAN_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langbulgarianmodel.ISO_8859_5_BULGARIAN_MODEL", "kind": "Gdef"}, "ISO_8859_5_RUSSIAN_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langrussianmodel.ISO_8859_5_RUSSIAN_MODEL", "kind": "Gdef"}, "ISO_8859_7_GREEK_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langgreekmodel.ISO_8859_7_GREEK_MODEL", "kind": "Gdef"}, "ISO_8859_9_TURKISH_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langturkishmodel.ISO_8859_9_TURKISH_MODEL", "kind": "Gdef"}, "KOI8_R_RUSSIAN_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langrussianmodel.KOI8_R_RUSSIAN_MODEL", "kind": "Gdef"}, "MACCYRILLIC_RUSSIAN_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langrussianmodel.MACCYRILLIC_RUSSIAN_MODEL", "kind": "Gdef"}, "SBCSGroupProber": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.charsetgroupprober.CharSetGroupProber"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.sbcsgroupprober.SBCSGroupProber", "name": "SBCSGroupProber", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.sbcsgroupprober.SBCSGroupProber", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.sbcsgroupprober", "mro": ["chardet.sbcsgroupprober.SBCSGroupProber", "chardet.charsetgroupprober.CharSetGroupProber", "chardet.charsetprober.CharSetProber", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.sbcsgroupprober.SBCSGroupProber.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.sbcsgroupprober.SBCSGroupProber"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SBCSGroupProber", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.sbcsgroupprober.SBCSGroupProber.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.sbcsgroupprober.SBCSGroupProber", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SingleByteCharSetProber": {".class": "SymbolTableNode", "cross_ref": "chardet.sbcharsetprober.SingleByteCharSetProber", "kind": "Gdef"}, "TIS_620_THAI_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langthaimodel.TIS_620_THAI_MODEL", "kind": "Gdef"}, "WINDOWS_1251_BULGARIAN_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langbulgarianmodel.WINDOWS_1251_BULGARIAN_MODEL", "kind": "Gdef"}, "WINDOWS_1251_RUSSIAN_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langrussianmodel.WINDOWS_1251_RUSSIAN_MODEL", "kind": "Gdef"}, "WINDOWS_1253_GREEK_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langgreekmodel.WINDOWS_1253_GREEK_MODEL", "kind": "Gdef"}, "WINDOWS_1255_HEBREW_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.langhebrewmodel.WINDOWS_1255_HEBREW_MODEL", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.sbcsgroupprober.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.sbcsgroupprober.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.sbcsgroupprober.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.sbcsgroupprober.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.sbcsgroupprober.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.sbcsgroupprober.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\chardet\\sbcsgroupprober.py"}