{".class": "MypyFile", "_fullname": "charset_normalizer.constant", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BOM_UTF16_BE": {".class": "SymbolTableNode", "cross_ref": "codecs.BOM_UTF16_BE", "kind": "Gdef"}, "BOM_UTF16_LE": {".class": "SymbolTableNode", "cross_ref": "codecs.BOM_UTF16_LE", "kind": "Gdef"}, "BOM_UTF32_BE": {".class": "SymbolTableNode", "cross_ref": "codecs.BOM_UTF32_BE", "kind": "Gdef"}, "BOM_UTF32_LE": {".class": "SymbolTableNode", "cross_ref": "codecs.BOM_UTF32_LE", "kind": "Gdef"}, "BOM_UTF8": {".class": "SymbolTableNode", "cross_ref": "codecs.BOM_UTF8", "kind": "Gdef"}, "CHARDET_CORRESPONDENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.CHARDET_CORRESPONDENCE", "name": "CHARDET_CORRESPONDENCE", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "COMMON_SAFE_ASCII_CHARACTERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.COMMON_SAFE_ASCII_CHARACTERS", "name": "COMMON_SAFE_ASCII_CHARACTERS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ENCODING_MARKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.ENCODING_MARKS", "name": "ENCODING_MARKS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "FREQUENCIES": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.assets.FREQUENCIES", "kind": "Gdef"}, "IANA_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.IANA_SUPPORTED", "name": "IANA_SUPPORTED", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "IANA_SUPPORTED_COUNT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.IANA_SUPPORTED_COUNT", "name": "IANA_SUPPORTED_COUNT", "type": "builtins.int"}}, "IANA_SUPPORTED_SIMILAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.IANA_SUPPORTED_SIMILAR", "name": "IANA_SUPPORTED_SIMILAR", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "IGNORECASE": {".class": "SymbolTableNode", "cross_ref": "re.IGNORECASE", "kind": "Gdef"}, "KO_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.KO_NAMES", "name": "KO_NAMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "LANGUAGE_SUPPORTED_COUNT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.LANGUAGE_SUPPORTED_COUNT", "name": "LANGUAGE_SUPPORTED_COUNT", "type": "builtins.int"}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "RE_POSSIBLE_ENCODING_INDICATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "charset_normalizer.constant.RE_POSSIBLE_ENCODING_INDICATION", "name": "RE_POSSIBLE_ENCODING_INDICATION", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TOO_BIG_SEQUENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.TOO_BIG_SEQUENCE", "name": "TOO_BIG_SEQUENCE", "type": "builtins.int"}}, "TOO_SMALL_SEQUENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.TOO_SMALL_SEQUENCE", "name": "TOO_SMALL_SEQUENCE", "type": "builtins.int"}}, "TRACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.TRACE", "name": "TRACE", "type": "builtins.int"}}, "UNICODE_RANGES_COMBINED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.UNICODE_RANGES_COMBINED", "name": "UNICODE_RANGES_COMBINED", "type": {".class": "Instance", "args": ["builtins.str", "builtins.range"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "UNICODE_SECONDARY_RANGE_KEYWORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.UNICODE_SECONDARY_RANGE_KEYWORD", "name": "UNICODE_SECONDARY_RANGE_KEYWORD", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "UTF8_MAXIMAL_ALLOCATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.UTF8_MAXIMAL_ALLOCATION", "name": "UTF8_MAXIMAL_ALLOCATION", "type": "builtins.int"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "ZH_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "charset_normalizer.constant.ZH_NAMES", "name": "ZH_NAMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.constant.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.constant.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.constant.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.constant.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.constant.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.constant.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aliases": {".class": "SymbolTableNode", "cross_ref": "encodings.aliases.aliases", "kind": "Gdef"}, "re_compile": {".class": "SymbolTableNode", "cross_ref": "re.compile", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\charset_normalizer\\constant.py"}