{"data_mtime": 1753366013, "dep_lines": [3, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30], "dependencies": ["importlib_metadata._functools", "re", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "5706a824d57d684b2985ee3a05a77ac152f55ebc", "id": "importlib_metadata._text", "ignore_all": true, "interface_hash": "5a34ce6c64446a5bc0a46a95e714efcef50f8cb2", "mtime": 1750258716, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\importlib_metadata\\_text.py", "plugin_data": null, "size": 2166, "suppressed": [], "version_id": "1.15.0"}