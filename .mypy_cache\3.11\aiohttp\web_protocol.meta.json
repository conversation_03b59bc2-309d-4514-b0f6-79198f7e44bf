{"data_mtime": 1753366020, "dep_lines": [2, 28, 29, 30, 31, 38, 39, 40, 41, 42, 43, 44, 45, 50, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 25, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["asyncio.streams", "aiohttp.abc", "aiohttp.base_protocol", "aiohttp.helpers", "aiohttp.http", "aiohttp.http_exceptions", "aiohttp.log", "aiohttp.streams", "aiohttp.tcp_helpers", "aiohttp.web_exceptions", "aiohttp.web_log", "aiohttp.web_request", "aiohttp.web_response", "aiohttp.web_server", "asyncio", "sys", "traceback", "warnings", "collections", "contextlib", "html", "http", "logging", "typing", "attr", "yarl", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "aiohttp.http_parser", "aiohttp.http_writer", "asyncio.events", "asyncio.exceptions", "asyncio.protocols", "asyncio.timeouts", "asyncio.transports", "attr.setters", "concurrent", "concurrent.futures", "concurrent.futures._base", "enum", "multidict", "types", "typing_extensions", "urllib", "urllib.parse", "yarl._url"], "hash": "01ccab1875a888ab90535379d6ea84df04e07cb4", "id": "aiohttp.web_protocol", "ignore_all": true, "interface_hash": "81060e75ddad591334327001c65b4606c53cc936", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web_protocol.py", "plugin_data": null, "size": 26268, "suppressed": [], "version_id": "1.15.0"}