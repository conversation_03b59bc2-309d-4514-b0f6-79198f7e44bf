{".class": "MypyFile", "_fullname": "azure.core.rest._requests_basic", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CaseInsensitiveDict": {".class": "SymbolTableNode", "cross_ref": "requests.structures.CaseInsensitiveDict", "kind": "Gdef"}, "HttpResponseImpl": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._http_response_impl.HttpResponseImpl", "kind": "Gdef"}, "RestRequestsTransportResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.rest._http_response_impl.HttpResponseImpl", "azure.core.rest._requests_basic._RestRequestsTransportResponseBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.rest._requests_basic.RestRequestsTransportResponse", "name": "RestRequestsTransportResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic.RestRequestsTransportResponse", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.rest._requests_basic", "mro": ["azure.core.rest._requests_basic.RestRequestsTransportResponse", "azure.core.rest._http_response_impl.HttpResponseImpl", "azure.core.rest._requests_basic._RestRequestsTransportResponseBase", "azure.core.rest._http_response_impl._HttpResponseBaseImpl", "azure.core.rest._rest_py3.HttpResponse", "azure.core.rest._rest_py3._HttpResponseBase", "abc.ABC", "azure.core.rest._http_response_impl.HttpResponseBackcompatMixin", "azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin", "azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic.RestRequestsTransportResponse.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.rest._requests_basic.RestRequestsTransportResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.rest._requests_basic.RestRequestsTransportResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamDownloadGenerator": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator", "kind": "Gdef"}, "_CaseInsensitiveDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "requests.structures.CaseInsensitiveDict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.rest._requests_basic._CaseInsensitiveDict", "name": "_CaseInsensitiveDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic._CaseInsensitiveDict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.rest._requests_basic", "mro": ["azure.core.rest._requests_basic._CaseInsensitiveDict", "requests.structures.CaseInsensitiveDict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic._CaseInsensitiveDict.items", "name": "items", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.rest._requests_basic._CaseInsensitiveDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.rest._requests_basic._CaseInsensitiveDict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_HttpResponseBackcompatMixinBase": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase", "kind": "Gdef"}, "_HttpResponseBaseImpl": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._http_response_impl._HttpResponseBaseImpl", "kind": "Gdef"}, "_ItemsView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.ItemsView"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.rest._requests_basic._ItemsView", "name": "_ItemsView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic._ItemsView", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.rest._requests_basic", "mro": ["azure.core.rest._requests_basic._ItemsView", "typing.ItemsView", "typing.MappingView", "typing.Sized", "typing.AbstractSet", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic._ItemsView.__contains__", "name": "__contains__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic._ItemsView.__repr__", "name": "__repr__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.rest._requests_basic._ItemsView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.rest._requests_basic._ItemsView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RestRequestsTransportResponseBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.rest._http_response_impl._HttpResponseBaseImpl", "azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.rest._requests_basic._RestRequestsTransportResponseBase", "name": "_RestRequestsTransportResponseBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic._RestRequestsTransportResponseBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.rest._requests_basic", "mro": ["azure.core.rest._requests_basic._RestRequestsTransportResponseBase", "azure.core.rest._http_response_impl._HttpResponseBaseImpl", "azure.core.rest._rest_py3._HttpResponseBase", "abc.ABC", "azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin", "azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic._RestRequestsTransportResponseBase.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.rest._requests_basic._RestRequestsTransportResponseBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.rest._requests_basic._RestRequestsTransportResponseBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RestRequestsTransportResponseBaseMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin", "name": "_RestRequestsTransportResponseBaseMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.rest._requests_basic", "mro": ["azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin", "azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase", "builtins.object"], "names": {".class": "SymbolTable", "_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin._body", "name": "_body", "type": null}}, "_content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin._content", "name": "_content", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.rest._requests_basic._RestRequestsTransportResponseBaseMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._requests_basic.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._requests_basic.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._requests_basic.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._requests_basic.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._requests_basic.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._requests_basic.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections.abc", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\rest\\_requests_basic.py"}