{".class": "MypyFile", "_fullname": "aiohttp.client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractCookieJar": {".class": "SymbolTableNode", "cross_ref": "aiohttp.abc.AbstractCookieJar", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_public": false}, "BaseConnector": {".class": "SymbolTableNode", "cross_ref": "aiohttp.connector.BaseConnector", "kind": "Gdef"}, "BasicAuth": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.BasicAuth", "kind": "Gdef", "module_public": false}, "CIMultiDict": {".class": "SymbolTableNode", "cross_ref": "multidict.CIMultiDict", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ClientConnectionError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectionError", "kind": "Gdef"}, "ClientConnectionResetError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectionResetError", "kind": "Gdef"}, "ClientConnectorCertificateError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectorCertificateError", "kind": "Gdef"}, "ClientConnectorDNSError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectorDNSError", "kind": "Gdef"}, "ClientConnectorError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectorError", "kind": "Gdef"}, "ClientConnectorSSLError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectorSSLError", "kind": "Gdef"}, "ClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientError", "kind": "Gdef"}, "ClientHttpProxyError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientHttpProxyError", "kind": "Gdef"}, "ClientOSError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientOSError", "kind": "Gdef"}, "ClientPayloadError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientPayloadError", "kind": "Gdef"}, "ClientProxyConnectionError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientProxyConnectionError", "kind": "Gdef"}, "ClientRequest": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.ClientRequest", "kind": "Gdef"}, "ClientResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.ClientResponse", "kind": "Gdef"}, "ClientResponseError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientResponseError", "kind": "Gdef"}, "ClientSSLError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientSSLError", "kind": "Gdef"}, "ClientSession": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.client.ClientSession", "name": "ClientSession", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.client", "mro": ["aiohttp.client.ClientSession", "builtins.object"], "names": {".class": "SymbolTable", "ATTRS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.client.ClientSession.ATTRS", "name": "ATTRS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client.ClientSession.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of ClientSession", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "aiohttp.client.ClientSession"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client.ClientSession.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "arg_types": ["aiohttp.client.ClientSession", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of ClientSession", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["aiohttp.client.ClientSession", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of ClientSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of ClientSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["aiohttp.client.ClientSession", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of ClientSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "base_url", "connector", "loop", "cookies", "headers", "proxy", "proxy_auth", "skip_auto_headers", "auth", "json_serialize", "request_class", "response_class", "ws_response_class", "version", "cookie_jar", "connector_owner", "raise_for_status", "read_timeout", "conn_timeout", "timeout", "auto_decompress", "trust_env", "requote_redirect_url", "trace_configs", "read_bufsize", "max_line_size", "max_field_size", "fallback_charset_resolver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "base_url", "connector", "loop", "cookies", "headers", "proxy", "proxy_auth", "skip_auto_headers", "auth", "json_serialize", "request_class", "response_class", "ws_response_class", "version", "cookie_jar", "connector_owner", "raise_for_status", "read_timeout", "conn_timeout", "timeout", "auto_decompress", "trust_env", "requote_redirect_url", "trace_configs", "read_bufsize", "max_line_size", "max_field_size", "fallback_charset_resolver"], "arg_types": ["aiohttp.client.ClientSession", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["aiohttp.connector.BaseConnector", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.JSONEncoder"}, {".class": "TypeType", "item": "aiohttp.client_reqrep.ClientRequest"}, {".class": "TypeType", "item": "aiohttp.client_reqrep.ClientResponse"}, {".class": "TypeType", "item": "aiohttp.client_ws.ClientWebSocketResponse"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "UnionType", "items": ["aiohttp.abc.AbstractCookieJar", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.object", "aiohttp.client.ClientTimeout"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._CharsetResolver"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "aiohttp.client.ClientSession.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "aiohttp.client.ClientSession"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of ClientSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession.__setattr__", "name": "__setattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "val"], "arg_types": ["aiohttp.client.ClientSession", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setattr__ of ClientSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_auto_decompress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._auto_decompress", "name": "_auto_decompress", "type": "builtins.bool"}}, "_base_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession._base_url", "name": "_base_url", "type": {".class": "UnionType", "items": ["yarl._url.URL", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_base_url_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._base_url_origin", "name": "_base_url_origin", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}}}, "_build_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "str_or_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession._build_url", "name": "_build_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "str_or_url"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_url of ClientSession", "ret_type": "yarl._url.URL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "aiohttp.client.ClientSession._connector", "name": "_connector", "type": {".class": "UnionType", "items": ["aiohttp.connector.BaseConnector", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_connector_owner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._connector_owner", "name": "_connector_owner", "type": "builtins.bool"}}, "_cookie_jar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._cookie_jar", "name": "_cookie_jar", "type": "aiohttp.abc.AbstractCookieJar"}}, "_default_auth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._default_auth", "name": "_default_auth", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_default_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession._default_headers", "name": "_default_headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}}}, "_default_proxy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._default_proxy", "name": "_default_proxy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_default_proxy_auth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._default_proxy_auth", "name": "_default_proxy_auth", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_json_serialize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._json_serialize", "name": "_json_serialize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._loop", "name": "_loop", "type": "asyncio.events.AbstractEventLoop"}}, "_max_field_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._max_field_size", "name": "_max_field_size", "type": "builtins.int"}}, "_max_line_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._max_line_size", "name": "_max_line_size", "type": "builtins.int"}}, "_prepare_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession._prepare_headers", "name": "_prepare_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["aiohttp.client.ClientSession", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_headers of ClientSession", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raise_for_status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._raise_for_status", "name": "_raise_for_status", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "_read_bufsize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._read_bufsize", "name": "_read_bufsize", "type": "builtins.int"}}, "_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "method", "str_or_url", "params", "data", "json", "cookies", "headers", "skip_auto_headers", "auth", "allow_redirects", "max_redirects", "compress", "chunked", "expect100", "raise_for_status", "read_until_eof", "proxy", "proxy_auth", "timeout", "verify_ssl", "fingerprint", "ssl_context", "ssl", "server_hostname", "proxy_headers", "trace_request_ctx", "read_bufsize", "auto_decompress", "max_line_size", "max_field_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client.ClientSession._request", "name": "_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "method", "str_or_url", "params", "data", "json", "cookies", "headers", "skip_auto_headers", "auth", "allow_redirects", "max_redirects", "compress", "chunked", "expect100", "raise_for_status", "read_until_eof", "proxy", "proxy_auth", "timeout", "verify_ssl", "fingerprint", "ssl_context", "ssl", "server_hostname", "proxy_headers", "trace_request_ctx", "read_bufsize", "auto_decompress", "max_line_size", "max_field_size"], "arg_types": ["aiohttp.client.ClientSession", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_request of ClientSession", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "aiohttp.client_reqrep.ClientResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._request_class", "name": "_request_class", "type": {".class": "TypeType", "item": "aiohttp.client_reqrep.ClientRequest"}}}, "_requote_redirect_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._requote_redirect_url", "name": "_requote_redirect_url", "type": "builtins.bool"}}, "_resolve_charset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._resolve_charset", "name": "_resolve_charset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.client_reqrep.ClientResponse", "builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_response_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._response_class", "name": "_response_class", "type": {".class": "TypeType", "item": "aiohttp.client_reqrep.ClientResponse"}}}, "_retry_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession._retry_connection", "name": "_retry_connection", "type": "builtins.bool"}}, "_skip_auto_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._skip_auto_headers", "name": "_skip_auto_headers", "type": {".class": "Instance", "args": ["multidict.istr"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_source_traceback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "aiohttp.client.ClientSession._source_traceback", "name": "_source_traceback", "type": {".class": "UnionType", "items": ["traceback.StackSummary", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._timeout", "name": "_timeout", "type": "aiohttp.client.ClientTimeout"}}, "_trace_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._trace_configs", "name": "_trace_configs", "type": {".class": "Instance", "args": ["aiohttp.tracing.TraceConfig"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_trust_env": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._trust_env", "name": "_trust_env", "type": "builtins.bool"}}, "_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._version", "name": "_version", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}}}, "_ws_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "url", "method", "protocols", "timeout", "receive_timeout", "autoclose", "autoping", "heartbeat", "auth", "origin", "params", "headers", "proxy", "proxy_auth", "ssl", "verify_ssl", "fingerprint", "ssl_context", "server_hostname", "proxy_headers", "compress", "max_msg_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client.ClientSession._ws_connect", "name": "_ws_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "url", "method", "protocols", "timeout", "receive_timeout", "autoclose", "autoping", "heartbeat", "auth", "origin", "params", "headers", "proxy", "proxy_auth", "ssl", "verify_ssl", "fingerprint", "ssl_context", "server_hostname", "proxy_headers", "compress", "max_msg_size"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["aiohttp.client_ws.ClientWSTimeout", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ws_connect of ClientSession", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "aiohttp.client_ws.ClientWebSocketResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ws_response_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client.ClientSession._ws_response_class", "name": "_ws_response_class", "type": {".class": "TypeType", "item": "aiohttp.client_ws.ClientWebSocketResponse"}}}, "auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.auth", "name": "auth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auth of ClientSession", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.auth", "name": "auth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auth of ClientSession", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "auto_decompress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.auto_decompress", "name": "auto_decompress", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auto_decompress of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.auto_decompress", "name": "auto_decompress", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auto_decompress of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client.ClientSession.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of ClientSession", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.closed", "name": "closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "closed of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.closed", "name": "closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "closed of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "connector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.connector", "name": "connector", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connector of ClientSession", "ret_type": {".class": "UnionType", "items": ["aiohttp.connector.BaseConnector", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.connector", "name": "connector", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connector of ClientSession", "ret_type": {".class": "UnionType", "items": ["aiohttp.connector.BaseConnector", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "connector_owner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.connector_owner", "name": "connector_owner", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connector_owner of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.connector_owner", "name": "connector_owner", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connector_owner of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cookie_jar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.cookie_jar", "name": "cookie_jar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cookie_jar of ClientSession", "ret_type": "aiohttp.abc.AbstractCookieJar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.cookie_jar", "name": "cookie_jar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cookie_jar of ClientSession", "ret_type": "aiohttp.abc.AbstractCookieJar", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "aiohttp.client.ClientSession.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypedDictType", "fallback": "aiohttp.client._RequestOptions", "items": [["params", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}], ["data", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["json", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cookies", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["skip_auto_headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["allow_redirects", "builtins.bool"], ["max_redirects", "builtins.int"], ["compress", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["chunked", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["expect100", "builtins.bool"], ["raise_for_status", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["read_until_eof", "builtins.bool"], ["proxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["timeout", {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["ssl", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}], ["server_hostname", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["trace_request_ctx", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["read_bufsize", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auto_decompress", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_line_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_field_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._RequestContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "detach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession.detach", "name": "detach", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach of ClientSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "aiohttp.client.ClientSession.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypedDictType", "fallback": "aiohttp.client._RequestOptions", "items": [["params", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}], ["data", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["json", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cookies", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["skip_auto_headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["allow_redirects", "builtins.bool"], ["max_redirects", "builtins.int"], ["compress", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["chunked", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["expect100", "builtins.bool"], ["raise_for_status", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["read_until_eof", "builtins.bool"], ["proxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["timeout", {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["ssl", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}], ["server_hostname", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["trace_request_ctx", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["read_bufsize", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auto_decompress", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_line_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_field_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._RequestContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "aiohttp.client.ClientSession.head", "name": "head", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypedDictType", "fallback": "aiohttp.client._RequestOptions", "items": [["params", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}], ["data", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["json", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cookies", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["skip_auto_headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["allow_redirects", "builtins.bool"], ["max_redirects", "builtins.int"], ["compress", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["chunked", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["expect100", "builtins.bool"], ["raise_for_status", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["read_until_eof", "builtins.bool"], ["proxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["timeout", {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["ssl", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}], ["server_hostname", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["trace_request_ctx", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["read_bufsize", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auto_decompress", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_line_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_field_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "head of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._RequestContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers of ClientSession", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers of ClientSession", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "json_serialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.json_serialize", "name": "json_serialize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json_serialize of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.JSONEncoder"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.json_serialize", "name": "json_serialize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json_serialize of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.JSONEncoder"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.loop", "name": "loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loop of ClientSession", "ret_type": "asyncio.events.AbstractEventLoop", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.loop", "name": "loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loop of ClientSession", "ret_type": "asyncio.events.AbstractEventLoop", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "aiohttp.client.ClientSession.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypedDictType", "fallback": "aiohttp.client._RequestOptions", "items": [["params", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}], ["data", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["json", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cookies", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["skip_auto_headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["allow_redirects", "builtins.bool"], ["max_redirects", "builtins.int"], ["compress", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["chunked", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["expect100", "builtins.bool"], ["raise_for_status", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["read_until_eof", "builtins.bool"], ["proxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["timeout", {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["ssl", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}], ["server_hostname", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["trace_request_ctx", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["read_bufsize", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auto_decompress", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_line_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_field_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._RequestContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "aiohttp.client.ClientSession.patch", "name": "patch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypedDictType", "fallback": "aiohttp.client._RequestOptions", "items": [["params", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}], ["data", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["json", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cookies", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["skip_auto_headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["allow_redirects", "builtins.bool"], ["max_redirects", "builtins.int"], ["compress", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["chunked", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["expect100", "builtins.bool"], ["raise_for_status", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["read_until_eof", "builtins.bool"], ["proxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["timeout", {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["ssl", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}], ["server_hostname", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["trace_request_ctx", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["read_bufsize", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auto_decompress", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_line_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_field_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._RequestContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "aiohttp.client.ClientSession.post", "name": "post", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypedDictType", "fallback": "aiohttp.client._RequestOptions", "items": [["params", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}], ["data", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["json", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cookies", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["skip_auto_headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["allow_redirects", "builtins.bool"], ["max_redirects", "builtins.int"], ["compress", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["chunked", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["expect100", "builtins.bool"], ["raise_for_status", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["read_until_eof", "builtins.bool"], ["proxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["timeout", {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["ssl", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}], ["server_hostname", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["trace_request_ctx", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["read_bufsize", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auto_decompress", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_line_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_field_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "post of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._RequestContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "aiohttp.client.ClientSession.put", "name": "put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypedDictType", "fallback": "aiohttp.client._RequestOptions", "items": [["params", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}], ["data", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["json", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cookies", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["skip_auto_headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["allow_redirects", "builtins.bool"], ["max_redirects", "builtins.int"], ["compress", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["chunked", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["expect100", "builtins.bool"], ["raise_for_status", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["read_until_eof", "builtins.bool"], ["proxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["timeout", {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["ssl", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}], ["server_hostname", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["trace_request_ctx", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["read_bufsize", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auto_decompress", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_line_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_field_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._RequestContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "raise_for_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.raise_for_status", "name": "raise_for_status", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raise_for_status of ClientSession", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.raise_for_status", "name": "raise_for_status", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raise_for_status of ClientSession", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "method", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "aiohttp.client.ClientSession.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "method", "url", "kwargs"], "arg_types": ["aiohttp.client.ClientSession", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypedDictType", "fallback": "aiohttp.client._RequestOptions", "items": [["params", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}], ["data", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["json", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cookies", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["skip_auto_headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["allow_redirects", "builtins.bool"], ["max_redirects", "builtins.int"], ["compress", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["chunked", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["expect100", "builtins.bool"], ["raise_for_status", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["read_until_eof", "builtins.bool"], ["proxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["timeout", {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["ssl", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}], ["server_hostname", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["trace_request_ctx", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["read_bufsize", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auto_decompress", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_line_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_field_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._RequestContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "requote_redirect_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "aiohttp.client.ClientSession.requote_redirect_url", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "aiohttp.client.ClientSession.requote_redirect_url", "name": "requote_redirect_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "requote_redirect_url of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.requote_redirect_url", "name": "requote_redirect_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "requote_redirect_url of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.client.ClientSession.requote_redirect_url", "name": "requote_redirect_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["aiohttp.client.ClientSession", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "requote_redirect_url of ClientSession", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "requote_redirect_url", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "requote_redirect_url of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "skip_auto_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.skip_auto_headers", "name": "skip_auto_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skip_auto_headers of ClientSession", "ret_type": {".class": "Instance", "args": ["multidict.istr"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.skip_auto_headers", "name": "skip_auto_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skip_auto_headers of ClientSession", "ret_type": {".class": "Instance", "args": ["multidict.istr"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout of ClientSession", "ret_type": "aiohttp.client.ClientTimeout", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout of ClientSession", "ret_type": "aiohttp.client.ClientTimeout", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "trace_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.trace_configs", "name": "trace_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trace_configs of ClientSession", "ret_type": {".class": "Instance", "args": ["aiohttp.tracing.TraceConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.trace_configs", "name": "trace_configs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trace_configs of ClientSession", "ret_type": {".class": "Instance", "args": ["aiohttp.tracing.TraceConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "trust_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.trust_env", "name": "trust_env", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trust_env of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.trust_env", "name": "trust_env", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trust_env of ClientSession", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client.ClientSession.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of ClientSession", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client.ClientSession.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of ClientSession", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ws_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "url", "method", "protocols", "timeout", "receive_timeout", "autoclose", "autoping", "heartbeat", "auth", "origin", "params", "headers", "proxy", "proxy_auth", "ssl", "verify_ssl", "fingerprint", "ssl_context", "server_hostname", "proxy_headers", "compress", "max_msg_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientSession.ws_connect", "name": "ws_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "url", "method", "protocols", "timeout", "receive_timeout", "autoclose", "autoping", "heartbeat", "auth", "origin", "params", "headers", "proxy", "proxy_auth", "ssl", "verify_ssl", "fingerprint", "ssl_context", "server_hostname", "proxy_headers", "compress", "max_msg_size"], "arg_types": ["aiohttp.client.ClientSession", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["aiohttp.client_ws.ClientWSTimeout", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ws_connect of ClientSession", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.client._WSRequestContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientSession.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.client.ClientSession", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.client.ClientTimeout", "name": "ClientTimeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 198, "converter_init_type": null, "has_converter": false, "has_default": true, "init": true, "init_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "kw_only": false, "name": "total"}, {"alias": null, "context_column": 4, "context_line": 199, "converter_init_type": null, "has_converter": false, "has_default": true, "init": true, "init_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "kw_only": false, "name": "connect"}, {"alias": null, "context_column": 4, "context_line": 200, "converter_init_type": null, "has_converter": false, "has_default": true, "init": true, "init_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "kw_only": false, "name": "sock_read"}, {"alias": null, "context_column": 4, "context_line": 201, "converter_init_type": null, "has_converter": false, "has_default": true, "init": true, "init_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "kw_only": false, "name": "sock_connect"}, {"alias": null, "context_column": 4, "context_line": 202, "converter_init_type": null, "has_converter": false, "has_default": true, "init": true, "init_type": "builtins.float", "kw_only": false, "name": "ceil_threshold"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.client", "mro": ["aiohttp.client.ClientTimeout", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_client_ClientTimeout_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.client.ClientTimeout.__aiohttp_client_ClientTimeout_AttrsAttributes__", "name": "__aiohttp_client_ClientTimeout_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientTimeout.__aiohttp_client_ClientTimeout_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.client", "mro": ["aiohttp.client.ClientTimeout.__aiohttp_client_ClientTimeout_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "ceil_threshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ceil_threshold", "name": "ceil_threshold", "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "connect", "name": "connect", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "sock_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sock_connect", "name": "sock_connect", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "sock_read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sock_read", "name": "sock_read", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "total": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "total", "name": "total", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.client.ClientTimeout.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.client.ClientTimeout.__aiohttp_client_ClientTimeout_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientTimeout.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of ClientTimeout", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientTimeout.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of ClientTimeout", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "total", "connect", "sock_read", "sock_connect", "ceil_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientTimeout.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "total", "connect", "sock_read", "sock_connect", "ceil_threshold"], "arg_types": ["aiohttp.client.ClientTimeout", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientTimeout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientTimeout.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of ClientTimeout", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.ClientTimeout.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of ClientTimeout", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client.ClientTimeout.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.client.ClientTimeout.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "total"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "connect"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sock_read"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sock_connect"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ceil_threshold"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.client.ClientTimeout.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "ceil_threshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready", "has_explicit_value"], "fullname": "aiohttp.client.ClientTimeout.ceil_threshold", "name": "ceil_threshold", "type": "builtins.float"}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready", "has_explicit_value"], "fullname": "aiohttp.client.ClientTimeout.connect", "name": "connect", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sock_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready", "has_explicit_value"], "fullname": "aiohttp.client.ClientTimeout.sock_connect", "name": "sock_connect", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sock_read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready", "has_explicit_value"], "fullname": "aiohttp.client.ClientTimeout.sock_read", "name": "sock_read", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "total": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready", "has_explicit_value"], "fullname": "aiohttp.client.ClientTimeout.total", "name": "total", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client.ClientTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.client.ClientTimeout", "values": [], "variance": 0}, "slots": ["ceil_threshold", "connect", "sock_connect", "sock_read", "total"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientWSTimeout": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_ws.ClientWSTimeout", "kind": "Gdef"}, "ClientWebSocketResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_ws.ClientWebSocketResponse", "kind": "Gdef"}, "ConnectionTimeoutError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ConnectionTimeoutError", "kind": "Gdef"}, "ContentTypeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ContentTypeError", "kind": "Gdef"}, "CookieJar": {".class": "SymbolTableNode", "cross_ref": "aiohttp.cookiejar.CookieJar", "kind": "Gdef", "module_public": false}, "Coroutine": {".class": "SymbolTableNode", "cross_ref": "typing.Coroutine", "kind": "Gdef", "module_public": false}, "DEBUG": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.DEBUG", "kind": "Gdef", "module_public": false}, "DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.client.DEFAULT_TIMEOUT", "name": "DEFAULT_TIMEOUT", "type": "aiohttp.client.ClientTimeout"}}, "DEFAULT_WS_CLIENT_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_ws.DEFAULT_WS_CLIENT_TIMEOUT", "kind": "Gdef", "module_public": false}, "EMPTY_BODY_METHODS": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.EMPTY_BODY_METHODS", "kind": "Gdef", "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_public": false}, "Fingerprint": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.Fingerprint", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef", "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_public": false}, "HTTP_AND_EMPTY_SCHEMA_SET": {".class": "SymbolTableNode", "cross_ref": "aiohttp.connector.HTTP_AND_EMPTY_SCHEMA_SET", "kind": "Gdef", "module_public": false}, "HttpVersion": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion", "kind": "Gdef", "module_public": false}, "IDEMPOTENT_METHODS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.client.IDEMPOTENT_METHODS", "name": "IDEMPOTENT_METHODS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "InvalidURL": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.InvalidURL", "kind": "Gdef"}, "InvalidUrlClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.InvalidUrlClientError", "kind": "Gdef"}, "InvalidUrlRedirectClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.InvalidUrlRedirectClientError", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "JSONEncoder": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.JSONEncoder", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "LooseCookies": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.LooseCookies", "kind": "Gdef", "module_public": false}, "LooseHeaders": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.LooseHeaders", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MultiDict": {".class": "SymbolTableNode", "cross_ref": "multidict.MultiDict", "kind": "Gdef", "module_public": false}, "MultiDictProxy": {".class": "SymbolTableNode", "cross_ref": "multidict.MultiDictProxy", "kind": "Gdef", "module_public": false}, "NamedPipeConnector": {".class": "SymbolTableNode", "cross_ref": "aiohttp.connector.NamedPipeConnector", "kind": "Gdef"}, "NonHttpUrlClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.NonHttpUrlClientError", "kind": "Gdef"}, "NonHttpUrlRedirectClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.NonHttpUrlRedirectClientError", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Query": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.Query", "kind": "Gdef", "module_public": false}, "RedirectClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.RedirectClientError", "kind": "Gdef"}, "RequestInfo": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.RequestInfo", "kind": "Gdef"}, "SSLContext": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLContext", "kind": "Gdef", "module_public": false}, "ServerConnectionError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ServerConnectionError", "kind": "Gdef"}, "ServerDisconnectedError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ServerDisconnectedError", "kind": "Gdef"}, "ServerFingerprintMismatch": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ServerFingerprintMismatch", "kind": "Gdef"}, "ServerTimeoutError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ServerTimeoutError", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "SocketTimeoutError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.SocketTimeoutError", "kind": "Gdef"}, "StrOrURL": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.StrOrURL", "kind": "Gdef", "module_public": false}, "TCPConnector": {".class": "SymbolTableNode", "cross_ref": "aiohttp.connector.TCPConnector", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TimeoutHandle": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.TimeoutHandle", "kind": "Gdef", "module_public": false}, "TooManyRedirects": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.TooManyRedirects", "kind": "Gdef"}, "Trace": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.Trace", "kind": "Gdef", "module_public": false}, "TraceConfig": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceConfig", "kind": "Gdef", "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "URL": {".class": "SymbolTableNode", "cross_ref": "yarl._url.URL", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnixConnector": {".class": "SymbolTableNode", "cross_ref": "aiohttp.connector.UnixConnector", "kind": "Gdef"}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_public": false}, "WSHandshakeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSHandshakeError", "kind": "Gdef", "module_public": false}, "WSMessageTypeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.WSMessageTypeError", "kind": "Gdef"}, "WSServerHandshakeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.WSServerHandshakeError", "kind": "Gdef"}, "WS_KEY": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.WS_KEY", "kind": "Gdef", "module_public": false}, "WebSocketDataQueue": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.reader.WebSocketDataQueue", "kind": "Gdef", "module_public": false}, "WebSocketReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.reader.WebSocketReader", "kind": "Gdef", "module_public": false}, "WebSocketWriter": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.writer.WebSocketWriter", "kind": "Gdef", "module_public": false}, "_BaseRequestContextManager": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.client._BaseRequestContextManager", "name": "_BaseRequestContextManager", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.client._BaseRequestContextManager", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.client", "mro": ["aiohttp.client._BaseRequestContextManager", "typing.Coroutine", "typing.Awaitable", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client._BaseRequestContextManager.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of _BaseRequestContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client._BaseRequestContextManager.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "tb"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of _BaseRequestContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client._BaseRequestContextManager.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__await__ of _BaseRequestContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "coro"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client._BaseRequestContextManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "coro"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _BaseRequestContextManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client._BaseRequestContextManager.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of _BaseRequestContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "aiohttp.client._BaseRequestContextManager.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_coro": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client._BaseRequestContextManager._coro", "name": "_coro", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}}}, "_resp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client._BaseRequestContextManager._resp", "name": "_resp", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client._BaseRequestContextManager.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of _BaseRequestContextManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client._BaseRequestContextManager.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of _BaseRequestContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "throw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client._BaseRequestContextManager.throw", "name": "throw", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throw of _BaseRequestContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._BaseRequestContextManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "id": 1, "name": "_RetType", "namespace": "aiohttp.client._BaseRequestContextManager", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_RetType"], "typeddict_type": null}}, "_CharsetResolver": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.client._CharsetResolver", "line": 225, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.client_reqrep.ClientResponse", "builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_RequestContextManager": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.client._RequestContextManager", "line": 1437, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["aiohttp.client_reqrep.ClientResponse"], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}}}, "_RequestOptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.client._RequestOptions", "name": "_RequestOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.client._RequestOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.client", "mro": ["aiohttp.client._RequestOptions", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["params", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}], ["data", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["json", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cookies", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["skip_auto_headers", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["allow_redirects", "builtins.bool"], ["max_redirects", "builtins.int"], ["compress", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["chunked", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["expect100", "builtins.bool"], ["raise_for_status", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bool", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["read_until_eof", "builtins.bool"], ["proxy", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_auth", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["timeout", {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "aiohttp.helpers._SENTINEL", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["ssl", {".class": "UnionType", "items": ["ssl.SSLContext", "builtins.bool", "aiohttp.client_reqrep.Fingerprint"], "uses_pep604_syntax": false}], ["server_hostname", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["proxy_headers", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["trace_request_ctx", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["read_bufsize", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["auto_decompress", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_line_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_field_size", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}}}, "_RetType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._RetType", "name": "_RetType", "upper_bound": "builtins.object", "values": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWebSocketResponse"], "variance": 0}}, "_SENTINEL": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers._SENTINEL", "kind": "Gdef", "module_public": false}, "_SessionRequestContextManager": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.client._SessionRequestContextManager", "name": "_SessionRequestContextManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.client._SessionRequestContextManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.client", "mro": ["aiohttp.client._SessionRequestContextManager", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client._SessionRequestContextManager.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client._SessionRequestContextManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of _SessionRequestContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "aiohttp.client_reqrep.ClientResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client._SessionRequestContextManager.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "tb"], "arg_types": ["aiohttp.client._SessionRequestContextManager", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of _SessionRequestContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "coro", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client._SessionRequestContextManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "coro", "session"], "arg_types": ["aiohttp.client._SessionRequestContextManager", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "NoneType"}, "aiohttp.client_reqrep.ClientResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "aiohttp.client.ClientSession"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _SessionRequestContextManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "aiohttp.client._SessionRequestContextManager.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_coro": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client._SessionRequestContextManager._coro", "name": "_coro", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "NoneType"}, "aiohttp.client_reqrep.ClientResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}}}, "_resp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client._SessionRequestContextManager._resp", "name": "_resp", "type": {".class": "UnionType", "items": ["aiohttp.client_reqrep.ClientResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client._SessionRequestContextManager._session", "name": "_session", "type": "aiohttp.client.ClientSession"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client._SessionRequestContextManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.client._SessionRequestContextManager", "values": [], "variance": 0}, "slots": ["_coro", "_resp", "_session"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WSRequestContextManager": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.client._WSRequestContextManager", "line": 1438, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["aiohttp.client_ws.ClientWebSocketResponse"], "extra_attrs": null, "type_ref": "aiohttp.client._BaseRequestContextManager"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.client.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_merge_ssl_params": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep._merge_ssl_params", "kind": "Gdef", "module_public": false}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_public": false}, "attr": {".class": "SymbolTableNode", "cross_ref": "attr", "kind": "Gdef", "module_public": false}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef", "module_public": false}, "get_env_proxy_for_url": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.get_env_proxy_for_url", "kind": "Gdef", "module_public": false}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "hdrs": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs", "kind": "Gdef", "module_public": false}, "http": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http", "kind": "Gdef", "module_public": false}, "istr": {".class": "SymbolTableNode", "cross_ref": "multidict.istr", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "payload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload", "kind": "Gdef", "module_public": false}, "request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "params", "data", "json", "headers", "skip_auto_headers", "auth", "allow_redirects", "max_redirects", "compress", "chunked", "expect100", "raise_for_status", "read_until_eof", "proxy", "proxy_auth", "timeout", "cookies", "version", "connector", "read_bufsize", "loop", "max_line_size", "max_field_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "params", "data", "json", "headers", "skip_auto_headers", "auth", "allow_redirects", "max_redirects", "compress", "chunked", "expect100", "raise_for_status", "read_until_eof", "proxy", "proxy_auth", "timeout", "cookies", "version", "connector", "read_bufsize", "loop", "max_line_size", "max_field_size"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Query"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.helpers.BasicAuth"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["aiohttp.client.ClientTimeout", "builtins.object"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "UnionType", "items": ["aiohttp.connector.BaseConnector", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request", "ret_type": "aiohttp.client._SessionRequestContextManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sentinel": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.sentinel", "kind": "Gdef", "module_public": false}, "strip_auth_from_url": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.strip_auth_from_url", "kind": "Gdef", "module_public": false}, "suppress": {".class": "SymbolTableNode", "cross_ref": "contextlib.suppress", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}, "ws_ext_gen": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.ws_ext_gen", "kind": "Gdef", "module_public": false}, "ws_ext_parse": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.ws_ext_parse", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\client.py"}