"""Load balancing strategies for LLM Proxy Server"""

import asyncio
import random
import time
from typing import Dict, List, Optional
from collections import defaultdict
import structlog

logger = structlog.get_logger()


class LoadBalancer:
    """Load balancer with multiple strategies"""
    
    def __init__(self, strategy: str = "least_connections"):
        self.strategy = strategy
        self.host_connections: Dict[str, int] = defaultdict(int)
        self.host_weights: Dict[str, float] = defaultdict(lambda: 1.0)
        self.host_health: Dict[str, bool] = defaultdict(lambda: True)
        self.last_selected: Dict[str, str] = {}  # model -> last selected host
        self.round_robin_index: Dict[str, int] = defaultdict(int)
        
        logger.info("Load balancer initialized", strategy=strategy)
    
    async def select_host(self, model: str, available_hosts: List[str]) -> str:
        """Select the best host for a model based on the configured strategy"""
        if not available_hosts:
            raise ValueError("No available hosts for model")
        
        # Filter out unhealthy hosts
        healthy_hosts = [host for host in available_hosts if self.host_health.get(host, True)]
        
        if not healthy_hosts:
            logger.warning("No healthy hosts available, using all hosts", model=model)
            healthy_hosts = available_hosts
        
        if len(healthy_hosts) == 1:
            return healthy_hosts[0]
        
        if self.strategy == "round_robin":
            return self._round_robin_select(model, healthy_hosts)
        elif self.strategy == "least_connections":
            return self._least_connections_select(healthy_hosts)
        elif self.strategy == "weighted_random":
            return self._weighted_random_select(healthy_hosts)
        elif self.strategy == "random":
            return self._random_select(healthy_hosts)
        else:
            logger.warning("Unknown strategy, falling back to round_robin", strategy=self.strategy)
            return self._round_robin_select(model, healthy_hosts)
    
    def _round_robin_select(self, model: str, hosts: List[str]) -> str:
        """Round-robin selection"""
        index = self.round_robin_index[model] % len(hosts)
        self.round_robin_index[model] = (index + 1) % len(hosts)
        selected = hosts[index]
        
        logger.debug("Round-robin selection", model=model, selected=selected, index=index)
        return selected
    
    def _least_connections_select(self, hosts: List[str]) -> str:
        """Select host with least active connections"""
        min_connections = min(self.host_connections.get(host, 0) for host in hosts)
        candidates = [host for host in hosts if self.host_connections.get(host, 0) == min_connections]
        
        # If multiple hosts have the same connection count, pick randomly
        selected = random.choice(candidates)
        
        logger.debug("Least connections selection", 
                    selected=selected, 
                    connections=self.host_connections.get(selected, 0),
                    candidates=len(candidates))
        return selected
    
    def _weighted_random_select(self, hosts: List[str]) -> str:
        """Weighted random selection based on host weights"""
        weights = [self.host_weights.get(host, 1.0) for host in hosts]
        total_weight = sum(weights)
        
        if total_weight == 0:
            return random.choice(hosts)
        
        # Normalize weights
        normalized_weights = [w / total_weight for w in weights]
        
        # Select based on weights
        r = random.random()
        cumulative = 0
        for i, weight in enumerate(normalized_weights):
            cumulative += weight
            if r <= cumulative:
                selected = hosts[i]
                logger.debug("Weighted random selection", 
                           selected=selected, 
                           weight=self.host_weights.get(selected, 1.0))
                return selected
        
        # Fallback
        return hosts[-1]
    
    def _random_select(self, hosts: List[str]) -> str:
        """Pure random selection"""
        selected = random.choice(hosts)
        logger.debug("Random selection", selected=selected)
        return selected
    
    def increment_connections(self, host: str):
        """Increment connection count for a host"""
        self.host_connections[host] += 1
        logger.debug("Connection incremented", host=host, connections=self.host_connections[host])
    
    def decrement_connections(self, host: str):
        """Decrement connection count for a host"""
        if self.host_connections[host] > 0:
            self.host_connections[host] -= 1
        logger.debug("Connection decremented", host=host, connections=self.host_connections[host])
    
    def set_host_weight(self, host: str, weight: float):
        """Set weight for a host (used in weighted strategies)"""
        self.host_weights[host] = weight
        logger.info("Host weight updated", host=host, weight=weight)
    
    def set_host_health(self, host: str, healthy: bool):
        """Set health status for a host"""
        old_status = self.host_health.get(host, True)
        self.host_health[host] = healthy
        
        if old_status != healthy:
            logger.info("Host health changed", host=host, healthy=healthy)
    
    def get_host_stats(self) -> Dict[str, Dict]:
        """Get statistics for all hosts"""
        return {
            host: {
                'connections': self.host_connections.get(host, 0),
                'weight': self.host_weights.get(host, 1.0),
                'healthy': self.host_health.get(host, True)
            }
            for host in set(list(self.host_connections.keys()) + 
                           list(self.host_weights.keys()) + 
                           list(self.host_health.keys()))
        }
    
    def reset_stats(self):
        """Reset all statistics"""
        self.host_connections.clear()
        self.round_robin_index.clear()
        logger.info("Load balancer stats reset")
