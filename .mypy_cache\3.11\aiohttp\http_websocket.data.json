{".class": "MypyFile", "_fullname": "aiohttp.http_websocket", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "WSCloseCode": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSCloseCode", "kind": "Gdef"}, "WSHandshakeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSHandshakeError", "kind": "Gdef"}, "WSMessage": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMessage", "kind": "Gdef"}, "WSMsgType": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMsgType", "kind": "Gdef"}, "WS_CLOSED_MESSAGE": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WS_CLOSED_MESSAGE", "kind": "Gdef"}, "WS_CLOSING_MESSAGE": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WS_CLOSING_MESSAGE", "kind": "Gdef"}, "WS_KEY": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.WS_KEY", "kind": "Gdef"}, "WebSocketError": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WebSocketError", "kind": "Gdef"}, "WebSocketReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.reader.WebSocketReader", "kind": "Gdef"}, "WebSocketWriter": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.writer.WebSocketWriter", "kind": "Gdef"}, "_INTERNAL_RECEIVE_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_websocket._INTERNAL_RECEIVE_TYPES", "name": "_INTERNAL_RECEIVE_TYPES", "type": {".class": "Instance", "args": ["aiohttp._websocket.models.WSMsgType"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_websocket.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_websocket.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_websocket.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_websocket.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_websocket.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_websocket.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_websocket.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "ws_ext_gen": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.ws_ext_gen", "kind": "Gdef"}, "ws_ext_parse": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.ws_ext_parse", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\http_websocket.py"}