{".class": "MypyFile", "_fullname": "opentelemetry.util._providers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MeterProvider": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.MeterProvider", "kind": "Gdef"}, "Provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.util._providers.Provider", "name": "Provider", "upper_bound": "builtins.object", "values": ["opentelemetry.trace.TracerProvider", "opentelemetry.metrics._internal.MeterProvider"], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TracerProvider": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.TracerProvider", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._providers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._providers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._providers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._providers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._providers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._providers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_load_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["provider_environment_variable", "provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.util._providers._load_provider", "name": "_load_provider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["provider_environment_variable", "provider"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_provider", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.util._providers.Provider", "id": -1, "name": "Provider", "namespace": "opentelemetry.util._providers._load_provider", "upper_bound": "builtins.object", "values": ["opentelemetry.trace.TracerProvider", "opentelemetry.metrics._internal.MeterProvider"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.util._providers.Provider", "id": -1, "name": "Provider", "namespace": "opentelemetry.util._providers._load_provider", "upper_bound": "builtins.object", "values": ["opentelemetry.trace.TracerProvider", "opentelemetry.metrics._internal.MeterProvider"], "variance": 0}]}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "entry_points": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.entry_points", "kind": "Gdef"}, "environ": {".class": "SymbolTableNode", "cross_ref": "os.environ", "kind": "Gdef"}, "getLogger": {".class": "SymbolTableNode", "cross_ref": "logging.getLogger", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.util._providers.logger", "name": "logger", "type": "logging.Logger"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\util\\_providers.py"}