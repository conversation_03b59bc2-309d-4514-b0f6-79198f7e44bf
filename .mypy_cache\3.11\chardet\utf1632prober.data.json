{".class": "MypyFile", "_fullname": "chardet.utf1632prober", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CharSetProber": {".class": "SymbolTableNode", "cross_ref": "chardet.charsetprober.CharSetProber", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ProbingState": {".class": "SymbolTableNode", "cross_ref": "chardet.enums.ProbingState", "kind": "Gdef"}, "UTF1632Prober": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.charsetprober.CharSetProber"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.utf1632prober.UTF1632Prober", "name": "UTF1632Prober", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.utf1632prober", "mro": ["chardet.utf1632prober.UTF1632Prober", "chardet.charsetprober.CharSetProber", "builtins.object"], "names": {".class": "SymbolTable", "EXPECTED_RATIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "chardet.utf1632prober.UTF1632Prober.EXPECTED_RATIO", "name": "EXPECTED_RATIO", "type": "builtins.float"}}, "MIN_CHARS_FOR_DETECTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "chardet.utf1632prober.UTF1632Prober.MIN_CHARS_FOR_DETECTION", "name": "MIN_CHARS_FOR_DETECTION", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UTF1632Prober", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "approx_16bit_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.approx_16bit_chars", "name": "approx_16bit_chars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "approx_16bit_chars of UTF1632Prober", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "approx_32bit_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.approx_32bit_chars", "name": "approx_32bit_chars", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "approx_32bit_chars of UTF1632Prober", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "charset_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "chardet.utf1632prober.UTF1632Prober.charset_name", "name": "charset_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "charset_name of UTF1632Prober", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.charset_name", "name": "charset_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "charset_name of UTF1632Prober", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "arg_types": ["chardet.utf1632prober.UTF1632Prober", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of UTF1632Prober", "ret_type": "chardet.enums.ProbingState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "first_half_surrogate_pair_detected_16be": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.first_half_surrogate_pair_detected_16be", "name": "first_half_surrogate_pair_detected_16be", "type": "builtins.bool"}}, "first_half_surrogate_pair_detected_16le": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.first_half_surrogate_pair_detected_16le", "name": "first_half_surrogate_pair_detected_16le", "type": "builtins.bool"}}, "get_confidence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.get_confidence", "name": "get_confidence", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_confidence of UTF1632Prober", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invalid_utf16be": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.invalid_utf16be", "name": "invalid_utf16be", "type": "builtins.bool"}}, "invalid_utf16le": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.invalid_utf16le", "name": "invalid_utf16le", "type": "builtins.bool"}}, "invalid_utf32be": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.invalid_utf32be", "name": "invalid_utf32be", "type": "builtins.bool"}}, "invalid_utf32le": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.invalid_utf32le", "name": "invalid_utf32le", "type": "builtins.bool"}}, "is_likely_utf16be": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.is_likely_utf16be", "name": "is_likely_utf16be", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_likely_utf16be of UTF1632Prober", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_likely_utf16le": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.is_likely_utf16le", "name": "is_likely_utf16le", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_likely_utf16le of UTF1632Prober", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_likely_utf32be": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.is_likely_utf32be", "name": "is_likely_utf32be", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_likely_utf32be of UTF1632Prober", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_likely_utf32le": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.is_likely_utf32le", "name": "is_likely_utf32le", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_likely_utf32le of UTF1632Prober", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "chardet.utf1632prober.UTF1632Prober.language", "name": "language", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "language of UTF1632Prober", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.language", "name": "language", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "language of UTF1632Prober", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nonzeros_at_mod": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.nonzeros_at_mod", "name": "nonzeros_at_mod", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.position", "name": "position", "type": "builtins.int"}}, "quad": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.quad", "name": "quad", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of UTF1632Prober", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "chardet.utf1632prober.UTF1632Prober.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of UTF1632Prober", "ret_type": "chardet.enums.ProbingState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.utf1632prober.UTF1632Prober"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state of UTF1632Prober", "ret_type": "chardet.enums.ProbingState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_utf16_characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pair"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.validate_utf16_characters", "name": "validate_utf16_characters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pair"], "arg_types": ["chardet.utf1632prober.UTF1632Prober", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_utf16_characters of UTF1632Prober", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_utf32_characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "quad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.utf1632prober.UTF1632Prober.validate_utf32_characters", "name": "validate_utf32_characters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "quad"], "arg_types": ["chardet.utf1632prober.UTF1632Prober", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_utf32_characters of UTF1632Prober", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "zeros_at_mod": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.utf1632prober.UTF1632Prober.zeros_at_mod", "name": "zeros_at_mod", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.utf1632prober.UTF1632Prober.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.utf1632prober.UTF1632Prober", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.utf1632prober.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.utf1632prober.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.utf1632prober.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.utf1632prober.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.utf1632prober.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.utf1632prober.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\chardet\\utf1632prober.py"}