{"data_mtime": 1753366013, "dep_lines": [18, 15, 16, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 30, 30], "dependencies": ["opentelemetry.context.context", "abc", "typing", "builtins", "_frozen_importlib", "opentelemetry.context"], "hash": "c03515614fbf544c3455a0c40438bc362eb06db7", "id": "opentelemetry.propagators.textmap", "ignore_all": true, "interface_hash": "8f18e93e571bb10d01249d8dab0fec26103d7e56", "mtime": 1750258719, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\propagators\\textmap.py", "plugin_data": null, "size": 6642, "suppressed": [], "version_id": "1.15.0"}