{".class": "MypyFile", "_fullname": "azure.core.utils._pipeline_transport_rest_shared", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "BytesIOSocket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.utils._pipeline_transport_rest_shared.BytesIOSocket", "name": "BytesIOSocket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared.BytesIOSocket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.utils._pipeline_transport_rest_shared", "mro": ["azure.core.utils._pipeline_transport_rest_shared.BytesIOSocket", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bytes_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared.BytesIOSocket.__init__", "name": "__init__", "type": null}}, "bytes_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.utils._pipeline_transport_rest_shared.BytesIOSocket.bytes_data", "name": "bytes_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "makefile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared.BytesIOSocket.makefile", "name": "makefile", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.utils._pipeline_transport_rest_shared.BytesIOSocket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.utils._pipeline_transport_rest_shared.BytesIOSocket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "FileContent": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._helpers.FileContent", "kind": "Gdef"}, "FileType": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._helpers.FileType", "kind": "Gdef"}, "FilesType": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._helpers.FilesType", "kind": "Gdef"}, "HTTP": {".class": "SymbolTableNode", "cross_ref": "email.policy.HTTP", "kind": "Gdef"}, "HTTPConnection": {".class": "SymbolTableNode", "cross_ref": "http.client.HTTPConnection", "kind": "Gdef"}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "azure.core.utils._pipeline_transport_rest_shared.HTTPRequestType", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "uses_pep604_syntax": false}}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Message": {".class": "SymbolTableNode", "cross_ref": "email.message.Message", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PipelineContext": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineContext", "kind": "Gdef"}, "PipelineRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineRequest", "kind": "Gdef"}, "PipelineResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineResponse", "kind": "Gdef"}, "PipelineTransportAioHttpTransportResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "azure.core.pipeline.transport.AioHttpTransportResponse", "name": "PipelineTransportAioHttpTransportResponse", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "PipelineTransportHttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "PipelineTransportHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpResponse", "kind": "Gdef"}, "PipelineTransportHttpResponseBase": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base._HttpResponseBase", "kind": "Gdef"}, "RestHttpRequestPy3": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpRequest", "kind": "Gdef"}, "SansIOHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_HTTPSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.utils._pipeline_transport_rest_shared._HTTPSerializer", "name": "_HTTPSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._HTTPSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.utils._pipeline_transport_rest_shared", "mro": ["azure.core.utils._pipeline_transport_rest_shared._HTTPSerializer", "http.client.HTTPConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._HTTPSerializer.__init__", "name": "__init__", "type": null}}, "buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.utils._pipeline_transport_rest_shared._HTTPSerializer.buffer", "name": "buffer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "putheader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "header", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._HTTPSerializer.putheader", "name": "putheader", "type": null}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._HTTPSerializer.send", "name": "send", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.utils._pipeline_transport_rest_shared._HTTPSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.utils._pipeline_transport_rest_shared._HTTPSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.utils._pipeline_transport_rest_shared.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.utils._pipeline_transport_rest_shared.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.utils._pipeline_transport_rest_shared.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.utils._pipeline_transport_rest_shared.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.utils._pipeline_transport_rest_shared.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.utils._pipeline_transport_rest_shared.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_aiohttp_body_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._aiohttp_body_helper", "name": "_aiohttp_body_helper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_aiohttp_body_helper", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_await_result": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._tools.await_result", "kind": "Gdef"}, "_decode_parts_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["response", "message", "http_response_type", "requests", "deserialize_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._decode_parts_helper", "name": "_decode_parts_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["response", "message", "http_response_type", "requests", "deserialize_response"], "arg_types": ["azure.core.pipeline.transport._base._HttpResponseBase", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "TypeType", "item": "azure.core.pipeline.transport._base._HttpResponseBase"}, {".class": "Instance", "args": ["azure.core.pipeline.transport._base.HttpRequest"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_decode_parts_helper", "ret_type": {".class": "Instance", "args": ["azure.core.pipeline.transport._base.HttpResponse"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_data_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._format_data_helper", "name": "_format_data_helper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.rest._helpers.FileType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_data_helper", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.rest._helpers.FileContent"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_parameters_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["http_request", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._format_parameters_helper", "name": "_format_parameters_helper", "type": null}}, "_get_raw_parts_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["response", "http_response_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._get_raw_parts_helper", "name": "_get_raw_parts_helper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["response", "http_response_type"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_raw_parts_helper", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pad_attr_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["attr", "backcompat_attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._pad_attr_name", "name": "_pad_attr_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["attr", "backcompat_attrs"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pad_attr_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parts_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._parts_helper", "name": "_parts_helper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response"], "arg_types": ["azure.core.pipeline.transport._base.HttpResponse"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parts_helper", "ret_type": {".class": "Instance", "args": ["azure.core.pipeline.transport._base.HttpResponse"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_multipart_body_helper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["http_request", "content_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._prepare_multipart_body_helper", "name": "_prepare_multipart_body_helper", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["http_request", "content_index"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.utils._pipeline_transport_rest_shared.HTTPRequestType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_multipart_body_helper", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["http_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared._serialize_request", "name": "_serialize_request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["http_request"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.utils._pipeline_transport_rest_shared.HTTPRequestType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_serialize_request", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "binary_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "azure.core.utils._pipeline_transport_rest_shared.binary_type", "line": 55, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.str"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "get_file_items": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.utils._pipeline_transport_rest_shared.get_file_items", "name": "get_file_items", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["files"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.rest._helpers.FilesType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_file_items", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.rest._helpers.FileType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "message_parser": {".class": "SymbolTableNode", "cross_ref": "email.message_from_bytes", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\utils\\_pipeline_transport_rest_shared.py"}