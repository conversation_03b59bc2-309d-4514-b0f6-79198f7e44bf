# PowerShell script to activate the Python virtual environment for LLM Proxy Server
Write-Host "Activating Python virtual environment for LLM Proxy Server..." -ForegroundColor Green

# Activate the virtual environment
& ".\venv\Scripts\Activate.ps1"

Write-Host ""
Write-Host "Virtual environment activated!" -ForegroundColor Green
Write-Host ""

Write-Host "Python version:" -ForegroundColor Yellow
python --version

Write-Host ""
Write-Host "Key installed packages:" -ForegroundColor Yellow
pip show fastapi uvicorn pydantic httpx ollama | Select-String "Name:|Version:"

Write-Host ""
Write-Host "To run the LLM Proxy Server:" -ForegroundColor Cyan
Write-Host "  cd llm-proxy-server" -ForegroundColor White
Write-Host "  python -m llm_proxy_server.main" -ForegroundColor White

Write-Host ""
Write-Host "To deactivate the virtual environment, type: deactivate" -ForegroundColor Cyan
