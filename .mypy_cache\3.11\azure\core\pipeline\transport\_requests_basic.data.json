{".class": "MypyFile", "_fullname": "azure.core.pipeline.transport._requests_basic", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AzureErrorUnion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "azure.core.pipeline.transport._requests_basic.AzureErrorUnion", "line": 66, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["azure.core.exceptions.ServiceRequestError", "azure.core.exceptions.ServiceResponseError", "azure.core.exceptions.IncompleteReadError", "azure.core.exceptions.HttpResponseError"], "uses_pep604_syntax": false}}}, "BiggerBlockSizeHTTPAdapter": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._bigger_block_size_http_adapters.BiggerBlockSizeHTTPAdapter", "kind": "Gdef"}, "ConnectTimeoutError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ConnectTimeoutError", "kind": "Gdef"}, "ConnectionConfiguration": {".class": "SymbolTableNode", "cross_ref": "azure.core.configuration.ConnectionConfiguration", "kind": "Gdef"}, "CoreDecodeError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.DecodeError", "kind": "Gdef"}, "DecodeError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.DecodeError", "kind": "Gdef"}, "HttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "HttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpResponse", "kind": "Gdef"}, "HttpResponseError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.HttpResponseError", "kind": "Gdef"}, "HttpTransport": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpTransport", "kind": "Gdef"}, "IncompleteReadError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.IncompleteReadError", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NewConnectionError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.NewConnectionError", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PipelineType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._requests_basic.PipelineType", "name": "PipelineType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ProtocolError", "kind": "Gdef"}, "ReadTimeoutError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ReadTimeoutError", "kind": "Gdef"}, "RequestsTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base.HttpTransport"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport", "name": "RequestsTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.transport._requests_basic", "mro": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", "azure.core.pipeline.transport._base.HttpTransport", "typing.ContextManager", "contextlib.AbstractContextManager", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of RequestsTransport", "ret_type": "azure.core.pipeline.transport._requests_basic.RequestsTransport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RequestsTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_been_opened": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport._has_been_opened", "name": "_has_been_opened", "type": "builtins.bool"}}, "_init_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport._init_session", "name": "_init_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", "requests.sessions.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_session of RequestsTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_protocols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport._protocols", "name": "_protocols", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_session_owner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport._session_owner", "name": "_session_owner", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_use_env_settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport._use_env_settings", "name": "_use_env_settings", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.close", "name": "close", "type": null}}, "connection_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.connection_config", "name": "connection_config", "type": "azure.core.configuration.ConnectionConfiguration"}}, "open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.open", "name": "open", "type": null}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.send", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", {".class": "UnionType", "items": ["azure.core.pipeline.transport._base.HttpRequest", "azure.core.rest._rest_py3.HttpRequest"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RequestsTransport", "ret_type": {".class": "UnionType", "items": ["azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.HttpResponse"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", "azure.core.pipeline.transport._base.HttpRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RequestsTransport", "ret_type": "azure.core.pipeline.transport._base.HttpResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", "azure.core.pipeline.transport._base.HttpRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RequestsTransport", "ret_type": "azure.core.pipeline.transport._base.HttpResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", "azure.core.rest._rest_py3.HttpRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RequestsTransport", "ret_type": "azure.core.rest._rest_py3.HttpResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", "azure.core.rest._rest_py3.HttpRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RequestsTransport", "ret_type": "azure.core.rest._rest_py3.HttpResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", "azure.core.pipeline.transport._base.HttpRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RequestsTransport", "ret_type": "azure.core.pipeline.transport._base.HttpResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 4], "arg_names": ["self", "request", "proxies", "kwargs"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransport", "azure.core.rest._rest_py3.HttpRequest", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RequestsTransport", "ret_type": "azure.core.rest._rest_py3.HttpResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.session", "name": "session", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._requests_basic.RequestsTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestsTransportResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.transport._base.HttpResponse", "azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransportResponse", "name": "RequestsTransportResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransportResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.transport._requests_basic", "mro": ["azure.core.pipeline.transport._requests_basic.RequestsTransportResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase", "azure.core.pipeline.transport._base._HttpResponseBase", "builtins.object"], "names": {".class": "SymbolTable", "stream_download": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "pipeline", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransportResponse.stream_download", "name": "stream_download", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "pipeline", "kwargs"], "arg_types": ["azure.core.pipeline.transport._requests_basic.RequestsTransportResponse", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._requests_basic.PipelineType", "id": -1, "name": "PipelineType", "namespace": "azure.core.pipeline.transport._requests_basic.RequestsTransportResponse.stream_download", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_download of RequestsTransportResponse", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._requests_basic.PipelineType", "id": -1, "name": "PipelineType", "namespace": "azure.core.pipeline.transport._requests_basic.RequestsTransportResponse.stream_download", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._requests_basic.RequestsTransportResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._requests_basic.RequestsTransportResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RestHttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpRequest", "kind": "Gdef"}, "RestHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpResponse", "kind": "Gdef"}, "Retry": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.retry.Retry", "kind": "Gdef"}, "ServiceRequestError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ServiceRequestError", "kind": "Gdef"}, "ServiceResponseError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ServiceResponseError", "kind": "Gdef"}, "StreamDownloadGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator", "name": "StreamDownloadGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.transport._requests_basic", "mro": ["azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "pipeline", "response", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.__iter__", "name": "__iter__", "type": null}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.__len__", "name": "__len__", "type": null}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.__next__", "name": "__next__", "type": null}}, "block_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.block_size", "name": "block_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "content_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.content_length", "name": "content_length", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "iter_content_func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.iter_content_func", "name": "iter_content_func", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.next", "name": "next", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pipeline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.pipeline", "name": "pipeline", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.request", "name": "request", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.response", "name": "response", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._requests_basic.StreamDownloadGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_HttpResponseBase": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base._HttpResponseBase", "kind": "Gdef"}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.transport._requests_basic._LOGGER", "name": "_LOGGER", "type": "logging.Logger"}}, "_RequestsTransportResponseBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.transport._base._HttpResponseBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase", "name": "_RequestsTransportResponseBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.transport._requests_basic", "mro": ["azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase", "azure.core.pipeline.transport._base._HttpResponseBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "request", "requests_response", "block_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase.__init__", "name": "__init__", "type": null}}, "body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase.body", "name": "body", "type": null}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "encoding"], "arg_types": ["azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text of _RequestsTransportResponseBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.transport._requests_basic._RequestsTransportResponseBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._requests_basic.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._requests_basic.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._requests_basic.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._requests_basic.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._requests_basic.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.transport._requests_basic.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_handle_non_stream_rest_response": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._tools.handle_non_stream_rest_response", "kind": "Gdef"}, "_is_rest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._tools.is_rest", "kind": "Gdef"}, "_read_raw_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["response", "chunk_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.transport._requests_basic._read_raw_stream", "name": "_read_raw_stream", "type": null}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\transport\\_requests_basic.py"}