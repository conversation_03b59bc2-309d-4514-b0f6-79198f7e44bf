{".class": "MypyFile", "_fullname": "opentelemetry.propagators.textmap", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CarrierT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "name": "CarrierT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "CarrierValT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "opentelemetry.propagators.textmap.CarrierValT", "line": 22, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "uses_pep604_syntax": false}}}, "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef"}, "DefaultGetter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.propagators.textmap.CarrierValT"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.propagators.textmap.DefaultGetter", "name": "DefaultGetter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.propagators.textmap.DefaultGetter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.propagators.textmap", "mro": ["opentelemetry.propagators.textmap.DefaultGetter", "opentelemetry.propagators.textmap.Getter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "carrier", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagators.textmap.DefaultGetter.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "carrier", "key"], "arg_types": ["opentelemetry.propagators.textmap.DefaultGetter", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.propagators.textmap.CarrierValT"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of DefaultGetter", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "carrier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagators.textmap.DefaultGetter.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "carrier"], "arg_types": ["opentelemetry.propagators.textmap.DefaultGetter", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.propagators.textmap.CarrierValT"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keys of <PERSON><PERSON>ult<PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.DefaultGetter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.propagators.textmap.DefaultGetter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultSetter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.propagators.textmap.CarrierValT"}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Setter"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.propagators.textmap.DefaultSetter", "name": "DefaultSetter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.propagators.textmap.DefaultSetter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.propagators.textmap", "mro": ["opentelemetry.propagators.textmap.DefaultSetter", "opentelemetry.propagators.textmap.Setter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "carrier", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagators.textmap.DefaultSetter.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "carrier", "key", "value"], "arg_types": ["opentelemetry.propagators.textmap.DefaultSetter", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.propagators.textmap.CarrierValT"}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.propagators.textmap.CarrierValT"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of DefaultSetter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.DefaultSetter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.propagators.textmap.DefaultSetter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Getter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get", 1], ["keys", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.propagators.textmap.Getter", "name": "<PERSON><PERSON>", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.propagators.textmap.Getter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.propagators.textmap", "mro": ["opentelemetry.propagators.textmap.Getter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "carrier", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.propagators.textmap.Getter.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "carrier", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.propagators.textmap.Getter.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "carrier", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "carrier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.propagators.textmap.Getter.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "carrier"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keys of <PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.propagators.textmap.Getter.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "carrier"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keys of <PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.Getter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Getter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["CarrierT"], "typeddict_type": null}}, "Setter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["set", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.propagators.textmap.Setter", "name": "<PERSON>ter", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Setter", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.propagators.textmap.Setter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.propagators.textmap", "mro": ["opentelemetry.propagators.textmap.Setter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "carrier", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.propagators.textmap.Setter.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "carrier", "key", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Setter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Setter"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Setter", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of Setter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.propagators.textmap.Setter.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "carrier", "key", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Setter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Setter"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Setter", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of Setter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.Setter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": 1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.Setter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Setter"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["CarrierT"], "typeddict_type": null}}, "TextMapPropagator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["extract", 1], ["fields", 1], ["inject", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.propagators.textmap.TextMapPropagator", "name": "TextMapPropagator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.propagators.textmap.TextMapPropagator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.propagators.textmap", "mro": ["opentelemetry.propagators.textmap.TextMapPropagator", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "extract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "getter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.propagators.textmap.TextMapPropagator.extract", "name": "extract", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "getter"], "arg_types": ["opentelemetry.propagators.textmap.TextMapPropagator", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract of TextMapPropagator", "ret_type": "opentelemetry.context.context.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.propagators.textmap.TextMapPropagator.extract", "name": "extract", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "getter"], "arg_types": ["opentelemetry.propagators.textmap.TextMapPropagator", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract of TextMapPropagator", "ret_type": "opentelemetry.context.context.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "opentelemetry.propagators.textmap.TextMapPropagator.fields", "name": "fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.propagators.textmap.TextMapPropagator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fields of TextMapPropagator", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.propagators.textmap.TextMapPropagator.fields", "name": "fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.propagators.textmap.TextMapPropagator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fields of TextMapPropagator", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "inject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "setter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.propagators.textmap.TextMapPropagator.inject", "name": "inject", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "setter"], "arg_types": ["opentelemetry.propagators.textmap.TextMapPropagator", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Setter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject of TextMapPropagator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.propagators.textmap.TextMapPropagator.inject", "name": "inject", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "setter"], "arg_types": ["opentelemetry.propagators.textmap.TextMapPropagator", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Setter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject of TextMapPropagator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "CarrierT", "namespace": "opentelemetry.propagators.textmap.TextMapPropagator.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.TextMapPropagator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.propagators.textmap.TextMapPropagator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.textmap.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.textmap.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.textmap.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.textmap.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.textmap.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.textmap.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "default_getter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "opentelemetry.propagators.textmap.default_getter", "name": "default_getter", "type": {".class": "Instance", "args": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "CarrierT"}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}}}, "default_setter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "opentelemetry.propagators.textmap.default_setter", "name": "default_setter", "type": {".class": "Instance", "args": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "CarrierT"}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Setter"}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\propagators\\textmap.py"}