{".class": "MypyFile", "_fullname": "aiohttp.http_parser", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASCIISET": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.http_parser.ASCIISET", "name": "ASCIISET", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BadHttpMessage": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.BadHttpMessage", "kind": "Gdef", "module_public": false}, "BadHttpMethod": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.BadHttpMethod", "kind": "Gdef", "module_public": false}, "BadStatusLine": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.BadStatusLine", "kind": "Gdef", "module_public": false}, "BaseProtocol": {".class": "SymbolTableNode", "cross_ref": "aiohttp.base_protocol.BaseProtocol", "kind": "Gdef", "module_public": false}, "BaseTimerContext": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.BaseTimerContext", "kind": "Gdef", "module_public": false}, "BrotliDecompressor": {".class": "SymbolTableNode", "cross_ref": "aiohttp.compression_utils.BrotliDecompressor", "kind": "Gdef", "module_public": false}, "CIMultiDict": {".class": "SymbolTableNode", "cross_ref": "multidict.CIMultiDict", "kind": "Gdef", "module_public": false}, "CIMultiDictProxy": {".class": "SymbolTableNode", "cross_ref": "multidict.CIMultiDictProxy", "kind": "Gdef", "module_public": false}, "ChunkState": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.ChunkState", "name": "ChunkState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "aiohttp.http_parser.ChunkState", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.ChunkState", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "PARSE_CHUNKED_CHUNK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.ChunkState.PARSE_CHUNKED_CHUNK", "name": "PARSE_CHUNKED_CHUNK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "PARSE_CHUNKED_CHUNK_EOF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.ChunkState.PARSE_CHUNKED_CHUNK_EOF", "name": "PARSE_CHUNKED_CHUNK_EOF", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "PARSE_CHUNKED_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.ChunkState.PARSE_CHUNKED_SIZE", "name": "PARSE_CHUNKED_SIZE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "PARSE_MAYBE_TRAILERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.ChunkState.PARSE_MAYBE_TRAILERS", "name": "PARSE_MAYBE_TRAILERS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "PARSE_TRAILERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.ChunkState.PARSE_TRAILERS", "name": "PARSE_TRAILERS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.ChunkState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_parser.ChunkState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_public": false}, "ContentEncodingError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.ContentEncodingError", "kind": "Gdef", "module_public": false}, "ContentLengthError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.ContentLengthError", "kind": "Gdef", "module_public": false}, "DEBUG": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.DEBUG", "kind": "Gdef", "module_public": false}, "DIGITS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.http_parser.DIGITS", "name": "DIGITS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "DeflateBuffer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.DeflateBuffer", "name": "Def<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.DeflateBuffer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.DeflateBuffer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "out", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.DeflateBuffer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "out", "encoding"], "arg_types": ["aiohttp.http_parser.DeflateBuffer", "aiohttp.streams.StreamReader", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DeflateBuffer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_started_decoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.DeflateBuffer._started_decoding", "name": "_started_decoding", "type": "builtins.bool"}}, "begin_http_chunk_receiving": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.DeflateBuffer.begin_http_chunk_receiving", "name": "begin_http_chunk_receiving", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.http_parser.DeflateBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "begin_http_chunk_receiving of DeflateBuffer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decompressor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.DeflateBuffer.decompressor", "name": "decompressor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.DeflateBuffer.encoding", "name": "encoding", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "end_http_chunk_receiving": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.DeflateBuffer.end_http_chunk_receiving", "name": "end_http_chunk_receiving", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.http_parser.DeflateBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_http_chunk_receiving of DeflateBuffer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "chunk", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.DeflateBuffer.feed_data", "name": "feed_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "chunk", "size"], "arg_types": ["aiohttp.http_parser.DeflateBuffer", "builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed_data of DeflateBuffer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.DeflateBuffer.feed_eof", "name": "feed_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.http_parser.DeflateBuffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed_eof of DeflateBuffer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.DeflateBuffer.out", "name": "out", "type": "aiohttp.streams.StreamReader"}}, "set_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "exc", "exc_cause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.DeflateBuffer.set_exception", "name": "set_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "exc", "exc_cause"], "arg_types": ["aiohttp.http_parser.DeflateBuffer", "builtins.BaseException", "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_exception of DeflateBuffer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.DeflateBuffer.size", "name": "size", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.DeflateBuffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_parser.DeflateBuffer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EMPTY_BODY_METHODS": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.EMPTY_BODY_METHODS", "kind": "Gdef", "module_public": false}, "EMPTY_BODY_STATUS_CODES": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.EMPTY_BODY_STATUS_CODES", "kind": "Gdef", "module_public": false}, "EMPTY_PAYLOAD": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.EMPTY_PAYLOAD", "kind": "Gdef", "module_public": false}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_public": false}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_public": false}, "HAS_BROTLI": {".class": "SymbolTableNode", "cross_ref": "aiohttp.compression_utils.HAS_BROTLI", "kind": "Gdef", "module_public": false}, "HEXDIGITS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.http_parser.HEXDIGITS", "name": "HEXDIGITS", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "HeadersParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.HeadersParser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HeadersParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.HeadersParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "max_line_size", "max_headers", "max_field_size", "lax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HeadersParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "max_line_size", "max_headers", "max_field_size", "lax"], "arg_types": ["aiohttp.http_parser.HeadersParser", "builtins.int", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>ers<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lax": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HeadersParser._lax", "name": "_lax", "type": "builtins.bool"}}, "max_field_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HeadersParser.max_field_size", "name": "max_field_size", "type": "builtins.int"}}, "max_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HeadersParser.max_headers", "name": "max_headers", "type": "builtins.int"}}, "max_line_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HeadersParser.max_line_size", "name": "max_line_size", "type": "builtins.int"}}, "parse_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HeadersParser.parse_headers", "name": "parse_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["aiohttp.http_parser.HeadersParser", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_headers of HeadersParser", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.HeadersParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_parser.HeadersParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HttpParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_is_chunked_te", 1], ["parse_message", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.HttpParser", "name": "<PERSON>ttp<PERSON><PERSON><PERSON>", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "aiohttp.http_parser.HttpParser", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.HttpParser", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "protocol", "loop", "limit", "max_line_size", "max_headers", "max_field_size", "timer", "code", "method", "payload_exception", "response_with_body", "read_until_eof", "auto_decompress"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "protocol", "loop", "limit", "max_line_size", "max_headers", "max_field_size", "timer", "code", "method", "payload_exception", "response_with_body", "read_until_eof", "auto_decompress"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}, {".class": "UnionType", "items": ["aiohttp.base_protocol.BaseProtocol", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["aiohttp.helpers.BaseTimerContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HttpParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_auto_decompress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser._auto_decompress", "name": "_auto_decompress", "type": "builtins.bool"}}, "_headers_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser._headers_parser", "name": "_headers_parser", "type": "aiohttp.http_parser.HeadersParser"}}, "_is_chunked_te": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "te"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.http_parser.HttpParser._is_chunked_te", "name": "_is_chunked_te", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "te"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_chunked_te of HttpParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.http_parser.HttpParser._is_chunked_te", "name": "_is_chunked_te", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "te"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_chunked_te of HttpParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_limit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser._limit", "name": "_limit", "type": "builtins.int"}}, "_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.http_parser.HttpParser._lines", "name": "_lines", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_payload": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser._payload", "name": "_payload", "type": {".class": "NoneType"}}}, "_payload_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.http_parser.HttpParser._payload_parser", "name": "_payload_parser", "type": {".class": "UnionType", "items": ["aiohttp.http_parser.HttpPayloadParser", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_tail": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser._tail", "name": "_tail", "type": "builtins.bytes"}}, "_upgraded": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser._upgraded", "name": "_upgraded", "type": "builtins.bool"}}, "code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.code", "name": "code", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "feed_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "data", "SEP", "EMPTY", "CONTENT_LENGTH", "METH_CONNECT", "SEC_WEBSOCKET_KEY1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpParser.feed_data", "name": "feed_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "data", "SEP", "EMPTY", "CONTENT_LENGTH", "METH_CONNECT", "SEC_WEBSOCKET_KEY1"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}, "builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser._SEP"}, "builtins.bytes", "multidict.istr", "builtins.str", "multidict.istr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed_data of HttpParser", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}, "aiohttp.streams.StreamReader"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpParser.feed_eof", "name": "feed_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed_eof of HttpParser", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "aiohttp.http_parser.HttpParser.lax", "name": "lax", "type": "builtins.bool"}}, "loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.loop", "name": "loop", "type": {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_field_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.max_field_size", "name": "max_field_size", "type": "builtins.int"}}, "max_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.max_headers", "name": "max_headers", "type": "builtins.int"}}, "max_line_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.max_line_size", "name": "max_line_size", "type": "builtins.int"}}, "method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.method", "name": "method", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "parse_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpParser.parse_headers", "name": "parse_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_headers of HttpParser", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.http_parser.HttpParser.parse_message", "name": "parse_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_message of HttpParser", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.parse_message", "name": "parse_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_message of HttpParser", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "payload_exception": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.payload_exception", "name": "payload_exception", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "protocol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.protocol", "name": "protocol", "type": {".class": "UnionType", "items": ["aiohttp.base_protocol.BaseProtocol", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "read_until_eof": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.read_until_eof", "name": "read_until_eof", "type": "builtins.bool"}}, "response_with_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.response_with_body", "name": "response_with_body", "type": "builtins.bool"}}, "set_upgraded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpParser.set_upgraded", "name": "set_upgraded", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_upgraded of HttpParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpParser.timer", "name": "timer", "type": {".class": "UnionType", "items": ["aiohttp.helpers.BaseTimerContext", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.HttpParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "id": 1, "name": "_MsgT", "namespace": "aiohttp.http_parser.HttpParser", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_MsgT"], "typeddict_type": null}}, "HttpPayloadParser": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.HttpPayloadParser", "name": "HttpPayloadParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpPayloadParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.HttpPayloadParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "payload", "length", "chunked", "compression", "code", "method", "response_with_body", "auto_decompress", "lax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpPayloadParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "payload", "length", "chunked", "compression", "code", "method", "response_with_body", "auto_decompress", "lax"], "arg_types": ["aiohttp.http_parser.HttpPayloadParser", "aiohttp.streams.StreamReader", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HttpPayloadParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_auto_decompress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpPayloadParser._auto_decompress", "name": "_auto_decompress", "type": "builtins.bool"}}, "_chunk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpPayloadParser._chunk", "name": "_chunk", "type": "aiohttp.http_parser.ChunkState"}}, "_chunk_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpPayloadParser._chunk_size", "name": "_chunk_size", "type": "builtins.int"}}, "_chunk_tail": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpPayloadParser._chunk_tail", "name": "_chunk_tail", "type": "builtins.bytes"}}, "_lax": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpPayloadParser._lax", "name": "_lax", "type": "builtins.bool"}}, "_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpPayloadParser._length", "name": "_length", "type": "builtins.int"}}, "_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpPayloadParser._type", "name": "_type", "type": "aiohttp.http_parser.ParseState"}}, "done": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpPayloadParser.done", "name": "done", "type": "builtins.bool"}}, "feed_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "chunk", "SEP", "CHUNK_EXT"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpPayloadParser.feed_data", "name": "feed_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "chunk", "SEP", "CHUNK_EXT"], "arg_types": ["aiohttp.http_parser.HttpPayloadParser", "builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser._SEP"}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed_data of HttpPayloadParser", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpPayloadParser.feed_eof", "name": "feed_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.http_parser.HttpPayloadParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed_eof of HttpPayloadParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "payload": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_parser.HttpPayloadParser.payload", "name": "payload", "type": {".class": "UnionType", "items": ["aiohttp.streams.StreamReader", "aiohttp.http_parser.DeflateBuffer"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.HttpPayloadParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_parser.HttpPayloadParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HttpRequestParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.HttpRequestParser", "name": "HttpRequestParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpRequestParser", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.HttpRequestParser", "aiohttp.http_parser.HttpParser", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_is_chunked_te": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "te"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpRequestParser._is_chunked_te", "name": "_is_chunked_te", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "te"], "arg_types": ["aiohttp.http_parser.HttpRequestParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_chunked_te of HttpRequestParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpRequestParser.parse_message", "name": "parse_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["aiohttp.http_parser.HttpRequestParser", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_message of HttpRequestParser", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.HttpRequestParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_parser.HttpRequestParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HttpRequestParserC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "aiohttp.http_parser.HttpRequestParserC", "line": 1041, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "aiohttp.http_parser.HttpRequestParser"}}, "HttpRequestParserPy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.http_parser.HttpRequestParserPy", "line": 1027, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "aiohttp.http_parser.HttpRequestParser"}}, "HttpResponseParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "extra_attrs": null, "type_ref": "aiohttp.http_parser.HttpParser"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.HttpResponseParser", "name": "HttpResponseParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpResponseParser", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.HttpResponseParser", "aiohttp.http_parser.HttpParser", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_is_chunked_te": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "te"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpResponseParser._is_chunked_te", "name": "_is_chunked_te", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "te"], "arg_types": ["aiohttp.http_parser.HttpResponseParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_chunked_te of HttpResponseParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "data", "SEP", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpResponseParser.feed_data", "name": "feed_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "data", "SEP", "args", "kwargs"], "arg_types": ["aiohttp.http_parser.HttpResponseParser", "builtins.bytes", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser._SEP"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed_data of HttpResponseParser", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}, "aiohttp.streams.StreamReader"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.HttpResponseParser.lax", "name": "lax", "type": "builtins.bool"}}, "parse_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.HttpResponseParser.parse_message", "name": "parse_message", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["aiohttp.http_parser.HttpResponseParser", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_message of HttpResponseParser", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.HttpResponseParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_parser.HttpResponseParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HttpResponseParserC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "aiohttp.http_parser.HttpResponseParserC", "line": 1042, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "aiohttp.http_parser.HttpResponseParser"}}, "HttpResponseParserPy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.http_parser.HttpResponseParserPy", "line": 1028, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "aiohttp.http_parser.HttpResponseParser"}}, "HttpVersion": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion", "kind": "Gdef", "module_public": false}, "HttpVersion10": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion10", "kind": "Gdef", "module_public": false}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef", "module_public": false}, "InvalidHeader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.InvalidHeader", "kind": "Gdef", "module_public": false}, "InvalidURLError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.InvalidURLError", "kind": "Gdef", "module_public": false}, "LineTooLong": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.LineTooLong", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "NO_EXTENSIONS": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.NO_EXTENSIONS", "kind": "Gdef", "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ParseState": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.ParseState", "name": "ParseState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "aiohttp.http_parser.ParseState", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.ParseState", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "PARSE_CHUNKED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.ParseState.PARSE_CHUNKED", "name": "PARSE_CHUNKED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "PARSE_LENGTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.ParseState.PARSE_LENGTH", "name": "PARSE_LENGTH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "PARSE_NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.ParseState.PARSE_NONE", "name": "PARSE_NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "PARSE_UNTIL_EOF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.ParseState.PARSE_UNTIL_EOF", "name": "PARSE_UNTIL_EOF", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.ParseState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_parser.ParseState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "RawHeaders": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.RawHeaders", "kind": "Gdef", "module_public": false}, "RawRequestMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.RawRequestMessage", "name": "RawRequestMessage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "aiohttp.http_parser.RawRequestMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["method", "path", "version", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked", "url"]}}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.RawRequestMessage", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "method"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "path"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "version"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "headers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "raw_headers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "should_close"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "compression"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "upgrade"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "chunked"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "method", "path", "version", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "aiohttp.http_parser.RawRequestMessage.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "method", "path", "version", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked", "url"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of RawRequestMessage", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.RawRequestMessage._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of RawRequestMessage", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "aiohttp.http_parser.RawRequestMessage._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of RawRequestMessage", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of RawRequestMessage", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "method", "path", "version", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.RawRequestMessage._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "method", "path", "version", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked", "url"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of RawRequestMessage", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawRequestMessage._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage._source", "name": "_source", "type": "builtins.str"}}, "chunked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.chunked", "name": "chunked", "type": "builtins.bool"}}, "chunked-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.chunked", "kind": "<PERSON><PERSON><PERSON>"}, "compression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.compression", "name": "compression", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "compression-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.compression", "kind": "<PERSON><PERSON><PERSON>"}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}}}, "headers-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.headers", "kind": "<PERSON><PERSON><PERSON>"}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.method", "name": "method", "type": "builtins.str"}}, "method-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.method", "kind": "<PERSON><PERSON><PERSON>"}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.path", "name": "path", "type": "builtins.str"}}, "path-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.path", "kind": "<PERSON><PERSON><PERSON>"}, "raw_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.raw_headers", "name": "raw_headers", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}}}, "raw_headers-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.raw_headers", "kind": "<PERSON><PERSON><PERSON>"}, "should_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.should_close", "name": "should_close", "type": "builtins.bool"}}, "should_close-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.should_close", "kind": "<PERSON><PERSON><PERSON>"}, "upgrade": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.upgrade", "name": "upgrade", "type": "builtins.bool"}}, "upgrade-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.upgrade", "kind": "<PERSON><PERSON><PERSON>"}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.url", "name": "url", "type": "yarl._url.URL"}}, "url-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.url", "kind": "<PERSON><PERSON><PERSON>"}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawRequestMessage.version", "name": "version", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}}}, "version-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage.version", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawRequestMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": "aiohttp.http_parser.RawRequestMessage"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "NoneType"}, "yarl._url.URL"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "RawRequestMessageC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "aiohttp.http_parser.RawRequestMessageC", "line": 1043, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}}}, "RawRequestMessagePy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.http_parser.RawRequestMessagePy", "line": 1029, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}}}, "RawResponseMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_parser.RawResponseMessage", "name": "RawResponseMessage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "aiohttp.http_parser.RawResponseMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["version", "code", "reason", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked"]}}, "module_name": "aiohttp.http_parser", "mro": ["aiohttp.http_parser.RawResponseMessage", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "version"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "code"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reason"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "headers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "raw_headers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "should_close"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "compression"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "upgrade"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "chunked"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "version", "code", "reason", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "aiohttp.http_parser.RawResponseMessage.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "version", "code", "reason", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of RawResponseMessage", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.RawResponseMessage._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of RawResponseMessage", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "aiohttp.http_parser.RawResponseMessage._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of RawResponseMessage", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of RawResponseMessage", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "version", "code", "reason", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser.RawResponseMessage._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "version", "code", "reason", "headers", "raw_headers", "should_close", "compression", "upgrade", "chunked"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of RawResponseMessage", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage._NT", "id": -1, "name": "_NT", "namespace": "aiohttp.http_parser.RawResponseMessage._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage._source", "name": "_source", "type": "builtins.str"}}, "chunked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.chunked", "name": "chunked", "type": "builtins.bool"}}, "chunked-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage.chunked", "kind": "<PERSON><PERSON><PERSON>"}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.code", "name": "code", "type": "builtins.int"}}, "code-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage.code", "kind": "<PERSON><PERSON><PERSON>"}, "compression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.compression", "name": "compression", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "compression-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage.compression", "kind": "<PERSON><PERSON><PERSON>"}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}}}, "headers-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage.headers", "kind": "<PERSON><PERSON><PERSON>"}, "raw_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.raw_headers", "name": "raw_headers", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}}}, "raw_headers-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage.raw_headers", "kind": "<PERSON><PERSON><PERSON>"}, "reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.reason", "name": "reason", "type": "builtins.str"}}, "reason-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage.reason", "kind": "<PERSON><PERSON><PERSON>"}, "should_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.should_close", "name": "should_close", "type": "builtins.bool"}}, "should_close-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage.should_close", "kind": "<PERSON><PERSON><PERSON>"}, "upgrade": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.upgrade", "name": "upgrade", "type": "builtins.bool"}}, "upgrade-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage.upgrade", "kind": "<PERSON><PERSON><PERSON>"}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.http_parser.RawResponseMessage.version", "name": "version", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}}}, "version-redefinition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage.version", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser.RawResponseMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": "aiohttp.http_parser.RawResponseMessage"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "RawResponseMessageC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 8, "fullname": "aiohttp.http_parser.RawResponseMessageC", "line": 1044, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}}}, "RawResponseMessagePy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.http_parser.RawResponseMessagePy", "line": 1030, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "StreamReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.StreamReader", "kind": "Gdef", "module_public": false}, "TOKENRE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.http_parser.TOKENRE", "name": "TOKENRE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "TransferEncodingError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.TransferEncodingError", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "URL": {".class": "SymbolTableNode", "cross_ref": "yarl._url.URL", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "VERSRE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.http_parser.VERSRE", "name": "VERSRE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "ZLibDecompressor": {".class": "SymbolTableNode", "cross_ref": "aiohttp.compression_utils.ZLibDecompressor", "kind": "Gdef", "module_public": false}, "_EXC_SENTINEL": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers._EXC_SENTINEL", "kind": "Gdef", "module_public": false}, "_MsgT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_parser._MsgT", "name": "_MsgT", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawResponseMessage"}], "variance": 0}}, "_SEP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.http_parser._SEP", "line": 63, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bytes", "value": "\\r\\n"}, {".class": "LiteralType", "fallback": "builtins.bytes", "value": "\\n"}], "uses_pep604_syntax": false}}}, "_TCHAR_SPECIALS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.http_parser._TCHAR_SPECIALS", "name": "_TCHAR_SPECIALS", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_parser.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_parser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_parser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_parser.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_parser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_parser.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_parser.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_is_supported_upgrade": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_parser._is_supported_upgrade", "name": "_is_supported_upgrade", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["headers"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_supported_upgrade", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef", "module_public": false}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_public": false}, "hdrs": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs", "kind": "Gdef", "module_public": false}, "istr": {".class": "SymbolTableNode", "cross_ref": "multidict.istr", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "set_exception": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.set_exception", "kind": "Gdef", "module_public": false}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef", "module_public": false}, "suppress": {".class": "SymbolTableNode", "cross_ref": "contextlib.suppress", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\http_parser.py"}