{".class": "MypyFile", "_fullname": "aiohttp._websocket.reader", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "NO_EXTENSIONS": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.NO_EXTENSIONS", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "WebSocketDataQueue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "aiohttp._websocket.reader.WebSocketDataQueue", "line": 14, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "aiohttp._websocket.reader_py.WebSocketDataQueue"}}, "WebSocketDataQueuePython": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.reader_py.WebSocketDataQueue", "kind": "Gdef"}, "WebSocketReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "aiohttp._websocket.reader.WebSocketReader", "line": 13, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "aiohttp._websocket.reader_py.WebSocketReader"}}, "WebSocketReaderPython": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.reader_py.WebSocketReader", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.reader.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.reader.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.reader.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.reader.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.reader.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.reader.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\_websocket\\reader.py"}