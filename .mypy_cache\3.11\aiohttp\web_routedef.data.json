{".class": "MypyFile", "_fullname": "aiohttp.web_routedef", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractRoute": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.AbstractRoute", "kind": "Gdef", "module_public": false}, "AbstractRouteDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["register", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_routedef.AbstractRouteDef", "name": "AbstractRouteDef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "aiohttp.web_routedef.AbstractRouteDef", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_routedef", "mro": ["aiohttp.web_routedef.AbstractRouteDef", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "router"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.web_routedef.AbstractRouteDef.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "router"], "arg_types": ["aiohttp.web_routedef.AbstractRouteDef", "aiohttp.web_urldispatcher.UrlDispatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of AbstractRouteDef", "ret_type": {".class": "Instance", "args": ["aiohttp.web_urldispatcher.AbstractRoute"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_routedef.AbstractRouteDef.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "router"], "arg_types": ["aiohttp.web_routedef.AbstractRouteDef", "aiohttp.web_urldispatcher.UrlDispatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of AbstractRouteDef", "ret_type": {".class": "Instance", "args": ["aiohttp.web_urldispatcher.AbstractRoute"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.AbstractRouteDef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_routedef.AbstractRouteDef", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractView": {".class": "SymbolTableNode", "cross_ref": "aiohttp.abc.AbstractView", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Handler": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.Handler", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.PathLike", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_request.Request", "kind": "Gdef", "module_public": false}, "RouteDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_routedef.AbstractRouteDef"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_routedef.RouteDef", "name": "RouteDef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteDef", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 60, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "method"}, {"alias": null, "context_column": 4, "context_line": 61, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "path"}, {"alias": null, "context_column": 4, "context_line": 62, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, "kw_only": false, "name": "handler"}, {"alias": null, "context_column": 4, "context_line": 63, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "kw_only": false, "name": "kwargs"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.web_routedef", "mro": ["aiohttp.web_routedef.RouteDef", "aiohttp.web_routedef.AbstractRouteDef", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_web_routedef_RouteDef_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_routedef.RouteDef.__aiohttp_web_routedef_RouteDef_AttrsAttributes__", "name": "__aiohttp_web_routedef_RouteDef_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteDef.__aiohttp_web_routedef_RouteDef_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.web_routedef", "mro": ["aiohttp.web_routedef.RouteDef.__aiohttp_web_routedef_RouteDef_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "handler", "name": "handler", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "kwargs", "name": "kwargs", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "method", "name": "method", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "path", "name": "path", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.web_routedef.RouteDef.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.web_routedef.RouteDef.__aiohttp_web_routedef_RouteDef_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteDef.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of RouteDef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteDef.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of RouteDef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "path", "handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "path", "handler", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteDef", "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RouteDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteDef.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of RouteDef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteDef.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of RouteDef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.RouteDef.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.web_routedef.RouteDef.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "method"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "path"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "handler"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "kwargs"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteDef.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_routedef.RouteDef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of RouteDef", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_routedef.RouteDef.handler", "name": "handler", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}}}, "kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_routedef.RouteDef.kwargs", "name": "kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_routedef.RouteDef.method", "name": "method", "type": "builtins.str"}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_routedef.RouteDef.path", "name": "path", "type": "builtins.str"}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "router"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteDef.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "router"], "arg_types": ["aiohttp.web_routedef.RouteDef", "aiohttp.web_urldispatcher.UrlDispatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of RouteDef", "ret_type": {".class": "Instance", "args": ["aiohttp.web_urldispatcher.AbstractRoute"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteDef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_routedef.RouteDef", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RouteTableDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["aiohttp.web_routedef.AbstractRouteDef"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_routedef.RouteTableDef", "name": "RouteTableDef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_routedef", "mro": ["aiohttp.web_routedef.RouteTableDef", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of RouteTableDef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.__getitem__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "aiohttp.web_routedef.RouteTableDef.__getitem__", "name": "__getitem__", "type": null}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiohttp.web_routedef.RouteTableDef.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of RouteTableDef", "ret_type": "aiohttp.web_routedef.AbstractRouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiohttp.web_routedef.RouteTableDef.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of RouteTableDef", "ret_type": "aiohttp.web_routedef.AbstractRouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiohttp.web_routedef.RouteTableDef.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_routedef.RouteTableDef", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of RouteTableDef", "ret_type": {".class": "Instance", "args": ["aiohttp.web_routedef.AbstractRouteDef"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiohttp.web_routedef.RouteTableDef.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_routedef.RouteTableDef", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of RouteTableDef", "ret_type": {".class": "Instance", "args": ["aiohttp.web_routedef.AbstractRouteDef"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of RouteTableDef", "ret_type": "aiohttp.web_routedef.AbstractRouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_routedef.RouteTableDef", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of RouteTableDef", "ret_type": {".class": "Instance", "args": ["aiohttp.web_routedef.AbstractRouteDef"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_routedef.RouteTableDef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RouteTableDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_routedef.RouteTableDef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of RouteTableDef", "ret_type": {".class": "Instance", "args": ["aiohttp.web_routedef.AbstractRouteDef"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_routedef.RouteTableDef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of RouteTableDef", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_routedef.RouteTableDef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of RouteTableDef", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_items": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.web_routedef.RouteTableDef._items", "name": "_items", "type": {".class": "Instance", "args": ["aiohttp.web_routedef.AbstractRouteDef"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of RouteTableDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._Deco"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of RouteTableDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._Deco"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.head", "name": "head", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "head of RouteTableDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._Deco"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options of RouteTableDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._Deco"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.patch", "name": "patch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch of RouteTableDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._Deco"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.post", "name": "post", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "post of RouteTableDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._Deco"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.put", "name": "put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put of RouteTableDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._Deco"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "route": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "method", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.route", "name": "route", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "method", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "route of RouteTableDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._Deco"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "static": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "prefix", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.static", "name": "static", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "prefix", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "static of RouteTableDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.RouteTableDef.view", "name": "view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.RouteTableDef", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "view of RouteTableDef", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._Deco"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.RouteTableDef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_routedef.RouteTableDef", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "StaticDef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_routedef.AbstractRouteDef"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_routedef.StaticDef", "name": "StaticDef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.StaticDef", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 85, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "prefix"}, {"alias": null, "context_column": 4, "context_line": 86, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}, "kw_only": false, "name": "path"}, {"alias": null, "context_column": 4, "context_line": 87, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "kw_only": false, "name": "kwargs"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.web_routedef", "mro": ["aiohttp.web_routedef.StaticDef", "aiohttp.web_routedef.AbstractRouteDef", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_web_routedef_StaticDef_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_routedef.StaticDef.__aiohttp_web_routedef_StaticDef_AttrsAttributes__", "name": "__aiohttp_web_routedef_StaticDef_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.StaticDef.__aiohttp_web_routedef_StaticDef_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.web_routedef", "mro": ["aiohttp.web_routedef.StaticDef.__aiohttp_web_routedef_StaticDef_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "kwargs", "name": "kwargs", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "path", "name": "path", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "prefix", "name": "prefix", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.web_routedef.StaticDef.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.web_routedef.StaticDef.__aiohttp_web_routedef_StaticDef_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.StaticDef.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of StaticDef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.StaticDef.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of StaticDef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "prefix", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.StaticDef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "prefix", "path", "kwargs"], "arg_types": ["aiohttp.web_routedef.StaticDef", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StaticDef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.StaticDef.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of StaticDef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.StaticDef.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of StaticDef", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_routedef.StaticDef.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.web_routedef.StaticDef.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "prefix"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "path"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "kwargs"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.StaticDef.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_routedef.StaticDef"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of StaticDef", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_routedef.StaticDef.kwargs", "name": "kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_routedef.StaticDef.path", "name": "path", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}}}, "prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_routedef.StaticDef.prefix", "name": "prefix", "type": "builtins.str"}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "router"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.StaticDef.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "router"], "arg_types": ["aiohttp.web_routedef.StaticDef", "aiohttp.web_urldispatcher.UrlDispatcher"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of StaticDef", "ret_type": {".class": "Instance", "args": ["aiohttp.web_urldispatcher.AbstractRoute"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_routedef.StaticDef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_routedef.StaticDef", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_response.StreamResponse", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UrlDispatcher": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.UrlDispatcher", "kind": "Gdef", "module_public": false}, "_Deco": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.web_routedef._Deco", "line": 152, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_HandlerType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.web_routedef._HandlerType", "line": 55, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "aiohttp.abc.AbstractView"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.Handler"}], "uses_pep604_syntax": false}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_routedef.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_routedef.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_routedef.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_routedef.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_routedef.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_routedef.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_routedef.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef", "module_public": false}, "attr": {".class": "SymbolTableNode", "cross_ref": "attr", "kind": "Gdef", "module_public": false}, "delete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete", "ret_type": "aiohttp.web_routedef.RouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["path", "handler", "name", "allow_head", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["path", "handler", "name", "allow_head", "kwargs"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get", "ret_type": "aiohttp.web_routedef.RouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hdrs": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs", "kind": "Gdef", "module_public": false}, "head": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.head", "name": "head", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "head", "ret_type": "aiohttp.web_routedef.RouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "options": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options", "ret_type": "aiohttp.web_routedef.RouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "patch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.patch", "name": "patch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch", "ret_type": "aiohttp.web_routedef.RouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.post", "name": "post", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "post", "ret_type": "aiohttp.web_routedef.RouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.put", "name": "put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put", "ret_type": "aiohttp.web_routedef.RouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "route": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["method", "path", "handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.route", "name": "route", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["method", "path", "handler", "kwargs"], "arg_types": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web_routedef._HandlerType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "route", "ret_type": "aiohttp.web_routedef.RouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "static": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["prefix", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.static", "name": "static", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["prefix", "path", "kwargs"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "static", "ret_type": "aiohttp.web_routedef.StaticDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "view": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_routedef.view", "name": "view", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["path", "handler", "kwargs"], "arg_types": ["builtins.str", {".class": "TypeType", "item": "aiohttp.abc.AbstractView"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "view", "ret_type": "aiohttp.web_routedef.RouteDef", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web_routedef.py"}