{".class": "MypyFile", "_fullname": "charset_normalizer.cd", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CoherenceMatches": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.models.CoherenceMatches", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FREQUENCIES": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.assets.FREQUENCIES", "kind": "Gdef"}, "IncrementalDecoder": {".class": "SymbolTableNode", "cross_ref": "codecs.IncrementalDecoder", "kind": "Gdef"}, "KO_NAMES": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.KO_NAMES", "kind": "Gdef"}, "LANGUAGE_SUPPORTED_COUNT": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.LANGUAGE_SUPPORTED_COUNT", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TOO_SMALL_SEQUENCE": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.TOO_SMALL_SEQUENCE", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeCounter": {".class": "SymbolTableNode", "cross_ref": "typing.Counter", "kind": "Gdef"}, "ZH_NAMES": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.ZH_NAMES", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.cd.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.cd.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.cd.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.cd.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.cd.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.cd.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "alpha_unicode_split": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["decoded_sequence"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.cd.alpha_unicode_split", "name": "alpha_unicode_split", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["decoded_sequence"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alpha_unicode_split", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alphabet_languages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["characters", "ignore_non_latin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.cd.alphabet_languages", "name": "alphabet_languages", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["characters", "ignore_non_latin"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alphabet_languages", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "characters_popularity_compare": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["language", "ordered_characters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.cd.characters_popularity_compare", "name": "characters_popularity_compare", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["language", "ordered_characters"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "characters_popularity_compare", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "coherence_ratio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["decoded_sequence", "threshold", "lg_inclusion"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.cd.coherence_ratio", "name": "coherence_ratio", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["decoded_sequence", "threshold", "lg_inclusion"], "arg_types": ["builtins.str", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "coherence_ratio", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "charset_normalizer.models.CoherenceMatches"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.cd.coherence_ratio", "name": "coherence_ratio", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "charset_normalizer.models.CoherenceMatch"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "encoding_languages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iana_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.cd.encoding_languages", "name": "encoding_languages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iana_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encoding_languages", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.cd.encoding_languages", "name": "encoding_languages", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "encoding_unicode_range": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iana_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.cd.encoding_unicode_range", "name": "encoding_unicode_range", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iana_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encoding_unicode_range", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter_alt_coherence_matches": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.cd.filter_alt_coherence_matches", "name": "filter_alt_coherence_matches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["results"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "charset_normalizer.models.CoherenceMatches"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_alt_coherence_matches", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "charset_normalizer.models.CoherenceMatches"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_target_features": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["language"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.cd.get_target_features", "name": "get_target_features", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["language"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_target_features", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.cd.get_target_features", "name": "get_target_features", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "is_accentuated": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_accentuated", "kind": "Gdef"}, "is_latin": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_latin", "kind": "Gdef"}, "is_multi_byte_encoding": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_multi_byte_encoding", "kind": "Gdef"}, "is_suspiciously_successive_range": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.md.is_suspiciously_successive_range", "kind": "Gdef"}, "is_unicode_range_secondary": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_unicode_range_secondary", "kind": "Gdef"}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "mb_encoding_languages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iana_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.cd.mb_encoding_languages", "name": "mb_encoding_languages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iana_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mb_encoding_languages", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.cd.mb_encoding_languages", "name": "mb_encoding_languages", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "merge_coherence_ratios": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.cd.merge_coherence_ratios", "name": "merge_coherence_ratios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["results"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "charset_normalizer.models.CoherenceMatches"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_coherence_ratios", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "charset_normalizer.models.CoherenceMatches"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unicode_range": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.unicode_range", "kind": "Gdef"}, "unicode_range_languages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["primary_range"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.cd.unicode_range_languages", "name": "unicode_range_languages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["primary_range"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_range_languages", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\charset_normalizer\\cd.py"}