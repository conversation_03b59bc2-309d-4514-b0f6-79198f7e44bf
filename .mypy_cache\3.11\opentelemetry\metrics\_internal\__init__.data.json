{".class": "MypyFile", "_fullname": "opentelemetry.metrics._internal", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Attributes": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.util.types.Attributes", "kind": "Gdef"}, "CallbackT": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.CallbackT", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.Counter", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Gauge": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.Gauge", "kind": "Gdef"}, "Histogram": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.Histogram", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Lock": {".class": "SymbolTableNode", "cross_ref": "threading.Lock", "kind": "Gdef"}, "Meter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["create_counter", 1], ["create_histogram", 1], ["create_observable_counter", 1], ["create_observable_gauge", 1], ["create_observable_up_down_counter", 1], ["create_up_down_counter", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.Meter", "name": "<PERSON>er", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.Meter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal", "mro": ["opentelemetry.metrics._internal.Meter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "version", "schema_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.Meter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "version", "schema_url"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON>er", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_instrument_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter._instrument_ids", "name": "_instrument_ids", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_instrument_ids_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter._instrument_ids_lock", "name": "_instrument_ids_lock", "type": "_thread.LockType"}}, "_log_instrument_registration_conflict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["name", "instrumentation_type", "unit", "description", "status"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "opentelemetry.metrics._internal.Meter._log_instrument_registration_conflict", "name": "_log_instrument_registration_conflict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["name", "instrumentation_type", "unit", "description", "status"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "opentelemetry.metrics._internal._InstrumentRegistrationStatus"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_instrument_registration_conflict of Meter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter._log_instrument_registration_conflict", "name": "_log_instrument_registration_conflict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["name", "instrumentation_type", "unit", "description", "status"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "opentelemetry.metrics._internal._InstrumentRegistrationStatus"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_instrument_registration_conflict of Meter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter._name", "name": "_name", "type": "builtins.str"}}, "_register_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "type_", "unit", "description", "advisory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.Meter._register_instrument", "name": "_register_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "type_", "unit", "description", "advisory"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", "builtins.type", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_instrument of Meter", "ret_type": "opentelemetry.metrics._internal._InstrumentRegistrationStatus", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_schema_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter._schema_url", "name": "_schema_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter._version", "name": "_version", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "create_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.Meter.create_counter", "name": "create_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_counter of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.Counter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter.create_counter", "name": "create_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_counter of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.Counter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_gauge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.Meter.create_gauge", "name": "create_gauge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_gauge of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.Gauge", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_histogram": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.Meter.create_histogram", "name": "create_histogram", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_histogram of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.Histogram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter.create_histogram", "name": "create_histogram", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_histogram of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.Histogram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_observable_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.Meter.create_observable_counter", "name": "create_observable_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_counter of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter.create_observable_counter", "name": "create_observable_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_counter of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_observable_gauge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.Meter.create_observable_gauge", "name": "create_observable_gauge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_gauge of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableGauge", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter.create_observable_gauge", "name": "create_observable_gauge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_gauge of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableGauge", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_observable_up_down_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.Meter.create_observable_up_down_counter", "name": "create_observable_up_down_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_up_down_counter of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter.create_observable_up_down_counter", "name": "create_observable_up_down_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_up_down_counter of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_up_down_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.Meter.create_up_down_counter", "name": "create_up_down_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_up_down_counter of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.UpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter.create_up_down_counter", "name": "create_up_down_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.Meter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_up_down_counter of Meter", "ret_type": "opentelemetry.metrics._internal.instrument.UpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.metrics._internal.Meter.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "schema_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.metrics._internal.Meter.schema_url", "name": "schema_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "schema_url of Meter", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter.schema_url", "name": "schema_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "schema_url of Meter", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.metrics._internal.Meter.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.Meter.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.Meter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.Meter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MeterProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_meter", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.MeterProvider", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.MeterProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal", "mro": ["opentelemetry.metrics._internal.MeterProvider", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_meter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "version", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.MeterProvider.get_meter", "name": "get_meter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "version", "schema_url", "attributes"], "arg_types": ["opentelemetry.metrics._internal.MeterProvider", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_meter of MeterProvider", "ret_type": "opentelemetry.metrics._internal.Meter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.MeterProvider.get_meter", "name": "get_meter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "version", "schema_url", "attributes"], "arg_types": ["opentelemetry.metrics._internal.MeterProvider", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_meter of MeterProvider", "ret_type": "opentelemetry.metrics._internal.Meter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.MeterProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.MeterProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.NoOpCounter", "kind": "Gdef"}, "NoOpGauge": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.NoOpGauge", "kind": "Gdef"}, "NoOpHistogram": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.NoOpHistogram", "kind": "Gdef"}, "NoOpMeter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.Meter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.NoOpMeter", "name": "NoOpMeter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal", "mro": ["opentelemetry.metrics._internal.NoOpMeter", "opentelemetry.metrics._internal.Meter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "create_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeter.create_counter", "name": "create_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.NoOpMeter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_counter of NoOpMeter", "ret_type": "opentelemetry.metrics._internal.instrument.Counter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_gauge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeter.create_gauge", "name": "create_gauge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.NoOpMeter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_gauge of NoOpMeter", "ret_type": "opentelemetry.metrics._internal.instrument.Gauge", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_histogram": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeter.create_histogram", "name": "create_histogram", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "arg_types": ["opentelemetry.metrics._internal.NoOpMeter", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_histogram of NoOpMeter", "ret_type": "opentelemetry.metrics._internal.instrument.Histogram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_observable_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeter.create_observable_counter", "name": "create_observable_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.NoOpMeter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_counter of NoOpMeter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_observable_gauge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeter.create_observable_gauge", "name": "create_observable_gauge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.NoOpMeter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_gauge of NoOpMeter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableGauge", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_observable_up_down_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeter.create_observable_up_down_counter", "name": "create_observable_up_down_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.NoOpMeter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_up_down_counter of NoOpMeter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_up_down_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeter.create_up_down_counter", "name": "create_up_down_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.NoOpMeter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_up_down_counter of NoOpMeter", "ret_type": "opentelemetry.metrics._internal.instrument.UpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.NoOpMeter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.NoOpMeter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpMeterProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.MeterProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.NoOpMeterProvider", "name": "NoOpMeterProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeterProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal", "mro": ["opentelemetry.metrics._internal.NoOpMeterProvider", "opentelemetry.metrics._internal.MeterProvider", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_meter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "version", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.NoOpMeterProvider.get_meter", "name": "get_meter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "version", "schema_url", "attributes"], "arg_types": ["opentelemetry.metrics._internal.NoOpMeterProvider", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_meter of NoOpMeterProvider", "ret_type": "opentelemetry.metrics._internal.Meter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.NoOpMeterProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.NoOpMeterProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpObservableCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.NoOpObservableCounter", "kind": "Gdef"}, "NoOpObservableGauge": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.NoOpObservableGauge", "kind": "Gdef"}, "NoOpObservableUpDownCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.NoOpObservableUpDownCounter", "kind": "Gdef"}, "NoOpUpDownCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.NoOpUpDownCounter", "kind": "Gdef"}, "OTEL_PYTHON_METER_PROVIDER": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.environment_variables.OTEL_PYTHON_METER_PROVIDER", "kind": "Gdef"}, "ObservableCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.ObservableCounter", "kind": "Gdef"}, "ObservableGauge": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.ObservableGauge", "kind": "Gdef"}, "ObservableUpDownCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "kind": "Gdef"}, "Once": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.util._once.Once", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpDownCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument.UpDownCounter", "kind": "Gdef"}, "_InstrumentRegistrationStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus", "name": "_InstrumentRegistrationStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 186, "name": "instrument_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 187, "name": "already_registered", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 188, "name": "conflict", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 189, "name": "current_advisory", "type": {".class": "UnionType", "items": ["opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "opentelemetry.metrics._internal", "mro": ["opentelemetry.metrics._internal._InstrumentRegistrationStatus", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "instrument_id", "already_registered", "conflict", "current_advisory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "instrument_id", "already_registered", "conflict", "current_advisory"], "arg_types": ["opentelemetry.metrics._internal._InstrumentRegistrationStatus", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _InstrumentRegistrationStatus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "instrument_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "already_registered"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "conflict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "current_advisory"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["instrument_id", "already_registered", "conflict", "current_advisory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["instrument_id", "already_registered", "conflict", "current_advisory"], "arg_types": ["builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _InstrumentRegistrationStatus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["instrument_id", "already_registered", "conflict", "current_advisory"], "arg_types": ["builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _InstrumentRegistrationStatus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "already_registered": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.already_registered", "name": "already_registered", "type": "builtins.bool"}}, "conflict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.conflict", "name": "conflict", "type": "builtins.bool"}}, "current_advisory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.current_advisory", "name": "current_advisory", "type": {".class": "UnionType", "items": ["opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "instrument_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.instrument_id", "name": "instrument_id", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal._InstrumentRegistrationStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal._InstrumentRegistrationStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_METER_PROVIDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "opentelemetry.metrics._internal._METER_PROVIDER", "name": "_METER_PROVIDER", "type": {".class": "UnionType", "items": ["opentelemetry.metrics._internal.MeterProvider", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_METER_PROVIDER_SET_ONCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.metrics._internal._METER_PROVIDER_SET_ONCE", "name": "_METER_PROVIDER_SET_ONCE", "type": "opentelemetry.util._once.Once"}}, "_MetricsHistogramAdvisory": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", "kind": "Gdef"}, "_PROXY_METER_PROVIDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.metrics._internal._PROXY_METER_PROVIDER", "name": "_PROXY_METER_PROVIDER", "type": "opentelemetry.metrics._internal._ProxyMeterProvider"}}, "_ProxyCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument._ProxyCounter", "kind": "Gdef"}, "_ProxyGauge": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument._ProxyGauge", "kind": "Gdef"}, "_ProxyHistogram": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument._ProxyHistogram", "kind": "Gdef"}, "_ProxyInstrumentT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "opentelemetry.metrics._internal._ProxyInstrumentT", "line": 87, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["opentelemetry.metrics._internal.instrument._ProxyCounter", "opentelemetry.metrics._internal.instrument._ProxyHistogram", "opentelemetry.metrics._internal.instrument._ProxyGauge", "opentelemetry.metrics._internal.instrument._ProxyObservableCounter", "opentelemetry.metrics._internal.instrument._ProxyObservableGauge", "opentelemetry.metrics._internal.instrument._ProxyObservableUpDownCounter", "opentelemetry.metrics._internal.instrument._ProxyUpDownCounter"], "uses_pep604_syntax": false}}}, "_ProxyMeter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.Meter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal._ProxyMeter", "name": "_ProxyMeter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal", "mro": ["opentelemetry.metrics._internal._ProxyMeter", "opentelemetry.metrics._internal.Meter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "version", "schema_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "version", "schema_url"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeter", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ProxyMeter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_instruments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal._ProxyMeter._instruments", "name": "_instruments", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal._ProxyInstrumentT"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal._ProxyMeter._lock", "name": "_lock", "type": "_thread.LockType"}}, "_real_meter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal._ProxyMeter._real_meter", "name": "_real_meter", "type": {".class": "UnionType", "items": ["opentelemetry.metrics._internal.Meter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "create_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter.create_counter", "name": "create_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_counter of _ProxyMeter", "ret_type": "opentelemetry.metrics._internal.instrument.Counter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_gauge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter.create_gauge", "name": "create_gauge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_gauge of _ProxyMeter", "ret_type": "opentelemetry.metrics._internal.instrument.Gauge", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_histogram": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter.create_histogram", "name": "create_histogram", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeter", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_histogram of _ProxyMeter", "ret_type": "opentelemetry.metrics._internal.instrument.Histogram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_observable_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter.create_observable_counter", "name": "create_observable_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_counter of _ProxyMeter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_observable_gauge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter.create_observable_gauge", "name": "create_observable_gauge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_gauge of _ProxyMeter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableGauge", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_observable_up_down_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter.create_observable_up_down_counter", "name": "create_observable_up_down_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_observable_up_down_counter of _ProxyMeter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_up_down_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter.create_up_down_counter", "name": "create_up_down_counter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_up_down_counter of _ProxyMeter", "ret_type": "opentelemetry.metrics._internal.instrument.UpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_set_meter_provider": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeter.on_set_meter_provider", "name": "on_set_meter_provider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter_provider"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeter", "opentelemetry.metrics._internal.MeterProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_set_meter_provider of _ProxyMeter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal._ProxyMeter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal._ProxyMeter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyMeterProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.MeterProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal._ProxyMeterProvider", "name": "_ProxyMeterProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeterProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal", "mro": ["opentelemetry.metrics._internal._ProxyMeterProvider", "opentelemetry.metrics._internal.MeterProvider", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeterProvider.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeterProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ProxyMeterProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal._ProxyMeterProvider._lock", "name": "_lock", "type": "_thread.LockType"}}, "_meters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal._ProxyMeterProvider._meters", "name": "_meters", "type": {".class": "Instance", "args": ["opentelemetry.metrics._internal._ProxyMeter"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_real_meter_provider": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal._ProxyMeterProvider._real_meter_provider", "name": "_real_meter_provider", "type": {".class": "UnionType", "items": ["opentelemetry.metrics._internal.MeterProvider", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_meter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "version", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeterProvider.get_meter", "name": "get_meter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "version", "schema_url", "attributes"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeterProvider", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_meter of _ProxyMeterProvider", "ret_type": "opentelemetry.metrics._internal.Meter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_set_meter_provider": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._ProxyMeterProvider.on_set_meter_provider", "name": "on_set_meter_provider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter_provider"], "arg_types": ["opentelemetry.metrics._internal._ProxyMeterProvider", "opentelemetry.metrics._internal.MeterProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_set_meter_provider of _ProxyMeterProvider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal._ProxyMeterProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal._ProxyMeterProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyObservableCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument._ProxyObservableCounter", "kind": "Gdef"}, "_ProxyObservableGauge": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument._ProxyObservableGauge", "kind": "Gdef"}, "_ProxyObservableUpDownCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument._ProxyObservableUpDownCounter", "kind": "Gdef"}, "_ProxyUpDownCounter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.instrument._ProxyUpDownCounter", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_load_provider": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.util._providers._load_provider", "kind": "Gdef"}, "_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.metrics._internal._logger", "name": "_logger", "type": "logging.Logger"}}, "_set_meter_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["meter_provider", "log"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal._set_meter_provider", "name": "_set_meter_provider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["meter_provider", "log"], "arg_types": ["opentelemetry.metrics._internal.MeterProvider", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_meter_provider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "environ": {".class": "SymbolTableNode", "cross_ref": "os.environ", "kind": "Gdef"}, "getLogger": {".class": "SymbolTableNode", "cross_ref": "logging.getLogger", "kind": "Gdef"}, "get_meter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["name", "version", "meter_provider", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.get_meter", "name": "get_meter", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["name", "version", "meter_provider", "schema_url", "attributes"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["opentelemetry.metrics._internal.MeterProvider", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_meter", "ret_type": "opentelemetry.metrics._internal.Meter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_meter_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.get_meter_provider", "name": "get_meter_provider", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_meter_provider", "ret_type": "opentelemetry.metrics._internal.MeterProvider", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_meter_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["meter_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.set_meter_provider", "name": "set_meter_provider", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["meter_provider"], "arg_types": ["opentelemetry.metrics._internal.MeterProvider"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_meter_provider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\metrics\\_internal\\__init__.py"}