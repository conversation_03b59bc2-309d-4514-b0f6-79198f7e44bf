{".class": "MypyFile", "_fullname": "aiohttp.web_exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "HTTPAccepted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPSuccessful"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPAccepted", "name": "HTTPAccepted", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPAccepted", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPAccepted", "aiohttp.web_exceptions.HTTPSuccessful", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPAccepted.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPAccepted.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPAccepted", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPBadGateway": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPBadGateway", "name": "HTTPBadGateway", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPBadGateway", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPBadGateway", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPBadGateway.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPBadGateway.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPBadGateway", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPBadRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPBadRequest", "name": "HTTPBadRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPBadRequest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPBadRequest", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPBadRequest.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPBadRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPBadRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPClientError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPClientError", "name": "HTTPClientError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPClientError", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPClientError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPClientError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPConflict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPConflict", "name": "HTTPConflict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPConflict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPConflict", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPConflict.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPConflict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPConflict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPCreated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPSuccessful"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPCreated", "name": "HTTPCreated", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPCreated", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPCreated", "aiohttp.web_exceptions.HTTPSuccessful", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPCreated.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPCreated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPCreated", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPError", "name": "HTTPError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPError", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_response.Response", "builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPException", "name": "HTTPException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPException", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPException.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_exceptions.HTTPException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of HTTPException", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__http_exception__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPException.__http_exception__", "name": "__http_exception__", "type": "builtins.bool"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "headers", "reason", "body", "text", "content_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "headers", "reason", "body", "text", "content_type"], "arg_types": ["aiohttp.web_exceptions.HTTPException", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "empty_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPException.empty_body", "name": "empty_body", "type": "builtins.bool"}}, "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPException.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPExpectationFailed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPExpectationFailed", "name": "HTTPExpectationFailed", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPExpectationFailed", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPExpectationFailed", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPExpectationFailed.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPExpectationFailed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPExpectationFailed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPFailedDependency": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPFailedDependency", "name": "HTTPFailedDependency", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPFailedDependency", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPFailedDependency", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPFailedDependency.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPFailedDependency.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPFailedDependency", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPForbidden": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPForbidden", "name": "HTTPForbidden", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPForbidden", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPForbidden", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPForbidden.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPForbidden.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPForbidden", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPMove"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPFound", "name": "HTTPFound", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPFound", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPFound", "aiohttp.web_exceptions.HTTPMove", "aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPFound.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPGatewayTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPGatewayTimeout", "name": "HTTPGatewayTimeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPGatewayTimeout", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPGatewayTimeout", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPGatewayTimeout.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPGatewayTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPGatewayTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPGone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPGone", "name": "HTTPGone", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPGone", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPGone", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPGone.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPGone.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPGone", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPInsufficientStorage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPInsufficientStorage", "name": "HTTPInsufficientStorage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPInsufficientStorage", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPInsufficientStorage", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPInsufficientStorage.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPInsufficientStorage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPInsufficientStorage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPInternalServerError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPInternalServerError", "name": "HTTPInternalServerError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPInternalServerError", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPInternalServerError", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPInternalServerError.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPInternalServerError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPInternalServerError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPLengthRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPLengthRequired", "name": "HTTPLengthRequired", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPLengthRequired", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPLengthRequired", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPLengthRequired.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPLengthRequired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPLengthRequired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPMethodNotAllowed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPMethodNotAllowed", "name": "HTTPMethodNotAllowed", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPMethodNotAllowed", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPMethodNotAllowed", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "method", "allowed_methods", "headers", "reason", "body", "text", "content_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPMethodNotAllowed.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "method", "allowed_methods", "headers", "reason", "body", "text", "content_type"], "arg_types": ["aiohttp.web_exceptions.HTTPMethodNotAllowed", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPMethodNotAllowed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allowed_methods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.web_exceptions.HTTPMethodNotAllowed.allowed_methods", "name": "allowed_methods", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_exceptions.HTTPMethodNotAllowed.method", "name": "method", "type": "builtins.str"}}, "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPMethodNotAllowed.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPMethodNotAllowed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPMethodNotAllowed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPMisdirectedRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPMisdirectedRequest", "name": "HTTPMisdirectedRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPMisdirectedRequest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPMisdirectedRequest", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPMisdirectedRequest.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPMisdirectedRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPMisdirectedRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPMove": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPRedirection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPMove", "name": "HTTPMove", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPMove", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPMove", "aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "location", "headers", "reason", "body", "text", "content_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPMove.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "location", "headers", "reason", "body", "text", "content_type"], "arg_types": ["aiohttp.web_exceptions.HTTPMove", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPMove", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_exceptions.HTTPMove.location", "name": "location", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPMove.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPMove", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPMovedPermanently": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPMove"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPMovedPermanently", "name": "HTTPMovedPermanently", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPMovedPermanently", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPMovedPermanently", "aiohttp.web_exceptions.HTTPMove", "aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPMovedPermanently.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPMovedPermanently.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPMovedPermanently", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPMultipleChoices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPMove"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPMultipleChoices", "name": "HTTPMultipleChoices", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPMultipleChoices", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPMultipleChoices", "aiohttp.web_exceptions.HTTPMove", "aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPMultipleChoices.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPMultipleChoices.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPMultipleChoices", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPNetworkAuthenticationRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPNetworkAuthenticationRequired", "name": "HTTPNetworkAuthenticationRequired", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPNetworkAuthenticationRequired", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPNetworkAuthenticationRequired", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNetworkAuthenticationRequired.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPNetworkAuthenticationRequired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPNetworkAuthenticationRequired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPNoContent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPSuccessful"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPNoContent", "name": "HTTPNoContent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPNoContent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPNoContent", "aiohttp.web_exceptions.HTTPSuccessful", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "empty_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNoContent.empty_body", "name": "empty_body", "type": "builtins.bool"}}, "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNoContent.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPNoContent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPNoContent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPNonAuthoritativeInformation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPSuccessful"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPNonAuthoritativeInformation", "name": "HTTPNonAuthoritativeInformation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPNonAuthoritativeInformation", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPNonAuthoritativeInformation", "aiohttp.web_exceptions.HTTPSuccessful", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNonAuthoritativeInformation.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPNonAuthoritativeInformation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPNonAuthoritativeInformation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPNotAcceptable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPNotAcceptable", "name": "HTTPNotAcceptable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPNotAcceptable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPNotAcceptable", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNotAcceptable.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPNotAcceptable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPNotAcceptable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPNotExtended": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPNotExtended", "name": "HTTPNotExtended", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPNotExtended", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPNotExtended", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNotExtended.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPNotExtended.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPNotExtended", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPNotFound", "name": "HTTPNotFound", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPNotFound", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPNotFound", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNotFound.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPNotImplemented": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPNotImplemented", "name": "HTTPNotImplemented", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPNotImplemented", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPNotImplemented", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNotImplemented.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPNotImplemented.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPNotImplemented", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPNotModified": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPRedirection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPNotModified", "name": "HTTPNotModified", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPNotModified", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPNotModified", "aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "empty_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNotModified.empty_body", "name": "empty_body", "type": "builtins.bool"}}, "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPNotModified.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPNotModified.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPNotModified", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPOk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPSuccessful"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPOk", "name": "HTTPOk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPOk", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPOk", "aiohttp.web_exceptions.HTTPSuccessful", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPOk.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPOk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPOk", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPPartialContent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPSuccessful"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPPartialContent", "name": "HTTPPartialContent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPPartialContent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPPartialContent", "aiohttp.web_exceptions.HTTPSuccessful", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPPartialContent.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPPartialContent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPPartialContent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPPaymentRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPPaymentRequired", "name": "HTTPPaymentRequired", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPPaymentRequired", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPPaymentRequired", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPPaymentRequired.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPPaymentRequired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPPaymentRequired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPPermanentRedirect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPMove"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPPermanentRedirect", "name": "HTTPPermanentRedirect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPPermanentRedirect", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPPermanentRedirect", "aiohttp.web_exceptions.HTTPMove", "aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPPermanentRedirect.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPPermanentRedirect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPPermanentRedirect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPPreconditionFailed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPPreconditionFailed", "name": "HTTPPreconditionFailed", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPPreconditionFailed", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPPreconditionFailed", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPPreconditionFailed.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPPreconditionFailed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPPreconditionFailed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPPreconditionRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPPreconditionRequired", "name": "HTTPPreconditionRequired", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPPreconditionRequired", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPPreconditionRequired", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPPreconditionRequired.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPPreconditionRequired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPPreconditionRequired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPProxyAuthenticationRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPProxyAuthenticationRequired", "name": "HTTPProxyAuthenticationRequired", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPProxyAuthenticationRequired", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPProxyAuthenticationRequired", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPProxyAuthenticationRequired.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPProxyAuthenticationRequired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPProxyAuthenticationRequired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPRedirection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPRedirection", "name": "HTTPRedirection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPRedirection", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPRedirection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPRedirection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPRequestEntityTooLarge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPRequestEntityTooLarge", "name": "HTTPRequestEntityTooLarge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPRequestEntityTooLarge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPRequestEntityTooLarge", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "max_size", "actual_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPRequestEntityTooLarge.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "max_size", "actual_size", "kwargs"], "arg_types": ["aiohttp.web_exceptions.HTTPRequestEntityTooLarge", "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPRequestEntityTooLarge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPRequestEntityTooLarge.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPRequestEntityTooLarge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPRequestEntityTooLarge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPRequestHeaderFieldsTooLarge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPRequestHeaderFieldsTooLarge", "name": "HTTPRequestHeaderFieldsTooLarge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPRequestHeaderFieldsTooLarge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPRequestHeaderFieldsTooLarge", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPRequestHeaderFieldsTooLarge.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPRequestHeaderFieldsTooLarge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPRequestHeaderFieldsTooLarge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPRequestRangeNotSatisfiable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPRequestRangeNotSatisfiable", "name": "HTTPRequestRangeNotSatisfiable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPRequestRangeNotSatisfiable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPRequestRangeNotSatisfiable", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPRequestRangeNotSatisfiable.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPRequestRangeNotSatisfiable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPRequestRangeNotSatisfiable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPRequestTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPRequestTimeout", "name": "HTTPRequestTimeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPRequestTimeout", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPRequestTimeout", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPRequestTimeout.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPRequestTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPRequestTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPRequestURITooLong": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPRequestURITooLong", "name": "HTTPRequestURITooLong", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPRequestURITooLong", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPRequestURITooLong", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPRequestURITooLong.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPRequestURITooLong.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPRequestURITooLong", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPResetContent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPSuccessful"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPResetContent", "name": "HTTPResetContent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPResetContent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPResetContent", "aiohttp.web_exceptions.HTTPSuccessful", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "empty_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPResetContent.empty_body", "name": "empty_body", "type": "builtins.bool"}}, "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPResetContent.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPResetContent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPResetContent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPSeeOther": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPMove"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPSeeOther", "name": "HTTPSeeOther", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPSeeOther", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPSeeOther", "aiohttp.web_exceptions.HTTPMove", "aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPSeeOther.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPSeeOther.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPSeeOther", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPServerError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPServerError", "name": "HTTPServerError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPServerError", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPServerError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPServerError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPServiceUnavailable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPServiceUnavailable", "name": "HTTPServiceUnavailable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPServiceUnavailable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPServiceUnavailable", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPServiceUnavailable.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPServiceUnavailable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPServiceUnavailable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPSuccessful": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPSuccessful", "name": "HTTPSuccessful", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPSuccessful", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPSuccessful", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPSuccessful.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPSuccessful", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPTemporaryRedirect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPMove"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPTemporaryRedirect", "name": "HTTPTemporaryRedirect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPTemporaryRedirect", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPTemporaryRedirect", "aiohttp.web_exceptions.HTTPMove", "aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPTemporaryRedirect.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPTemporaryRedirect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPTemporaryRedirect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPTooManyRequests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPTooManyRequests", "name": "HTTPTooManyRequests", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPTooManyRequests", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPTooManyRequests", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPTooManyRequests.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPTooManyRequests.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPTooManyRequests", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPUnauthorized": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPUnauthorized", "name": "HTTPUnauthorized", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPUnauthorized", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPUnauthorized", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPUnauthorized.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPUnauthorized.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPUnauthorized", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPUnavailableForLegalReasons": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons", "name": "HTTPUnavailableForLegalReasons", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPUnavailableForLegalReasons", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "link", "headers", "reason", "body", "text", "content_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "link", "headers", "reason", "body", "text", "content_type"], "arg_types": ["aiohttp.web_exceptions.HTTPUnavailableForLegalReasons", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPUnavailableForLegalReasons", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_link": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons._link", "name": "_link", "type": {".class": "UnionType", "items": ["yarl._url.URL", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "link": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons.link", "name": "link", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_exceptions.HTTPUnavailableForLegalReasons"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "link of HTTPUnavailableForLegalReasons", "ret_type": {".class": "UnionType", "items": ["yarl._url.URL", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons.link", "name": "link", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_exceptions.HTTPUnavailableForLegalReasons"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "link of HTTPUnavailableForLegalReasons", "ret_type": {".class": "UnionType", "items": ["yarl._url.URL", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPUnprocessableEntity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPUnprocessableEntity", "name": "HTTPUnprocessableEntity", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPUnprocessableEntity", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPUnprocessableEntity", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPUnprocessableEntity.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPUnprocessableEntity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPUnprocessableEntity", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPUnsupportedMediaType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPUnsupportedMediaType", "name": "HTTPUnsupportedMediaType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPUnsupportedMediaType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPUnsupportedMediaType", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPUnsupportedMediaType.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPUnsupportedMediaType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPUnsupportedMediaType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPUpgradeRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPUpgradeRequired", "name": "HTTPUpgradeRequired", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPUpgradeRequired", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPUpgradeRequired", "aiohttp.web_exceptions.HTTPClientError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPUpgradeRequired.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPUpgradeRequired.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPUpgradeRequired", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPUseProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPMove"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPUseProxy", "name": "HTTPUseProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPUseProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPUseProxy", "aiohttp.web_exceptions.HTTPMove", "aiohttp.web_exceptions.HTTPRedirection", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPUseProxy.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPUseProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPUseProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPVariantAlsoNegotiates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPVariantAlsoNegotiates", "name": "HTTPVariantAlsoNegotiates", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPVariantAlsoNegotiates", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPVariantAlsoNegotiates", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPVariantAlsoNegotiates.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPVariantAlsoNegotiates.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPVariantAlsoNegotiates", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPVersionNotSupported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_exceptions.HTTPServerError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.HTTPVersionNotSupported", "name": "HTTPVersionNotSupported", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.HTTPVersionNotSupported", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.HTTPVersionNotSupported", "aiohttp.web_exceptions.HTTPServerError", "aiohttp.web_exceptions.HTTPError", "aiohttp.web_exceptions.HTTPException", "aiohttp.web_response.Response", "aiohttp.web_response.StreamResponse", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "status_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.HTTPVersionNotSupported.status_code", "name": "status_code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.HTTPVersionNotSupported.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.HTTPVersionNotSupported", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "LooseHeaders": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.LooseHeaders", "kind": "Gdef", "module_public": false}, "NotAppKeyWarning": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.UserWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_exceptions.NotAppKeyWarning", "name": "NotAppKeyWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_exceptions.NotAppKeyWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.web_exceptions", "mro": ["aiohttp.web_exceptions.NotAppKeyWarning", "builtins.UserWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_exceptions.NotAppKeyWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_exceptions.NotAppKeyWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_response.Response", "kind": "Gdef", "module_public": false}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "StrOrURL": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.StrOrURL", "kind": "Gdef", "module_public": false}, "URL": {".class": "SymbolTableNode", "cross_ref": "yarl._url.URL", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_exceptions.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web_exceptions.py"}