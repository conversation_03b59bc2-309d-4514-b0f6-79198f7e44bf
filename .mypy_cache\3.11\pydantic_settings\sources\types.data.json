{".class": "MypyFile", "_fullname": "pydantic_settings.sources.types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_public": false}, "DEFAULT_PATH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic_settings.sources.types.DEFAULT_PATH", "name": "DEFAULT_PATH", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}}}, "DotenvType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_settings.sources.types.DotenvType", "line": 34, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pathlib.Path", "builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["pathlib.Path", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}}}, "ENV_FILE_SENTINEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic_settings.sources.types.ENV_FILE_SENTINEL", "name": "ENV_FILE_SENTINEL", "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.DotenvType"}}}, "EnvNoneType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.types.EnvNoneType", "name": "EnvNoneType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.types.EnvNoneType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_settings.sources.types", "mro": ["pydantic_settings.sources.types.EnvNoneType", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types.EnvNoneType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.types.EnvNoneType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ForceDecode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.types.ForceDecode", "name": "ForceDecode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.types.ForceDecode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_settings.sources.types", "mro": ["pydantic_settings.sources.types.ForceDecode", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types.ForceDecode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.types.ForceDecode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoDecode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.types.NoDecode", "name": "NoDecode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.types.NoDecode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_settings.sources.types", "mro": ["pydantic_settings.sources.types.NoDecode", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types.NoDecode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.types.NoDecode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "PathType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_settings.sources.types.PathType", "line": 35, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pathlib.Path", "builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["pathlib.Path", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}}}, "PydanticDataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._dataclasses.PydanticDataclass", "kind": "Gdef", "module_public": false}, "PydanticModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types.PydanticModel", "name": "PydanticModel", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._dataclasses.PydanticDataclass", "pydantic.main.BaseModel"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_CliExplicitFlag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.types._CliExplicitFlag", "name": "_CliExplicitFlag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.types._CliExplicitFlag", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_settings.sources.types", "mro": ["pydantic_settings.sources.types._CliExplicitFlag", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types._CliExplicitFlag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.types._CliExplicitFlag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CliImplicitFlag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.types._CliImplicitFlag", "name": "_CliImplicitFlag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.types._CliImplicitFlag", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_settings.sources.types", "mro": ["pydantic_settings.sources.types._CliImplicitFlag", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types._CliImplicitFlag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.types._CliImplicitFlag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CliPositionalArg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.types._CliPositionalArg", "name": "_CliPositionalArg", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.types._CliPositionalArg", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_settings.sources.types", "mro": ["pydantic_settings.sources.types._CliPositionalArg", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types._CliPositionalArg.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.types._CliPositionalArg", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CliSubCommand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.types._CliSubCommand", "name": "_CliSubCommand", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.types._CliSubCommand", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_settings.sources.types", "mro": ["pydantic_settings.sources.types._CliSubCommand", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types._CliSubCommand.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.types._CliSubCommand", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CliUnknownArgs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.types._CliUnknownArgs", "name": "_CliUnknownArgs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.types._CliUnknownArgs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_settings.sources.types", "mro": ["pydantic_settings.sources.types._CliUnknownArgs", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types._CliUnknownArgs.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.types._CliUnknownArgs", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic_settings.sources.types.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.types.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.types.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.types.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.types.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.types.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.types.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pydantic_settings\\sources\\types.py"}