{".class": "MypyFile", "_fullname": "urllib3.util.ssltransport", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ProxySchemeUnsupported": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ProxySchemeUnsupported", "kind": "Gdef"}, "SSLTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.util.ssltransport.SSLTransport", "name": "SSLTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.util.ssltransport", "mro": ["urllib3.util.ssltransport.SSLTransport", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.ssltransport.SSLTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.util.ssltransport.SSLTransport", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of SSLTransport", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.ssltransport.SSLTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.util.ssltransport.SSLTransport", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.ssltransport.SSLTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.util.ssltransport.SSLTransport", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "socket", "ssl_context", "server_hostname", "suppress_ragged_eofs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "socket", "ssl_context", "server_hostname", "suppress_ragged_eofs"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", "socket.socket", "ssl.SSLContext", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_decref_socketios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport._decref_socketios", "name": "_decref_socketios", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_decref_socketios of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ssl_io_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "arg1", "arg2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "name": "_ssl_io_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "arg1", "arg2"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.ssltransport._ReturnValue", "id": -1, "name": "_ReturnValue", "namespace": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.bytes", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.ssltransport._ReturnValue", "id": -1, "name": "_ReturnValue", "namespace": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.ssltransport._ReturnValue", "id": -1, "name": "_ReturnValue", "namespace": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "name": "_ssl_io_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "name": "_ssl_io_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "func", "arg1"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "name": "_ssl_io_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "func", "arg1"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "name": "_ssl_io_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "func", "arg1"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "func", "arg1", "arg2"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "name": "_ssl_io_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "func", "arg1", "arg2"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport._ssl_io_loop", "name": "_ssl_io_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "func", "arg1", "arg2"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "func", "arg1"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "func", "arg1", "arg2"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_io_loop of SSLTransport", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_validate_ssl_context_for_tls_in_tls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ssl_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "urllib3.util.ssltransport.SSLTransport._validate_ssl_context_for_tls_in_tls", "name": "_validate_ssl_context_for_tls_in_tls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ssl_context"], "arg_types": ["ssl.SSLContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_ssl_context_for_tls_in_tls of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport._validate_ssl_context_for_tls_in_tls", "name": "_validate_ssl_context_for_tls_in_tls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ssl_context"], "arg_types": ["ssl.SSLContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_ssl_context_for_tls_in_tls of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_wrap_ssl_read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "len", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport._wrap_ssl_read", "name": "_wrap_ssl_read", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "len", "buffer"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", "builtins.int", {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap_ssl_read of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cipher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.cipher", "name": "cipher", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cipher of SSLTransport", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.compression", "name": "compression", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compression of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fileno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.fileno", "name": "fileno", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fileno of SSLTransport", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getpeercert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.getpeercert", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "urllib3.util.ssltransport.SSLTransport.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpe<PERSON><PERSON>t of SSLTransport", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "urllib3.util.ssltransport.SSLTransport.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpe<PERSON><PERSON>t of SSLTransport", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpe<PERSON><PERSON>t of SSLTransport", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "urllib3.util.ssltransport.SSLTransport.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpe<PERSON><PERSON>t of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport.getpeercert", "name": "getpeercert", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpe<PERSON><PERSON>t of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "binary_form"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpe<PERSON><PERSON>t of SSLTransport", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "binary_form"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getpe<PERSON><PERSON>t of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "gettimeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.gettimeout", "name": "gettimeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gettimeout of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "incoming": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport.incoming", "name": "incoming", "type": "_ssl.MemoryBIO"}}, "makefile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.makefile", "name": "makefile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5], "arg_names": ["self", "mode", "buffering", "encoding", "errors", "newline"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makefile of SSLTransport", "ret_type": {".class": "UnionType", "items": ["typing.BinaryIO", "typing.TextIO", "socket.SocketIO"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "outgoing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport.outgoing", "name": "outgoing", "type": "_ssl.MemoryBIO"}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "len", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "len", "buffer"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", "builtins.int", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "buflen", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.recv", "name": "recv", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "buflen", "flags"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recv of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "recv_into": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.recv_into", "name": "recv_into", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "buffer", "nbytes", "flags"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssltransport._WriteBuffer"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recv_into of SSLTransport", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selected_alpn_protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.selected_alpn_protocol", "name": "selected_alpn_protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selected_alpn_protocol of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "flags"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", "builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of SSLTransport", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sendall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.sendall", "name": "sendall", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "flags"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", "builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sendall of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "settimeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.settimeout", "name": "settimeout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["urllib3.util.ssltransport.SSLTransport", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "settimeout of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shared_ciphers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.shared_ciphers", "name": "shared_ciphers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "shared_ciphers of SSLTransport", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport.socket", "name": "socket", "type": "socket.socket"}}, "sslobj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport.sslobj", "name": "sslobj", "type": "ssl.SSLObject"}}, "suppress_ragged_eofs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.util.ssltransport.SSLTransport.suppress_ragged_eofs", "name": "suppress_ragged_eofs", "type": "builtins.bool"}}, "unwrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.unwrap", "name": "unwrap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unwrap of SSLTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssltransport.SSLTransport.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.ssltransport.SSLTransport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of SSLTransport", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.ssltransport.SSLTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.util.ssltransport.SSLTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSL_BLOCKSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssltransport.SSL_BLOCKSIZE", "name": "SSL_BLOCKSIZE", "type": "builtins.int"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "_ReturnValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.ssltransport._ReturnValue", "name": "_ReturnValue", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_TYPE_PEER_CERT_RET": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET", "kind": "Gdef"}, "_TYPE_PEER_CERT_RET_DICT": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT", "kind": "Gdef"}, "_WriteBuffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3.util.ssltransport._WriteBuffer", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.bytearray", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "uses_pep604_syntax": false}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssltransport.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssltransport.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssltransport.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssltransport.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssltransport.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssltransport.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\urllib3\\util\\ssltransport.py"}