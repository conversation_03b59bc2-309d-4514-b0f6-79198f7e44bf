{".class": "MypyFile", "_fullname": "opentelemetry.propagate", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "OTEL_PROPAGATORS": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.environment_variables.OTEL_PROPAGATORS", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "_HTTP_TEXT_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "opentelemetry.propagate._HTTP_TEXT_FORMAT", "name": "_HTTP_TEXT_FORMAT", "type": "opentelemetry.propagators.textmap.TextMapPropagator"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagate.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagate.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagate.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagate.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagate.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagate.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagate.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "composite": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.propagators.composite", "kind": "Gdef"}, "entry_points": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.entry_points", "kind": "Gdef"}, "environ": {".class": "SymbolTableNode", "cross_ref": "os.environ", "kind": "Gdef"}, "environ_propagators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.propagate.environ_propagators", "name": "environ_propagators", "type": "builtins.str"}}, "extract": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["carrier", "context", "getter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagate.extract", "name": "extract", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["carrier", "context", "getter"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagate.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagate.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract", "ret_type": "opentelemetry.context.context.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagate.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "getLogger": {".class": "SymbolTableNode", "cross_ref": "logging.getLogger", "kind": "Gdef"}, "get_global_textmap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagate.get_global_textmap", "name": "get_global_textmap", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_global_textmap", "ret_type": "opentelemetry.propagators.textmap.TextMapPropagator", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["carrier", "context", "setter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagate.inject", "name": "inject", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["carrier", "context", "setter"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagate.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagate.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Setter"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagate.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.propagate.logger", "name": "logger", "type": "logging.Logger"}}, "propagator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "opentelemetry.propagate.propagator", "name": "propagator", "type": "builtins.str"}}, "propagators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "opentelemetry.propagate.propagators", "name": "propagators", "type": {".class": "Instance", "args": ["opentelemetry.propagators.textmap.TextMapPropagator"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "set_global_textmap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["http_text_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagate.set_global_textmap", "name": "set_global_textmap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["http_text_format"], "arg_types": ["opentelemetry.propagators.textmap.TextMapPropagator"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_global_textmap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "textmap": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.propagators.textmap", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\propagate\\__init__.py"}