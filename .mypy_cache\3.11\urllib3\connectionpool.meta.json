{"data_mtime": 1753366016, "dep_lines": [43, 44, 45, 46, 47, 48, 49, 52, 13, 14, 15, 16, 27, 42, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 55, 57, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["urllib3.util.connection", "urllib3.util.proxy", "urllib3.util.request", "urllib3.util.retry", "urllib3.util.ssl_match_hostname", "urllib3.util.timeout", "urllib3.util.url", "urllib3.util.util", "urllib3._base_connection", "urllib3._collections", "urllib3._request_methods", "urllib3.connection", "urllib3.exceptions", "urllib3.response", "__future__", "errno", "logging", "queue", "sys", "typing", "warnings", "weakref", "socket", "types", "ssl", "typing_extensions", "builtins", "_frozen_importlib", "_io", "_ssl", "abc", "enum", "http", "http.client", "io", "urllib3.util"], "hash": "31e5579519ba1afd0d0e00b15b02c84ec12554a0", "id": "urllib3.connectionpool", "ignore_all": true, "interface_hash": "1903a692dbefe893b9820df7cdbddd522c620d7d", "mtime": 1750470649, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\urllib3\\connectionpool.py", "plugin_data": null, "size": 43371, "suppressed": [], "version_id": "1.15.0"}