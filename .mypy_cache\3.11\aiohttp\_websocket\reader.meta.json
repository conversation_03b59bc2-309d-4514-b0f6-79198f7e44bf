{"data_mtime": 1753366020, "dep_lines": [8, 5, 3, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["aiohttp._websocket.reader_py", "aiohttp.helpers", "typing", "builtins", "_frozen_importlib", "abc", "aiohttp.base_protocol", "asyncio", "asyncio.events", "asyncio.protocols"], "hash": "869690b4e42f306c2117bea080ac88bcefb86d28", "id": "aiohttp._websocket.reader", "ignore_all": true, "interface_hash": "68eb9461c253040b7e2ba7ab785f24507698ef08", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\_websocket\\reader.py", "plugin_data": null, "size": 1061, "suppressed": [], "version_id": "1.15.0"}