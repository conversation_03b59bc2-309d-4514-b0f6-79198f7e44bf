{".class": "MypyFile", "_fullname": "aiohttp.multipart", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BadContentDispositionHeader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.multipart.BadContentDispositionHeader", "name": "BadContentDispositionHeader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BadContentDispositionHeader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.multipart", "mro": ["aiohttp.multipart.BadContentDispositionHeader", "builtins.RuntimeWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.BadContentDispositionHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.BadContentDispositionHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadContentDispositionParam": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.multipart.BadContentDispositionParam", "name": "BadContentDispositionParam", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BadContentDispositionParam", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.multipart", "mro": ["aiohttp.multipart.BadContentDispositionParam", "builtins.RuntimeWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.BadContentDispositionParam.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.BadContentDispositionParam", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BodyPartReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.multipart.BodyPartReader", "name": "Body<PERSON>art<PERSON>eader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.multipart", "mro": ["aiohttp.multipart.BodyPartReader", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReader.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.BodyPartReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.BodyPartReader", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of BodyPartReader", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.BodyPartReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.BodyPartReader", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.BodyPartReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.BodyPartReader", "values": [], "variance": 0}]}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.BodyPartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__anext__ of BodyPartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5], "arg_names": ["self", "boundary", "headers", "content", "subtype", "default_charset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5], "arg_names": ["self", "boundary", "headers", "content", "subtype", "default_charset"], "arg_types": ["aiohttp.multipart.BodyPartReader", "builtins.bytes", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, "aiohttp.streams.StreamReader", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BodyPartReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_at_eof": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._at_eof", "name": "_at_eof", "type": "builtins.bool"}}, "_boundary": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._boundary", "name": "_boundary", "type": "builtins.bytes"}}, "_boundary_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._boundary_len", "name": "_boundary_len", "type": "builtins.int"}}, "_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._cache", "name": "_cache", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._content", "name": "_content", "type": "aiohttp.streams.StreamReader"}}, "_content_eof": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._content_eof", "name": "_content_eof", "type": "builtins.int"}}, "_decode_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReader._decode_content", "name": "_decode_content", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["aiohttp.multipart.BodyPartReader", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_decode_content of BodyPartReader", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_decode_content_transfer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReader._decode_content_transfer", "name": "_decode_content_transfer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["aiohttp.multipart.BodyPartReader", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_decode_content_transfer of BodyPartReader", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_default_charset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._default_charset", "name": "_default_charset", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_is_form_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._is_form_data", "name": "_is_form_data", "type": "builtins.bool"}}, "_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._length", "name": "_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_prev_chunk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._prev_chunk", "name": "_prev_chunk", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_read_bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._read_bytes", "name": "_read_bytes", "type": "builtins.int"}}, "_read_chunk_from_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader._read_chunk_from_length", "name": "_read_chunk_from_length", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "size"], "arg_types": ["aiohttp.multipart.BodyPartReader", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_chunk_from_length of BodyPartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read_chunk_from_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader._read_chunk_from_stream", "name": "_read_chunk_from_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "size"], "arg_types": ["aiohttp.multipart.BodyPartReader", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_chunk_from_stream of BodyPartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unread": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader._unread", "name": "_unread", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "collections.deque"}}}, "at_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReader.at_eof", "name": "at_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.BodyPartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at_eof of BodyPartReader", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chunk_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.multipart.BodyPartReader.chunk_size", "name": "chunk_size", "type": "builtins.int"}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReader.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["aiohttp.multipart.BodyPartReader", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of BodyPartReader", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.multipart.BodyPartReader.filename", "name": "filename", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.BodyPartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filename of BodyPartReader", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader.filename", "name": "filename", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader.form", "name": "form", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "encoding"], "arg_types": ["aiohttp.multipart.BodyPartReader", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "form of BodyPartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_charset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReader.get_charset", "name": "get_charset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default"], "arg_types": ["aiohttp.multipart.BodyPartReader", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_charset of BodyPartReader", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}}}, "json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader.json", "name": "json", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "encoding"], "arg_types": ["aiohttp.multipart.BodyPartReader", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json of BodyPartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.multipart.BodyPartReader.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.BodyPartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.BodyPartReader.name", "name": "name", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader.next", "name": "next", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.BodyPartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "decode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "decode"], "arg_types": ["aiohttp.multipart.BodyPartReader", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader.read_chunk", "name": "read_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "size"], "arg_types": ["aiohttp.multipart.BodyPartReader", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_chunk of BodyPartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader.readline", "name": "readline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.BodyPartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "readline of BodyPartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.BodyPartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of BodyPartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReader.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "encoding"], "arg_types": ["aiohttp.multipart.BodyPartReader", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.BodyPartReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.BodyPartReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BodyPartReaderPayload": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.payload.Payload"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.multipart.BodyPartReaderPayload", "name": "BodyPartReaderPayload", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReaderPayload", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.multipart", "mro": ["aiohttp.multipart.BodyPartReaderPayload", "aiohttp.payload.Payload", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "value", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReaderPayload.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "value", "args", "kwargs"], "arg_types": ["aiohttp.multipart.BodyPartReaderPayload", "aiohttp.multipart.BodyPartReader", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BodyPartReaderPayload", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.multipart.BodyPartReaderPayload._value", "name": "_value", "type": "aiohttp.multipart.BodyPartReader"}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.BodyPartReaderPayload.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "errors"], "arg_types": ["aiohttp.multipart.BodyPartReaderPayload", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of BodyPartReaderPayload", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "writer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.BodyPartReaderPayload.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "writer"], "arg_types": ["aiohttp.multipart.BodyPartReaderPayload", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of BodyPartReaderPayload", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.BodyPartReaderPayload.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.BodyPartReaderPayload", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.CHAR", "kind": "Gdef", "module_public": false}, "CIMultiDict": {".class": "SymbolTableNode", "cross_ref": "multidict.CIMultiDict", "kind": "Gdef", "module_public": false}, "CIMultiDictProxy": {".class": "SymbolTableNode", "cross_ref": "multidict.CIMultiDictProxy", "kind": "Gdef", "module_public": false}, "CONTENT_DISPOSITION": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs.CONTENT_DISPOSITION", "kind": "Gdef", "module_public": false}, "CONTENT_ENCODING": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs.CONTENT_ENCODING", "kind": "Gdef", "module_public": false}, "CONTENT_LENGTH": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs.CONTENT_LENGTH", "kind": "Gdef", "module_public": false}, "CONTENT_TRANSFER_ENCODING": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs.CONTENT_TRANSFER_ENCODING", "kind": "Gdef", "module_public": false}, "CONTENT_TYPE": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs.CONTENT_TYPE", "kind": "Gdef", "module_public": false}, "ClientResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.ClientResponse", "kind": "Gdef", "module_public": false}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "HeadersParser": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.HeadersParser", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "JsonPayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.JsonPayload", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "LookupError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.LookupError", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MultipartPayloadWriter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.multipart.MultipartPayloadWriter", "name": "MultipartPayloadWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartPayloadWriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.multipart", "mro": ["aiohttp.multipart.MultipartPayloadWriter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "writer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartPayloadWriter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "writer"], "arg_types": ["aiohttp.multipart.MultipartPayloadWriter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultipartPayloadWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartPayloadWriter._compress", "name": "_compress", "type": {".class": "UnionType", "items": ["aiohttp.compression_utils.ZLibCompressor", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartPayloadWriter._encoding", "name": "_encoding", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_encoding_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartPayloadWriter._encoding_buffer", "name": "_encoding_buffer", "type": {".class": "UnionType", "items": ["builtins.bytearray", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartPayloadWriter._writer", "name": "_writer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "enable_compression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartPayloadWriter.enable_compression", "name": "enable_compression", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "strategy"], "arg_types": ["aiohttp.multipart.MultipartPayloadWriter", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_compression of MultipartPayloadWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartPayloadWriter.enable_encoding", "name": "enable_encoding", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "encoding"], "arg_types": ["aiohttp.multipart.MultipartPayloadWriter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_encoding of MultipartPayloadWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartPayloadWriter.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": ["aiohttp.multipart.MultipartPayloadWriter", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of MultipartPayloadWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartPayloadWriter.write_eof", "name": "write_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartPayloadWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_eof of MultipartPayloadWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.MultipartPayloadWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.MultipartPayloadWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipartReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.multipart.MultipartReader", "name": "MultipartReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.multipart", "mro": ["aiohttp.multipart.MultipartReader", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartReader.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.MultipartReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.MultipartReader", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of MultipartReader", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.MultipartReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.MultipartReader", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.MultipartReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.MultipartReader", "values": [], "variance": 0}]}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartReader.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__anext__ of MultipartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["aiohttp.multipart.MultipartReader", "aiohttp.multipart.BodyPartReader", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartReader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "content"], "arg_types": ["aiohttp.multipart.MultipartReader", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "aiohttp.streams.StreamReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultipartReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_at_bof": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartReader._at_bof", "name": "_at_bof", "type": "builtins.bool"}}, "_at_eof": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartReader._at_eof", "name": "_at_eof", "type": "builtins.bool"}}, "_boundary": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartReader._boundary", "name": "_boundary", "type": "builtins.bytes"}}, "_content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartReader._content", "name": "_content", "type": "aiohttp.streams.StreamReader"}}, "_default_charset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartReader._default_charset", "name": "_default_charset", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_get_boundary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartReader._get_boundary", "name": "_get_boundary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_boundary of MultipartReader", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_part_reader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartReader._get_part_reader", "name": "_get_part_reader", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["aiohttp.multipart.MultipartReader", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_part_reader of MultipartReader", "ret_type": {".class": "UnionType", "items": ["aiohttp.multipart.MultipartReader", "aiohttp.multipart.BodyPartReader"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_last_part": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartReader._last_part", "name": "_last_part", "type": {".class": "UnionType", "items": ["aiohttp.multipart.MultipartReader", "aiohttp.multipart.BodyPartReader", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_maybe_release_last_part": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartReader._maybe_release_last_part", "name": "_maybe_release_last_part", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_maybe_release_last_part of MultipartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mimetype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartReader._mimetype", "name": "_mimetype", "type": "aiohttp.helpers.MimeType"}}, "_read_boundary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartReader._read_boundary", "name": "_read_boundary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_boundary of MultipartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartReader._read_headers", "name": "_read_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_headers of MultipartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read_until_first_boundary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartReader._read_until_first_boundary", "name": "_read_until_first_boundary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_until_first_boundary of MultipartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_readline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartReader._readline", "name": "_readline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_readline of MultipartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unread": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartReader._unread", "name": "_unread", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "at_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartReader.at_eof", "name": "at_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at_eof of MultipartReader", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetch_next_part": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartReader.fetch_next_part", "name": "fetch_next_part", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fetch_next_part of MultipartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["aiohttp.multipart.MultipartReader", "aiohttp.multipart.BodyPartReader"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "aiohttp.multipart.MultipartReader.from_response", "name": "from_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "arg_types": [{".class": "TypeType", "item": "aiohttp.multipart.MultipartReader"}, "aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_response of MultipartReader", "ret_type": "aiohttp.multipart.MultipartResponseWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartReader.from_response", "name": "from_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response"], "arg_types": [{".class": "TypeType", "item": "aiohttp.multipart.MultipartReader"}, "aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_response of MultipartReader", "ret_type": "aiohttp.multipart.MultipartResponseWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartReader.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "multipart_reader_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "aiohttp.multipart.MultipartReader.multipart_reader_cls", "name": "multipart_reader_cls", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "aiohttp.multipart.MultipartReader"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartReader.next", "name": "next", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next of MultipartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["aiohttp.multipart.MultipartReader", "aiohttp.multipart.BodyPartReader", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "part_reader_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.multipart.MultipartReader.part_reader_cls", "name": "part_reader_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["boundary", "headers", "content", "subtype", "default_charset"], "arg_types": ["builtins.bytes", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, "aiohttp.streams.StreamReader", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["aiohttp.multipart.BodyPartReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "aiohttp.multipart.BodyPartReader", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartReader.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of MultipartReader", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response_wrapper_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.multipart.MultipartReader.response_wrapper_cls", "name": "response_wrapper_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["resp", "stream"], "arg_types": ["aiohttp.client_reqrep.ClientResponse", "aiohttp.multipart.MultipartReader"], "bound_args": ["aiohttp.multipart.MultipartResponseWrapper"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "aiohttp.multipart.MultipartResponseWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.MultipartReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.MultipartReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipartResponseWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.multipart.MultipartResponseWrapper", "name": "MultipartResponseWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartResponseWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.multipart", "mro": ["aiohttp.multipart.MultipartResponseWrapper", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartResponseWrapper.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartResponseWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of MultipartResponseWrapper", "ret_type": "aiohttp.multipart.MultipartResponseWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartResponseWrapper.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartResponseWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__anext__ of MultipartResponseWrapper", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["aiohttp.multipart.MultipartReader", "aiohttp.multipart.BodyPartReader"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resp", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartResponseWrapper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resp", "stream"], "arg_types": ["aiohttp.multipart.MultipartResponseWrapper", "aiohttp.client_reqrep.ClientResponse", "aiohttp.multipart.MultipartReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultipartResponseWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "at_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartResponseWrapper.at_eof", "name": "at_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartResponseWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "at_eof of MultipartResponseWrapper", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartResponseWrapper.next", "name": "next", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartResponseWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next of MultipartResponseWrapper", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["aiohttp.multipart.MultipartReader", "aiohttp.multipart.BodyPartReader", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartResponseWrapper.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartResponseWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of MultipartResponseWrapper", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartResponseWrapper.resp", "name": "resp", "type": "aiohttp.client_reqrep.ClientResponse"}}, "stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartResponseWrapper.stream", "name": "stream", "type": "aiohttp.multipart.MultipartReader"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.MultipartResponseWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.MultipartResponseWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipartWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.payload.Payload"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.multipart.MultipartWriter", "name": "MultipartWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.multipart", "mro": ["aiohttp.multipart.MultipartWriter", "aiohttp.payload.Payload", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of MultipartWriter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of MultipartWriter", "ret_type": "aiohttp.multipart.MultipartWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["aiohttp.multipart.MultipartWriter", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of MultipartWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "subtype", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "subtype", "boundary"], "arg_types": ["aiohttp.multipart.MultipartWriter", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultipartWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of MultipartWriter", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.multipart._Part"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of MultipartWriter", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_boundary": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartWriter._boundary", "name": "_boundary", "type": "builtins.bytes"}}, "_boundary_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.multipart.MultipartWriter._boundary_value", "name": "_boundary_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_boundary_value of MultipartWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartWriter._boundary_value", "name": "_boundary_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_boundary_value of MultipartWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_invalid_qdtext_char_regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.multipart.MultipartWriter._invalid_qdtext_char_regex", "name": "_invalid_qdtext_char_regex", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_is_form_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.multipart.MultipartWriter._is_form_data", "name": "_is_form_data", "type": "builtins.bool"}}, "_parts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartWriter._parts", "name": "_parts", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.multipart._Part"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_valid_tchar_regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.multipart.MultipartWriter._valid_tchar_regex", "name": "_valid_tchar_regex", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aiohttp.multipart.MultipartWriter._value", "name": "_value", "type": {".class": "NoneType"}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "headers"], "arg_types": ["aiohttp.multipart.MultipartWriter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of MultipartWriter", "ret_type": "aiohttp.payload.Payload", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.append_form", "name": "append_form", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "headers"], "arg_types": ["aiohttp.multipart.MultipartWriter", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_form of MultipartWriter", "ret_type": "aiohttp.payload.Payload", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.append_json", "name": "append_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "headers"], "arg_types": ["aiohttp.multipart.MultipartWriter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_json of MultipartWriter", "ret_type": "aiohttp.payload.Payload", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append_payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.append_payload", "name": "append_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "payload"], "arg_types": ["aiohttp.multipart.MultipartWriter", "aiohttp.payload.Payload"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_payload of MultipartWriter", "ret_type": "aiohttp.payload.Payload", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "boundary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.multipart.MultipartWriter.boundary", "name": "boundary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundary of MultipartWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartWriter.boundary", "name": "boundary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "boundary of MultipartWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.MultipartWriter.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "errors"], "arg_types": ["aiohttp.multipart.MultipartWriter", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of MultipartWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.multipart.MultipartWriter.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of MultipartWriter", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.multipart.MultipartWriter.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.multipart.MultipartWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of MultipartWriter", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "writer", "close_boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.multipart.MultipartWriter.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "writer", "close_boundary"], "arg_types": ["aiohttp.multipart.MultipartWriter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of MultipartWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.multipart.MultipartWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.multipart.MultipartWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Order": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.Order", "kind": "Gdef", "module_public": false}, "Payload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.Payload", "kind": "Gdef", "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "StreamReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.StreamReader", "kind": "Gdef", "module_public": false}, "StringPayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.StringPayload", "kind": "Gdef", "module_public": false}, "TOKEN": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.TOKEN", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "ZLibCompressor": {".class": "SymbolTableNode", "cross_ref": "aiohttp.compression_utils.ZLibCompressor", "kind": "Gdef", "module_public": false}, "ZLibDecompressor": {".class": "SymbolTableNode", "cross_ref": "aiohttp.compression_utils.ZLibDecompressor", "kind": "Gdef", "module_public": false}, "_Part": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.multipart._Part", "line": 790, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["aiohttp.payload.Payload", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.multipart.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.multipart.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.multipart.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.multipart.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.multipart.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.multipart.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.multipart.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef", "module_public": false}, "binascii": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "content_disposition_filename": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["params", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.content_disposition_filename", "name": "content_disposition_filename", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["params", "name"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "content_disposition_filename", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef", "module_public": false}, "get_payload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.get_payload", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "parse_content_disposition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.multipart.parse_content_disposition", "name": "parse_content_disposition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["header"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_content_disposition", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_mimetype": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.parse_mimetype", "kind": "Gdef", "module_public": false}, "parse_qsl": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qsl", "kind": "Gdef", "module_public": false}, "payload_type": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.payload_type", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "reify": {".class": "SymbolTableNode", "cross_ref": "propcache._helpers.under_cached_property", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef", "module_public": false}, "urlencode": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlencode", "kind": "Gdef", "module_public": false}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}, "zlib": {".class": "SymbolTableNode", "cross_ref": "zlib", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\multipart.py"}