{".class": "MypyFile", "_fullname": "opentelemetry.metrics._internal.observation", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Attributes": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.util.types.Attributes", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef"}, "Observation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.observation.Observation", "name": "Observation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.observation.Observation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "opentelemetry.metrics._internal.observation", "mro": ["opentelemetry.metrics._internal.observation.Observation", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.observation.Observation.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["opentelemetry.metrics._internal.observation.Observation", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Observation", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "value", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.observation.Observation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "value", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.observation.Observation", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Observation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.observation.Observation.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["opentelemetry.metrics._internal.observation.Observation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Observation", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.observation.Observation._attributes", "name": "_attributes", "type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}}}, "_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.observation.Observation._context", "name": "_context", "type": {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.observation.Observation._value", "name": "_value", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.metrics._internal.observation.Observation.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.observation.Observation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of Observation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.observation.Observation.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.observation.Observation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of Observation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.metrics._internal.observation.Observation.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.observation.Observation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of Observation", "ret_type": {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.observation.Observation.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.observation.Observation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of Observation", "ret_type": {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.metrics._internal.observation.Observation.value", "name": "value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.observation.Observation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "value of Observation", "ret_type": {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.observation.Observation.value", "name": "value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.metrics._internal.observation.Observation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "value of Observation", "ret_type": {".class": "UnionType", "items": ["builtins.float", "builtins.int"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.observation.Observation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.observation.Observation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.observation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.observation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.observation.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.observation.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.observation.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.observation.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\metrics\\_internal\\observation.py"}