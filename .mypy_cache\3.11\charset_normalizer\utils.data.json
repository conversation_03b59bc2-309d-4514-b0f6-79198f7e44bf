{".class": "MypyFile", "_fullname": "charset_normalizer.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ENCODING_MARKS": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.ENCODING_MARKS", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "IANA_SUPPORTED_SIMILAR": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.IANA_SUPPORTED_SIMILAR", "kind": "Gdef"}, "IncrementalDecoder": {".class": "SymbolTableNode", "cross_ref": "codecs.IncrementalDecoder", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MultibyteIncrementalDecoder": {".class": "SymbolTableNode", "cross_ref": "_multibytecodec.MultibyteIncrementalDecoder", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RE_POSSIBLE_ENCODING_INDICATION": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.RE_POSSIBLE_ENCODING_INDICATION", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "UNICODE_RANGES_COMBINED": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.UNICODE_RANGES_COMBINED", "kind": "Gdef"}, "UNICODE_SECONDARY_RANGE_KEYWORD": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.UNICODE_SECONDARY_RANGE_KEYWORD", "kind": "Gdef"}, "UTF8_MAXIMAL_ALLOCATION": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.UTF8_MAXIMAL_ALLOCATION", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aliases": {".class": "SymbolTableNode", "cross_ref": "encodings.aliases.aliases", "kind": "Gdef"}, "any_specified_encoding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["sequence", "search_zone"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.any_specified_encoding", "name": "any_specified_encoding", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["sequence", "search_zone"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "any_specified_encoding", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cp_similarity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["iana_name_a", "iana_name_b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.cp_similarity", "name": "cp_similarity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["iana_name_a", "iana_name_b"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cp_similarity", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cut_sequence_chunks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["sequences", "encoding_iana", "offsets", "chunk_size", "bom_or_sig_available", "strip_sig_or_bom", "sig_payload", "is_multi_byte_decoder", "decoded_payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.cut_sequence_chunks", "name": "cut_sequence_chunks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["sequences", "encoding_iana", "offsets", "chunk_size", "bom_or_sig_available", "strip_sig_or_bom", "sig_payload", "is_multi_byte_decoder", "decoded_payload"], "arg_types": ["builtins.bytes", "builtins.str", "builtins.range", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bytes", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cut_sequence_chunks", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "findall": {".class": "SymbolTableNode", "cross_ref": "re.findall", "kind": "Gdef"}, "iana_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cp_name", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.iana_name", "name": "iana_name", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cp_name", "strict"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iana_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "identify_sig_or_bom": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sequence"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.identify_sig_or_bom", "name": "identify_sig_or_bom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sequence"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identify_sig_or_bom", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "is_accentuated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_accentuated", "name": "is_accentuated", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_accentuated", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_accentuated", "name": "is_accentuated", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_ascii": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_ascii", "name": "is_ascii", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_ascii", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_ascii", "name": "is_ascii", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_case_variable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_case_variable", "name": "is_case_variable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_case_variable", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_case_variable", "name": "is_case_variable", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_cjk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_cjk", "name": "is_cjk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_cjk", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_cjk", "name": "is_cjk", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_cp_similar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["iana_name_a", "iana_name_b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.is_cp_similar", "name": "is_cp_similar", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["iana_name_a", "iana_name_b"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_cp_similar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_emoticon": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_emoticon", "name": "is_emoticon", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_emoticon", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_emoticon", "name": "is_emoticon", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_hangul": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_hangul", "name": "is_hangul", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_hangul", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_hangul", "name": "is_hangul", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_hiragana": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_hiragana", "name": "is_hiragana", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_hiragana", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_hiragana", "name": "is_hiragana", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_katakana": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_katakana", "name": "is_katakana", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_katakana", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_katakana", "name": "is_katakana", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_latin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_latin", "name": "is_latin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_latin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_latin", "name": "is_latin", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_multi_byte_encoding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_multi_byte_encoding", "name": "is_multi_byte_encoding", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multi_byte_encoding", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_multi_byte_encoding", "name": "is_multi_byte_encoding", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_private_use_only": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.is_private_use_only", "name": "is_private_use_only", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_private_use_only", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_punctuation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_punctuation", "name": "is_punctuation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_punctuation", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_punctuation", "name": "is_punctuation", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_separator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_separator", "name": "is_separator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_separator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_separator", "name": "is_separator", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_symbol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_symbol", "name": "is_symbol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_symbol", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_symbol", "name": "is_symbol", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_thai": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_thai", "name": "is_thai", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_thai", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_thai", "name": "is_thai", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_unicode_range_secondary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["range_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_unicode_range_secondary", "name": "is_unicode_range_secondary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["range_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unicode_range_secondary", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_unicode_range_secondary", "name": "is_unicode_range_secondary", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_unprintable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.is_unprintable", "name": "is_unprintable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unprintable", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.is_unprintable", "name": "is_unprintable", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "range_scan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["decoded_sequence"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.range_scan", "name": "range_scan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["decoded_sequence"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "range_scan", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_accent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.remove_accent", "name": "remove_accent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_accent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.remove_accent", "name": "remove_accent", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "set_logging_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["name", "level", "format_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.set_logging_handler", "name": "set_logging_handler", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["name", "level", "format_string"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_logging_handler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_strip_sig_or_bom": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iana_encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.utils.should_strip_sig_or_bom", "name": "should_strip_sig_or_bom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iana_encoding"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_strip_sig_or_bom", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unicode_range": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["character"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.utils.unicode_range", "name": "unicode_range", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["character"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_range", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.utils.unicode_range", "name": "unicode_range", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "unicodedata": {".class": "SymbolTableNode", "cross_ref": "unicodedata", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\charset_normalizer\\utils.py"}