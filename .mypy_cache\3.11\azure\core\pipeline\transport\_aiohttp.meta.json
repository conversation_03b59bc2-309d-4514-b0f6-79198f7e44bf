{"data_mtime": 1753366021, "dep_lines": [57, 58, 59, 63, 64, 73, 49, 50, 55, 69, 40, 46, 26, 27, 28, 39, 42, 43, 44, 45, 47, 173, 416, 520, 518, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 515], "dep_prios": [5, 5, 5, 5, 5, 20, 5, 5, 5, 25, 5, 10, 5, 10, 5, 5, 10, 10, 10, 10, 5, 20, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["azure.core.pipeline.transport._base", "azure.core.pipeline.transport._base_async", "azure.core.utils._pipeline_transport_rest_shared", "azure.core.pipeline._tools", "azure.core.pipeline._tools_async", "azure.core.rest._aiohttp", "azure.core.configuration", "azure.core.exceptions", "azure.core.pipeline", "azure.core.rest", "collections.abc", "aiohttp.client_exceptions", "__future__", "sys", "typing", "types", "logging", "asyncio", "codecs", "aiohttp", "multidict", "ssl", "zlib", "charset_normalizer", "chardet", "builtins", "_frozen_importlib", "_typeshed", "abc", "aiohttp.abc", "aiohttp.client", "aiohttp.client_reqrep", "aiohttp.client_ws", "aiohttp.connector", "aiohttp.cookiejar", "aiohttp.helpers", "aiohttp.http_writer", "aiohttp.tracing", "asyncio.events", "azure.core.pipeline._base_async", "azure.core.rest._helpers", "azure.core.rest._http_response_impl", "azure.core.rest._http_response_impl_async", "azure.core.rest._rest_py3", "contextlib", "enum", "http", "http.cookies", "typing_extensions", "yarl", "yarl._url"], "hash": "aaefba2a85811763775194a5c27b2779b650cb75", "id": "azure.core.pipeline.transport._aiohttp", "ignore_all": true, "interface_hash": "6db78135719a20c7e48c44f53d35d8997b41656a", "mtime": 1750470991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\transport\\_aiohttp.py", "plugin_data": null, "size": 22602, "suppressed": ["cchardet"], "version_id": "1.15.0"}