{"data_mtime": 1753366014, "dep_lines": [22, 23, 24, 17, 22, 17, 18, 19, 20, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 20, 20, 10, 10, 10, 5, 30, 30, 30], "dependencies": ["google.auth._helpers", "google.auth.exceptions", "google.auth.transport", "http.client", "google.auth", "http", "logging", "socket", "urllib", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "73fed5ed0f6c351f5859f9e2023d7c22a50e7df9", "id": "google.auth.transport._http_client", "ignore_all": true, "interface_hash": "e38b8394838cf30ee3dc2f6e630c7d489c9bda15", "mtime": 1750258720, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\google\\auth\\transport\\_http_client.py", "plugin_data": null, "size": 3798, "suppressed": [], "version_id": "1.15.0"}