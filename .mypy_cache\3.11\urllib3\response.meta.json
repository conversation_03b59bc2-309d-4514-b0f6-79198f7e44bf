{"data_mtime": 1753366016, "dep_lines": [45, 46, 14, 19, 29, 31, 32, 33, 49, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 29, 177, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25, 23, 151], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 5, 10, 10, 10, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 5], "dependencies": ["urllib3.util.response", "urllib3.util.retry", "http.client", "urllib3._base_connection", "urllib3.util", "urllib3._collections", "urllib3.connection", "urllib3.exceptions", "urllib3.connectionpool", "__future__", "collections", "io", "json", "logging", "re", "socket", "sys", "typing", "warnings", "zlib", "contextlib", "urllib3", "zstandard", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "email", "email.message", "enum", "http", "types", "typing_extensions", "urllib3._request_methods"], "hash": "dfb4805cc689738b84e6ad0e8e1465fbbb03bdbd", "id": "urllib3.response", "ignore_all": true, "interface_hash": "38d5998236d7a4e598c4553b870f2cf9f4e69dfe", "mtime": 1750470649, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\urllib3\\response.py", "plugin_data": null, "size": 46480, "suppressed": ["brotli", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "compression"], "version_id": "1.15.0"}