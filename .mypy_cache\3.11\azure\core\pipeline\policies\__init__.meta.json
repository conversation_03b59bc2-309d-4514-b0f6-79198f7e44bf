{"data_mtime": 1753366021, "dep_lines": [27, 28, 33, 34, 35, 36, 37, 46, 47, 48, 49, 50, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies._base", "azure.core.pipeline.policies._authentication", "azure.core.pipeline.policies._custom_hook", "azure.core.pipeline.policies._redirect", "azure.core.pipeline.policies._retry", "azure.core.pipeline.policies._distributed_tracing", "azure.core.pipeline.policies._universal", "azure.core.pipeline.policies._base_async", "azure.core.pipeline.policies._authentication_async", "azure.core.pipeline.policies._redirect_async", "azure.core.pipeline.policies._retry_async", "azure.core.pipeline.policies._sensitive_header_cleanup_policy", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "0c95f6021281369ba031f1bced5d7e635269c20b", "id": "azure.core.pipeline.policies", "ignore_all": true, "interface_hash": "586ec153d38d3dea06e0665a6966022d22dd743b", "mtime": 1750470991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\policies\\__init__.py", "plugin_data": null, "size": 2751, "suppressed": [], "version_id": "1.15.0"}