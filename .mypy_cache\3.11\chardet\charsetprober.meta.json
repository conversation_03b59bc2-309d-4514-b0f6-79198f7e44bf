{"data_mtime": 1753366013, "dep_lines": [33, 29, 30, 31, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 30, 30, 30], "dependencies": ["chardet.enums", "logging", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "2cab239a6908e45cee916f0aa9b9769fdf331abd", "id": "chardet.charsetp<PERSON>r", "ignore_all": true, "interface_hash": "f4e315e5592917f0217c148c6dad893dabd2803f", "mtime": 1750470759, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\chardet\\charsetprober.py", "plugin_data": null, "size": 5420, "suppressed": [], "version_id": "1.15.0"}