{"data_mtime": **********, "dep_lines": [7, 11, 3, 10, 14, 1, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [5, 25, 5, 25, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["pydantic_settings.sources.providers.env", "google.auth.credentials", "collections.abc", "google.auth", "pydantic_settings.main", "__future__", "functools", "typing", "builtins", "_frozen_importlib", "abc", "google", "google.auth._credentials_base", "google.auth._default", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings.sources.base", "types", "typing_extensions"], "hash": "ab5d8cfd77b484cbc26900f5b69d5f0aafd3b317", "id": "pydantic_settings.sources.providers.gcp", "ignore_all": true, "interface_hash": "d895057fa38b492c737ed72542e009fbfe95a87b", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pydantic_settings\\sources\\providers\\gcp.py", "plugin_data": null, "size": 5223, "suppressed": ["google.cloud.secretmanager"], "version_id": "1.15.0"}