{".class": "MypyFile", "_fullname": "aiohttp.abc", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "AbstractAccessLogger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["log", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.abc.AbstractAccessLogger", "name": "AbstractAccessLogger", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "aiohttp.abc.AbstractAccessLogger", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.abc", "mro": ["aiohttp.abc.AbstractAccessLogger", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "logger", "log_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.abc.AbstractAccessLogger.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "logger", "log_format"], "arg_types": ["aiohttp.abc.AbstractAccessLogger", "logging.Logger", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbstractAccessLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "aiohttp.abc.AbstractAccessLogger.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.abc.AbstractAccessLogger.enabled", "name": "enabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractAccessLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enabled of AbstractAccessLogger", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractAccessLogger.enabled", "name": "enabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractAccessLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enabled of AbstractAccessLogger", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "response", "time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractAccessLogger.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "response", "time"], "arg_types": ["aiohttp.abc.AbstractAccessLogger", "aiohttp.web_request.BaseRequest", "aiohttp.web_response.StreamResponse", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log of AbstractAccessLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractAccessLogger.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "response", "time"], "arg_types": ["aiohttp.abc.AbstractAccessLogger", "aiohttp.web_request.BaseRequest", "aiohttp.web_response.StreamResponse", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log of AbstractAccessLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "log_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.abc.AbstractAccessLogger.log_format", "name": "log_format", "type": "builtins.str"}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.abc.AbstractAccessLogger.logger", "name": "logger", "type": "logging.Logger"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.abc.AbstractAccessLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.abc.AbstractAccessLogger", "values": [], "variance": 0}, "slots": ["log_format", "logger"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractCookieJar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__iter__", 1], ["__len__", 1], ["clear", 1], ["clear_domain", 1], ["filter_cookies", 1], ["quote_cookie", 1], ["update_cookies", 1]], "alt_promote": null, "bases": ["typing.Sized", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "http.cookies.Morsel"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.abc.AbstractCookieJar", "name": "AbstractCookieJar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "aiohttp.abc.AbstractCookieJar", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.abc", "mro": ["aiohttp.abc.AbstractCookieJar", "typing.Sized", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.abc.AbstractCookieJar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "loop"], "arg_types": ["aiohttp.abc.AbstractCookieJar", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbstractCookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.abc.AbstractCookieJar._loop", "name": "_loop", "type": "asyncio.events.AbstractEventLoop"}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": ["self", "predicate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractCookieJar.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "predicate"], "arg_types": ["aiohttp.abc.AbstractCookieJar", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.abc.ClearCookiePredicate"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of Abstract<PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractCookieJar.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "predicate"], "arg_types": ["aiohttp.abc.AbstractCookieJar", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.abc.ClearCookiePredicate"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of Abstract<PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "clear_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "domain"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractCookieJar.clear_domain", "name": "clear_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "domain"], "arg_types": ["aiohttp.abc.AbstractCookieJar", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_domain of AbstractCookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractCookieJar.clear_domain", "name": "clear_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "domain"], "arg_types": ["aiohttp.abc.AbstractCookieJar", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_domain of AbstractCookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "filter_cookies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "request_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractCookieJar.filter_cookies", "name": "filter_cookies", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request_url"], "arg_types": ["aiohttp.abc.AbstractCookieJar", "yarl._url.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_cookies of AbstractCookieJar", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "http.cookies.BaseCookie"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractCookieJar.filter_cookies", "name": "filter_cookies", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request_url"], "arg_types": ["aiohttp.abc.AbstractCookieJar", "yarl._url.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_cookies of AbstractCookieJar", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "http.cookies.BaseCookie"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "quote_cookie": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractCookieJar.quote_cookie", "name": "quote_cookie", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractCookieJar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quote_cookie of AbstractCookieJar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractCookieJar.quote_cookie", "name": "quote_cookie", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractCookieJar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quote_cookie of AbstractCookieJar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_cookies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1], "arg_names": ["self", "cookies", "response_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractCookieJar.update_cookies", "name": "update_cookies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "cookies", "response_url"], "arg_types": ["aiohttp.abc.AbstractCookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, "yarl._url.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_cookies of AbstractCookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractCookieJar.update_cookies", "name": "update_cookies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "cookies", "response_url"], "arg_types": ["aiohttp.abc.AbstractCookieJar", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseCookies"}, "yarl._url.URL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_cookies of AbstractCookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.abc.AbstractCookieJar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.abc.AbstractCookieJar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractMatchInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["add_app", 1], ["apps", 1], ["expect_handler", 1], ["freeze", 1], ["get_info", 1], ["handler", 1], ["http_exception", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.abc.AbstractMatchInfo", "name": "AbstractMatchInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "aiohttp.abc.AbstractMatchInfo", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.abc", "mro": ["aiohttp.abc.AbstractMatchInfo", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "aiohttp.abc.AbstractMatchInfo.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "add_app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractMatchInfo.add_app", "name": "add_app", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "app"], "arg_types": ["aiohttp.abc.AbstractMatchInfo", "aiohttp.web_app.Application"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_app of AbstractMatchInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractMatchInfo.add_app", "name": "add_app", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "app"], "arg_types": ["aiohttp.abc.AbstractMatchInfo", "aiohttp.web_app.Application"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_app of AbstractMatchInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "apps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractMatchInfo.apps", "name": "apps", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apps of AbstractMatchInfo", "ret_type": {".class": "Instance", "args": ["aiohttp.web_app.Application"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractMatchInfo.apps", "name": "apps", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apps of AbstractMatchInfo", "ret_type": {".class": "Instance", "args": ["aiohttp.web_app.Application"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "expect_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractMatchInfo.expect_handler", "name": "expect_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expect_handler of AbstractMatchInfo", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_request.Request"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["aiohttp.web_response.StreamResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractMatchInfo.expect_handler", "name": "expect_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expect_handler of AbstractMatchInfo", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_request.Request"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["aiohttp.web_response.StreamResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "freeze": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractMatchInfo.freeze", "name": "freeze", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "freeze of AbstractMatchInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractMatchInfo.freeze", "name": "freeze", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "freeze of AbstractMatchInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractMatchInfo.get_info", "name": "get_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_info of AbstractMatchInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractMatchInfo.get_info", "name": "get_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_info of AbstractMatchInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractMatchInfo.handler", "name": "handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handler of AbstractMatchInfo", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_request.Request"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["aiohttp.web_response.StreamResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractMatchInfo.handler", "name": "handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handler of AbstractMatchInfo", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_request.Request"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["aiohttp.web_response.StreamResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "http_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractMatchInfo.http_exception", "name": "http_exception", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "http_exception of AbstractMatchInfo", "ret_type": {".class": "UnionType", "items": ["aiohttp.web_exceptions.HTTPException", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractMatchInfo.http_exception", "name": "http_exception", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractMatchInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "http_exception of AbstractMatchInfo", "ret_type": {".class": "UnionType", "items": ["aiohttp.web_exceptions.HTTPException", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.abc.AbstractMatchInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.abc.AbstractMatchInfo", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["close", 1], ["resolve", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.abc.AbstractResolver", "name": "AbstractResolver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "aiohttp.abc.AbstractResolver", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.abc", "mro": ["aiohttp.abc.AbstractResolver", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractResolver.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of AbstractResolver", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractResolver.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of AbstractResolver", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "host", "port", "family"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractResolver.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "host", "port", "family"], "arg_types": ["aiohttp.abc.AbstractResolver", "builtins.str", "builtins.int", "socket.AddressFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of AbstractResolver", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.abc.ResolveResult"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractResolver.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "host", "port", "family"], "arg_types": ["aiohttp.abc.AbstractResolver", "builtins.str", "builtins.int", "socket.AddressFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of AbstractResolver", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.abc.ResolveResult"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.abc.AbstractResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.abc.AbstractResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractRouter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["resolve", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.abc.AbstractRouter", "name": "AbstractRouter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "aiohttp.abc.AbstractRouter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.abc", "mro": ["aiohttp.abc.AbstractRouter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.abc.AbstractRouter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractRouter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbstractRouter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_frozen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.abc.AbstractRouter._frozen", "name": "_frozen", "type": "builtins.bool"}}, "freeze": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.abc.AbstractRouter.freeze", "name": "freeze", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractRouter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "freeze of AbstractRouter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "frozen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.abc.AbstractRouter.frozen", "name": "frozen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractRouter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frozen of AbstractRouter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractRouter.frozen", "name": "frozen", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractRouter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frozen of AbstractRouter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.abc.AbstractRouter.post_init", "name": "post_init", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "app"], "arg_types": ["aiohttp.abc.AbstractRouter", "aiohttp.web_app.Application"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "post_init of AbstractRouter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractRouter.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["aiohttp.abc.AbstractRouter", "aiohttp.web_request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of Abstract<PERSON><PERSON>er", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "aiohttp.abc.AbstractMatchInfo"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractRouter.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["aiohttp.abc.AbstractRouter", "aiohttp.web_request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of Abstract<PERSON><PERSON>er", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "aiohttp.abc.AbstractMatchInfo"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.abc.AbstractRouter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.abc.AbstractRouter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractStreamWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["drain", 1], ["enable_chunking", 1], ["enable_compression", 1], ["write", 1], ["write_eof", 1], ["write_headers", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.abc.AbstractStreamWriter", "name": "AbstractStreamWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "aiohttp.abc.AbstractStreamWriter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.abc", "mro": ["aiohttp.abc.AbstractStreamWriter", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "buffer_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "aiohttp.abc.AbstractStreamWriter.buffer_size", "name": "buffer_size", "type": "builtins.int"}}, "drain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractStreamWriter.drain", "name": "drain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractStreamWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drain of AbstractStreamWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractStreamWriter.drain", "name": "drain", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractStreamWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drain of AbstractStreamWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "enable_chunking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractStreamWriter.enable_chunking", "name": "enable_chunking", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractStreamWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_chunking of AbstractStreamWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractStreamWriter.enable_chunking", "name": "enable_chunking", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractStreamWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_chunking of AbstractStreamWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "enable_compression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractStreamWriter.enable_compression", "name": "enable_compression", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "strategy"], "arg_types": ["aiohttp.abc.AbstractStreamWriter", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_compression of AbstractStreamWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractStreamWriter.enable_compression", "name": "enable_compression", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "encoding", "strategy"], "arg_types": ["aiohttp.abc.AbstractStreamWriter", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_compression of AbstractStreamWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "aiohttp.abc.AbstractStreamWriter.length", "name": "length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "output_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "aiohttp.abc.AbstractStreamWriter.output_size", "name": "output_size", "type": "builtins.int"}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractStreamWriter.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": ["aiohttp.abc.AbstractStreamWriter", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of AbstractStreamWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractStreamWriter.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": ["aiohttp.abc.AbstractStreamWriter", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of AbstractStreamWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "write_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractStreamWriter.write_eof", "name": "write_eof", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "chunk"], "arg_types": ["aiohttp.abc.AbstractStreamWriter", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_eof of AbstractStreamWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractStreamWriter.write_eof", "name": "write_eof", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "chunk"], "arg_types": ["aiohttp.abc.AbstractStreamWriter", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_eof of AbstractStreamWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "write_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "status_line", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractStreamWriter.write_headers", "name": "write_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "status_line", "headers"], "arg_types": ["aiohttp.abc.AbstractStreamWriter", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_headers of AbstractStreamWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractStreamWriter.write_headers", "name": "write_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "status_line", "headers"], "arg_types": ["aiohttp.abc.AbstractStreamWriter", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_headers of AbstractStreamWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.abc.AbstractStreamWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.abc.AbstractStreamWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__await__", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.abc.AbstractView", "name": "AbstractView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "aiohttp.abc.AbstractView", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.abc", "mro": ["aiohttp.abc.AbstractView", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "aiohttp.abc.AbstractView.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__await__ of Abstract<PERSON>iew", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, "aiohttp.web_response.StreamResponse"], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractView.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__await__ of Abstract<PERSON>iew", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, "aiohttp.web_response.StreamResponse"], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.abc.AbstractView.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["aiohttp.abc.AbstractView", "aiohttp.web_request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbstractView", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.abc.AbstractView._request", "name": "_request", "type": "aiohttp.web_request.Request"}}, "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.abc.AbstractView.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request of AbstractView", "ret_type": "aiohttp.web_request.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.abc.AbstractView.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.abc.AbstractView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request of AbstractView", "ret_type": "aiohttp.web_request.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.abc.AbstractView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.abc.AbstractView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Application": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_app.Application", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "BaseCookie": {".class": "SymbolTableNode", "cross_ref": "http.cookies.BaseCookie", "kind": "Gdef"}, "BaseRequest": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_request.BaseRequest", "kind": "Gdef"}, "CIMultiDict": {".class": "SymbolTableNode", "cross_ref": "multidict.CIMultiDict", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClearCookiePredicate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.abc.ClearCookiePredicate", "line": 170, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "http.cookies.Morsel"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPException", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "IterableBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "aiohttp.abc.IterableBase", "line": 165, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "http.cookies.Morsel"}], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LooseCookies": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.LooseCookies", "kind": "Gdef"}, "Morsel": {".class": "SymbolTableNode", "cross_ref": "http.cookies.Morsel", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_request.Request", "kind": "Gdef"}, "ResolveResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.abc.ResolveResult", "name": "ResolveResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.abc.ResolveResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.abc", "mro": ["aiohttp.abc.ResolveResult", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["hostname", "builtins.str"], ["host", "builtins.str"], ["port", "builtins.int"], ["family", "builtins.int"], ["proto", "builtins.int"], ["flags", "builtins.int"]], "readonly_keys": [], "required_keys": ["family", "flags", "host", "hostname", "port", "proto"]}}}, "Sized": {".class": "SymbolTableNode", "cross_ref": "typing.Sized", "kind": "Gdef"}, "StreamResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_response.StreamResponse", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "yarl._url.URL", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.abc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.abc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.abc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.abc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.abc.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.abc.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "zlib": {".class": "SymbolTableNode", "cross_ref": "zlib", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\abc.py"}