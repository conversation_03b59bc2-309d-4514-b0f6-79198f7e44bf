{"data_mtime": 1753366021, "dep_lines": [49, 50, 33, 50, 31, 32, 39, 40, 51, 26, 27, 28, 29, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 20, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies._base", "azure.core.pipeline.policies._utils", "azure.core.pipeline.transport", "azure.core.pipeline.policies", "azure.core.configuration", "azure.core.pipeline", "azure.core.rest", "azure.core.exceptions", "azure.core._enum_meta", "typing", "io", "logging", "time", "enum", "builtins", "_frozen_importlib", "abc", "azure.core.pipeline.transport._base", "azure.core.pipeline.transport._base_async", "azure.core.rest._helpers", "azure.core.rest._rest_py3", "contextlib", "typing_extensions"], "hash": "7e08aedac684358d1e1504b9c195f689408d440e", "id": "azure.core.pipeline.policies._retry", "ignore_all": true, "interface_hash": "7fd93c3dcefb2770a6cf8d27d0c0ad960bc57386", "mtime": 1750470991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\policies\\_retry.py", "plugin_data": null, "size": 25353, "suppressed": [], "version_id": "1.15.0"}