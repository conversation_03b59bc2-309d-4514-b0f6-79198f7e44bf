{".class": "MypyFile", "_fullname": "azure.core.pipeline.policies._distributed_tracing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DistributedTracingPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "name": "DistributedTracingPolicy", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._distributed_tracing", "mro": ["azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "TRACING_CONTEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy.TRACING_CONTEXT", "name": "TRACING_CONTEXT", "type": "builtins.str"}}, "_ERROR_TYPE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._ERROR_TYPE", "name": "_ERROR_TYPE", "type": "builtins.str"}}, "_HTTP_REQUEST_METHOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._HTTP_REQUEST_METHOD", "name": "_HTTP_REQUEST_METHOD", "type": "builtins.str"}}, "_HTTP_RESEND_COUNT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._HTTP_RESEND_COUNT", "name": "_HTTP_RESEND_COUNT", "type": "builtins.str"}}, "_HTTP_RESPONSE_STATUS_CODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._HTTP_RESPONSE_STATUS_CODE", "name": "_HTTP_RESPONSE_STATUS_CODE", "type": "builtins.str"}}, "_REQUEST_ID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._REQUEST_ID", "name": "_REQUEST_ID", "type": "builtins.str"}}, "_REQUEST_ID_ATTR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._REQUEST_ID_ATTR", "name": "_REQUEST_ID_ATTR", "type": "builtins.str"}}, "_RESPONSE_ID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._RESPONSE_ID", "name": "_RESPONSE_ID", "type": "builtins.str"}}, "_RESPONSE_ID_ATTR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._RESPONSE_ID_ATTR", "name": "_RESPONSE_ID_ATTR", "type": "builtins.str"}}, "_SERVER_ADDRESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._SERVER_ADDRESS", "name": "_SERVER_ADDRESS", "type": "builtins.str"}}, "_SERVER_PORT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._SERVER_PORT", "name": "_SERVER_PORT", "type": "builtins.str"}}, "_SUPPRESSION_TOKEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._SUPPRESSION_TOKEN", "name": "_SUPPRESSION_TOKEN", "type": "builtins.str"}}, "_URL_FULL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._URL_FULL", "name": "_URL_FULL", "type": "builtins.str"}}, "_USER_AGENT_ORIGINAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._USER_AGENT_ORIGINAL", "name": "_USER_AGENT_ORIGINAL", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 4], "arg_names": ["self", "instrumentation_config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 4], "arg_names": ["self", "instrumentation_config", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DistributedTracingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_instrumentation_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._instrumentation_config", "name": "_instrumentation_config", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_network_span_namer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._network_span_namer", "name": "_network_span_namer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_set_http_client_span_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "span", "request", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._set_http_client_span_attributes", "name": "_set_http_client_span_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "span", "request", "response"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy"}, "opentelemetry.trace.span.Span", {".class": "UnionType", "items": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_http_client_span_attributes of DistributedTracingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tracing_attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy._tracing_attributes", "name": "_tracing_attributes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "end_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "request", "response", "exc_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy.end_span", "name": "end_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "request", "response", "exc_info"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._distributed_tracing.OptExcInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_span of DistributedTracingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy.on_exception", "name": "on_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_exception of DistributedTracingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request of DistributedTracingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy.on_response", "name": "on_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_response of DistributedTracingPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "HTTPResponseType"], "typeddict_type": null}}, "ExcInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "azure.core.pipeline.policies._distributed_tracing.ExcInfo", "line": 51, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "builtins.BaseException"}, "builtins.BaseException", "types.TracebackType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}}, "HTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPResponseType", "name": "HTTPResponseType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}}, "HttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpRequest", "kind": "Gdef"}, "HttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpResponse", "kind": "Gdef"}, "LegacyHttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "LegacyHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpResponse", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "OptExcInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "azure.core.pipeline.policies._distributed_tracing.OptExcInfo", "line": 52, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.pipeline.policies._distributed_tracing.ExcInfo"}, {".class": "TupleType", "implicit": false, "items": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PipelineRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineRequest", "kind": "Gdef"}, "PipelineResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineResponse", "kind": "Gdef"}, "SansIOHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.Span", "kind": "Gdef"}, "SpanKind": {".class": "SymbolTableNode", "cross_ref": "azure.core.tracing._models.SpanKind", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "TracingOptions": {".class": "SymbolTableNode", "cross_ref": "azure.core.tracing._models.TracingOptions", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._distributed_tracing._LOGGER", "name": "_LOGGER", "type": "logging.Logger"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._distributed_tracing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._distributed_tracing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._distributed_tracing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._distributed_tracing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._distributed_tracing.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._distributed_tracing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_default_network_span_namer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["http_request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._distributed_tracing._default_network_span_namer", "name": "_default_network_span_namer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["http_request"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing._default_network_span_namer", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_network_span_namer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._distributed_tracing.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._distributed_tracing._default_network_span_namer", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}]}}}, "change_context": {".class": "SymbolTableNode", "cross_ref": "azure.core.tracing.common.change_context", "kind": "Gdef"}, "get_tracer": {".class": "SymbolTableNode", "cross_ref": "azure.core.instrumentation.get_tracer", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "settings": {".class": "SymbolTableNode", "cross_ref": "azure.core.settings.settings", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\policies\\_distributed_tracing.py"}