#!/usr/bin/env python3
"""
Test script to verify the virtual environment setup for LLM Proxy Server
"""

import sys
import importlib.util

def test_import(module_name):
    """Test if a module can be imported"""
    try:
        spec = importlib.util.find_spec(module_name)
        if spec is not None:
            print(f"✓ {module_name} - Available")
            return True
        else:
            print(f"✗ {module_name} - Not found")
            return False
    except ImportError as e:
        print(f"✗ {module_name} - Import error: {e}")
        return False

def main():
    print("=" * 60)
    print("LLM Proxy Server Virtual Environment Test")
    print("=" * 60)
    
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print()
    
    print("Testing required dependencies:")
    print("-" * 30)
    
    # Test core dependencies
    required_modules = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'httpx',
        'ollama',
        'yaml',
        'aiofiles',
        'multipart',
        'jose',
        'passlib',
        'structlog',
        'tenacity',
        'prometheus_client',
        'sse_starlette'
    ]
    
    success_count = 0
    for module in required_modules:
        if test_import(module):
            success_count += 1
    
    print()
    print(f"Results: {success_count}/{len(required_modules)} modules available")
    
    if success_count == len(required_modules):
        print("✓ Virtual environment setup is complete and ready!")
    else:
        print("✗ Some dependencies are missing. Please check the installation.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
