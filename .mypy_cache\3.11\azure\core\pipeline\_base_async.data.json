{".class": "MypyFile", "_fullname": "azure.core.pipeline._base_async", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncContextManager": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncContextManager", "kind": "Gdef"}, "AsyncHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy", "kind": "Gdef"}, "AsyncHTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "name": "AsyncHTTPResponseType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "AsyncHttpTransport": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport", "kind": "Gdef"}, "AsyncPipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}], "extra_attrs": null, "type_ref": "typing.AsyncContextManager"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline._base_async.AsyncPipeline", "name": "AsyncPipeline", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline._base_async.AsyncPipeline", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline._base_async", "mro": ["azure.core.pipeline._base_async.AsyncPipeline", "typing.AsyncContextManager", "contextlib.AbstractAsyncContextManager", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline._base_async.AsyncPipeline.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of AsyncPipeline", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline._base_async.AsyncPipeline.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncPipeline", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "transport", "policies"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline._base_async.AsyncPipeline.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "transport", "policies"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_impl_policies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline._base_async.AsyncPipeline._impl_policies", "name": "_impl_policies", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_prepare_multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline._base_async.AsyncPipeline._prepare_multipart", "name": "_prepare_multipart", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_multipart of AsyncPipeline", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_multipart_mixed_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline._base_async.AsyncPipeline._prepare_multipart_mixed_request", "name": "_prepare_multipart_mixed_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_multipart_mixed_request of AsyncPipeline", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline._base_async.AsyncPipeline._transport", "name": "_transport", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline._base_async.AsyncPipeline.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "request", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of AsyncPipeline", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncPipeline.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async.AsyncPipeline", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async.AsyncPipeline"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "AsyncHTTPResponseType"], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PipelineContext": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineContext", "kind": "Gdef"}, "PipelineRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineRequest", "kind": "Gdef"}, "PipelineResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineResponse", "kind": "Gdef"}, "SansIOHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_AsyncTransportRunner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline._base_async._AsyncTransportRunner", "name": "_AsyncTransportRunner", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline._base_async._AsyncTransportRunner", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline._base_async", "mro": ["azure.core.pipeline._base_async._AsyncTransportRunner", "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sender"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline._base_async._AsyncTransportRunner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sender"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async._AsyncTransportRunner"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AsyncTransportRunner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sender": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline._base_async._AsyncTransportRunner._sender", "name": "_sender", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base_async.AsyncHttpTransport"}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline._base_async._AsyncTransportRunner.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async._AsyncTransportRunner"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of _AsyncTransportRunner", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async._AsyncTransportRunner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._AsyncTransportRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async._AsyncTransportRunner"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "AsyncHTTPResponseType"], "typeddict_type": null}}, "_SansIOAsyncHTTPPolicyRunner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "name": "_SansIOAsyncHTTPPolicyRunner", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline._base_async", "mro": ["azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _SansIOAsyncHTTPPolicyRunner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner._policy", "name": "_policy", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of _SansIOAsyncHTTPPolicyRunner", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline._base_async.AsyncHTTPResponseType", "id": 2, "name": "AsyncHTTPResponseType", "namespace": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline._base_async._SansIOAsyncHTTPPolicyRunner"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "AsyncHTTPResponseType"], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline._base_async.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline._base_async.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline._base_async.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline._base_async.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline._base_async.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline._base_async.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_await_result": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._tools_async.await_result", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cleanup_kwargs_for_transport": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline._base.cleanup_kwargs_for_transport", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\_base_async.py"}