{"data_mtime": 1753366016, "dep_lines": [20, 21, 24, 25, 26, 27, 28, 53, 54, 63, 64, 4, 19, 23, 24, 41, 44, 45, 53, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 10, 5, 5, 5, 5, 10, 5, 5, 5, 5, 20, 5, 20, 5, 5, 5, 5, 5, 10, 20, 10, 10, 10, 5, 10, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["urllib3.util.ssl_", "urllib3.util.ssltransport", "urllib3.http2.probe", "urllib3.util.response", "urllib3.util.timeout", "urllib3.util.util", "urllib3.util.wait", "urllib3.util.connection", "urllib3.util.request", "urllib3.util.ssl_match_hostname", "urllib3.util.url", "http.client", "urllib3.response", "urllib3._collections", "urllib3.http2", "urllib3._base_connection", "urllib3._version", "urllib3.exceptions", "urllib3.util", "__future__", "datetime", "http", "logging", "os", "re", "socket", "sys", "threading", "typing", "warnings", "ssl", "builtins", "_frozen_importlib", "_io", "_socket", "_ssl", "_typeshed", "abc", "email", "email.errors", "email.message", "enum", "io", "types", "typing_extensions", "urllib3._request_methods", "urllib3.connectionpool", "urllib3.util.retry"], "hash": "ee5834881f9e7c49455d29b2478987038293314a", "id": "urllib3.connection", "ignore_all": true, "interface_hash": "76e6f6b192a94436eb1a7482b4e981fe87fc6f13", "mtime": 1750470649, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\urllib3\\connection.py", "plugin_data": null, "size": 42613, "suppressed": [], "version_id": "1.15.0"}