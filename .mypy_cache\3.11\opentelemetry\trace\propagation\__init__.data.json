{".class": "MypyFile", "_fullname": "opentelemetry.trace.propagation", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef"}, "INVALID_SPAN": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.INVALID_SPAN", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SPAN_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace.propagation.SPAN_KEY", "name": "SPAN_KEY", "type": "builtins.str"}}, "Span": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.Span", "kind": "Gdef"}, "_SPAN_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace.propagation._SPAN_KEY", "name": "_SPAN_KEY", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.propagation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.propagation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.propagation.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.propagation.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.propagation.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.propagation.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.propagation.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_key": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.create_key", "kind": "Gdef"}, "get_current_span": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.propagation.get_current_span", "name": "get_current_span", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["context"], "arg_types": [{".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current_span", "ret_type": "opentelemetry.trace.span.Span", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_value": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.get_value", "kind": "Gdef"}, "set_span_in_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["span", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.propagation.set_span_in_context", "name": "set_span_in_context", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["span", "context"], "arg_types": ["opentelemetry.trace.span.Span", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_span_in_context", "ret_type": "opentelemetry.context.context.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_value": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.set_value", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\trace\\propagation\\__init__.py"}