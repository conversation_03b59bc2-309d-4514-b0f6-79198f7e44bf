{".class": "MypyFile", "_fullname": "chardet.mbcsgroupprober", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Big5Prober": {".class": "SymbolTableNode", "cross_ref": "chardet.big5prober.Big5Prober", "kind": "Gdef"}, "CP949Prober": {".class": "SymbolTableNode", "cross_ref": "chardet.cp949prober.CP949Prober", "kind": "Gdef"}, "CharSetGroupProber": {".class": "SymbolTableNode", "cross_ref": "chardet.charsetgroupprober.CharSetGroupProber", "kind": "Gdef"}, "EUCJPProber": {".class": "SymbolTableNode", "cross_ref": "chardet.eucjpprober.EUCJPProber", "kind": "Gdef"}, "EUCKRProber": {".class": "SymbolTableNode", "cross_ref": "chardet.euckrprober.EUCKRProber", "kind": "Gdef"}, "EUCTWProber": {".class": "SymbolTableNode", "cross_ref": "chardet.euctwprober.EUCTWProber", "kind": "Gdef"}, "GB2312Prober": {".class": "SymbolTableNode", "cross_ref": "chardet.gb2312prober.GB2312Prober", "kind": "Gdef"}, "JOHABProber": {".class": "SymbolTableNode", "cross_ref": "chardet.johabprober.JOHABProber", "kind": "Gdef"}, "LanguageFilter": {".class": "SymbolTableNode", "cross_ref": "chardet.enums.LanguageFilter", "kind": "Gdef"}, "MBCSGroupProber": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.charsetgroupprober.CharSetGroupProber"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.mbcsgroupprober.MBCSGroupProber", "name": "MBCSGroupProber", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.mbcsgroupprober.MBCSGroupProber", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.mbcsgroupprober", "mro": ["chardet.mbcsgroupprober.MBCSGroupProber", "chardet.charsetgroupprober.CharSetGroupProber", "chardet.charsetprober.CharSetProber", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "lang_filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.mbcsgroupprober.MBCSGroupProber.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "lang_filter"], "arg_types": ["chardet.mbcsgroupprober.MBCSGroupProber", "chardet.enums.LanguageFilter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MBCSGroupProber", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.mbcsgroupprober.MBCSGroupProber.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.mbcsgroupprober.MBCSGroupProber", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SJISProber": {".class": "SymbolTableNode", "cross_ref": "chardet.sjisprober.SJISProber", "kind": "Gdef"}, "UTF8Prober": {".class": "SymbolTableNode", "cross_ref": "chardet.utf8prober.UTF8Prober", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.mbcsgroupprober.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.mbcsgroupprober.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.mbcsgroupprober.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.mbcsgroupprober.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.mbcsgroupprober.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.mbcsgroupprober.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\chardet\\mbcsgroupprober.py"}