{".class": "MypyFile", "_fullname": "aiohttp.web_request", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractStreamWriter": {".class": "SymbolTableNode", "cross_ref": "aiohttp.abc.AbstractStreamWriter", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Application": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_app.Application", "kind": "Gdef", "module_public": false}, "BaseRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "aiohttp.helpers.HeadersMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_request.BaseRequest", "name": "BaseRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_request", "mro": ["aiohttp.web_request.BaseRequest", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.object"], "names": {".class": "SymbolTable", "ATTRS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_request.BaseRequest.ATTRS", "name": "ATTRS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "POST_METHODS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_request.BaseRequest.POST_METHODS", "name": "POST_METHODS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of BaseRequest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_request.BaseRequest", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of BaseRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_request.BaseRequest", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of BaseRequest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["aiohttp.web_request.BaseRequest", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of BaseRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "message", "payload", "protocol", "payload_writer", "task", "loop", "client_max_size", "state", "scheme", "host", "remote"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "message", "payload", "protocol", "payload_writer", "task", "loop", "client_max_size", "state", "scheme", "host", "remote"], "arg_types": ["aiohttp.web_request.BaseRequest", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, "aiohttp.streams.StreamReader", "aiohttp.web_protocol.RequestHandler", "aiohttp.abc.AbstractStreamWriter", {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "asyncio.events.AbstractEventLoop", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of BaseRequest", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of BaseRequest", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of BaseRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["aiohttp.web_request.BaseRequest", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of BaseRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._cache", "name": "_cache", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_cancel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest._cancel", "name": "_cancel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["aiohttp.web_request.BaseRequest", "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cancel of BaseRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client_max_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._client_max_size", "name": "_client_max_size", "type": "builtins.int"}}, "_etag_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["etag_header"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "aiohttp.web_request.BaseRequest._etag_values", "name": "_etag_values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["etag_header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_etag_values of BaseRequest", "ret_type": {".class": "Instance", "args": ["aiohttp.helpers.ETag"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._etag_values", "name": "_etag_values", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["etag_header"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_etag_values of BaseRequest", "ret_type": {".class": "Instance", "args": ["aiohttp.helpers.ETag"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_finish": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest._finish", "name": "_finish", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_finish of BaseRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._headers", "name": "_headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}}}, "_if_match_or_none_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "header_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "aiohttp.web_request.BaseRequest._if_match_or_none_impl", "name": "_if_match_or_none_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "header_value"], "arg_types": [{".class": "TypeType", "item": "aiohttp.web_request.BaseRequest"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_if_match_or_none_impl of BaseRequest", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["aiohttp.helpers.ETag"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._if_match_or_none_impl", "name": "_if_match_or_none_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "header_value"], "arg_types": [{".class": "TypeType", "item": "aiohttp.web_request.BaseRequest"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_if_match_or_none_impl of BaseRequest", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["aiohttp.helpers.ETag"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._loop", "name": "_loop", "type": "asyncio.events.AbstractEventLoop"}}, "_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._message", "name": "_message", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}}}, "_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._method", "name": "_method", "type": "builtins.str"}}, "_payload": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._payload", "name": "_payload", "type": "aiohttp.streams.StreamReader"}}, "_payload_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._payload_writer", "name": "_payload_writer", "type": "aiohttp.abc.AbstractStreamWriter"}}, "_post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request.BaseRequest._post", "name": "_post", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "aiohttp.web_request.FileField"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "multidict.MultiDictProxy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_prepare_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web_request.BaseRequest._prepare_hook", "name": "_prepare_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["aiohttp.web_request.BaseRequest", "aiohttp.web_response.StreamResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_hook of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_protocol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._protocol", "name": "_protocol", "type": "aiohttp.web_protocol.RequestHandler"}}, "_read_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request.BaseRequest._read_bytes", "name": "_read_bytes", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_rel_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._rel_url", "name": "_rel_url", "type": "yarl._url.URL"}}, "_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._state", "name": "_state", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._task", "name": "_task", "type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}}}, "_transport_peername": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._transport_peername", "name": "_transport_peername", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_transport_sslcontext": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._transport_sslcontext", "name": "_transport_sslcontext", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.web_request.BaseRequest._version", "name": "_version", "type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}}}, "body_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.body_exists", "name": "body_exists", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "body_exists of BaseRequest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.body_exists", "name": "body_exists", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "can_read_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.can_read_body", "name": "can_read_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_read_body of BaseRequest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.can_read_body", "name": "can_read_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_read_body of BaseRequest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "client_max_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.client_max_size", "name": "client_max_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "client_max_size of BaseRequest", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.client_max_size", "name": "client_max_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "client_max_size of BaseRequest", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "method", "rel_url", "headers", "scheme", "host", "remote", "client_max_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.clone", "name": "clone", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "method", "rel_url", "headers", "scheme", "host", "remote", "client_max_size"], "arg_types": ["aiohttp.web_request.BaseRequest", {".class": "UnionType", "items": ["builtins.str", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clone of BaseRequest", "ret_type": "aiohttp.web_request.BaseRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.content", "name": "content", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "content of BaseRequest", "ret_type": "aiohttp.streams.StreamReader", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.content", "name": "content", "type": {".class": "Instance", "args": ["aiohttp.streams.StreamReader"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "cookies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.cookies", "name": "cookies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cookies of BaseRequest", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.cookies", "name": "cookies", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "forwarded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.forwarded", "name": "forwarded", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forwarded of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.forwarded", "name": "forwarded", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "get_extra_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.BaseRequest.get_extra_info", "name": "get_extra_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "default"], "arg_types": ["aiohttp.web_request.BaseRequest", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_extra_info of BaseRequest", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.has_body", "name": "has_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_body of BaseRequest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.has_body", "name": "has_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_body of BaseRequest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "headers of BaseRequest", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.headers", "name": "headers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of BaseRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.host", "name": "host", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "http_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.http_range", "name": "http_range", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "http_range of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.http_range", "name": "http_range", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "if_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.if_match", "name": "if_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "if_match of BaseRequest", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["aiohttp.helpers.ETag"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.if_match", "name": "if_match", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["aiohttp.helpers.ETag"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "if_modified_since": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.if_modified_since", "name": "if_modified_since", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "if_modified_since of BaseRequest", "ret_type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.if_modified_since", "name": "if_modified_since", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "if_none_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.if_none_match", "name": "if_none_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "if_none_match of BaseRequest", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["aiohttp.helpers.ETag"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.if_none_match", "name": "if_none_match", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["aiohttp.helpers.ETag"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "if_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.if_range", "name": "if_range", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "if_range of BaseRequest", "ret_type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.if_range", "name": "if_range", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "if_unmodified_since": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.if_unmodified_since", "name": "if_unmodified_since", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "if_unmodified_since of BaseRequest", "ret_type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.if_unmodified_since", "name": "if_unmodified_since", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "loads"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web_request.BaseRequest.json", "name": "json", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "loads"], "arg_types": ["aiohttp.web_request.BaseRequest", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.JSONDecoder"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keep_alive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.keep_alive", "name": "keep_alive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "keep_alive of BaseRequest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.keep_alive", "name": "keep_alive", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.loop", "name": "loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loop of BaseRequest", "ret_type": "asyncio.events.AbstractEventLoop", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.loop", "name": "loop", "type": {".class": "Instance", "args": ["asyncio.events.AbstractEventLoop"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.message", "name": "message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "message of BaseRequest", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_parser.RawRequestMessage"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.message", "name": "message", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "yarl._url.URL"], "partial_fallback": "aiohttp.http_parser.RawRequestMessage"}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.method", "name": "method", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "method of BaseRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.method", "name": "method", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web_request.BaseRequest.multipart", "name": "multipart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multipart of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "aiohttp.multipart.MultipartReader"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.path", "name": "path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path of BaseRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.path", "name": "path", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "path_qs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.path_qs", "name": "path_qs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path_qs of BaseRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.path_qs", "name": "path_qs", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web_request.BaseRequest.post", "name": "post", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "post of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "aiohttp.web_request.FileField"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "multidict.MultiDictProxy"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.protocol", "name": "protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "protocol of BaseRequest", "ret_type": "aiohttp.web_protocol.RequestHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.protocol", "name": "protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "protocol of BaseRequest", "ret_type": "aiohttp.web_protocol.RequestHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of BaseRequest", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.MultiMapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.query", "name": "query", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.MultiMapping"}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "query_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.query_string", "name": "query_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query_string of BaseRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.query_string", "name": "query_string", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "raw_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.raw_headers", "name": "raw_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_headers of BaseRequest", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.RawHeaders"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.raw_headers", "name": "raw_headers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "raw_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.raw_path", "name": "raw_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_path of BaseRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.raw_path", "name": "raw_path", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web_request.BaseRequest.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rel_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.rel_url", "name": "rel_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rel_url of BaseRequest", "ret_type": "yarl._url.URL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.rel_url", "name": "rel_url", "type": {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web_request.BaseRequest.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.remote", "name": "remote", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remote of BaseRequest", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.remote", "name": "remote", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.scheme", "name": "scheme", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scheme of BaseRequest", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.scheme", "name": "scheme", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "secure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.secure", "name": "secure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "secure of BaseRequest", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.secure", "name": "secure", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.task", "name": "task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "task of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.task", "name": "task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "task of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web_request.BaseRequest.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text of BaseRequest", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.transport", "name": "transport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transport of BaseRequest", "ret_type": {".class": "UnionType", "items": ["asyncio.transports.Transport", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.transport", "name": "transport", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transport of BaseRequest", "ret_type": {".class": "UnionType", "items": ["asyncio.transports.Transport", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.url", "name": "url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "url of BaseRequest", "ret_type": "yarl._url.URL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.url", "name": "url", "type": {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of BaseRequest", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.http_writer.HttpVersion"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.version", "name": "version", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": "aiohttp.http_writer.HttpVersion"}], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}, "writer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_request.BaseRequest.writer", "name": "writer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "writer of BaseRequest", "ret_type": "aiohttp.abc.AbstractStreamWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.BaseRequest.writer", "name": "writer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.BaseRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "writer of BaseRequest", "ret_type": "aiohttp.abc.AbstractStreamWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.BaseRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_request.BaseRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BodyPartReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.multipart.BodyPartReader", "kind": "Gdef", "module_public": false}, "CIMultiDict": {".class": "SymbolTableNode", "cross_ref": "multidict.CIMultiDict", "kind": "Gdef", "module_public": false}, "CIMultiDictProxy": {".class": "SymbolTableNode", "cross_ref": "multidict.CIMultiDictProxy", "kind": "Gdef", "module_public": false}, "ChainMapProxy": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.ChainMapProxy", "kind": "Gdef", "module_public": false}, "DEBUG": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.DEBUG", "kind": "Gdef", "module_public": false}, "DEFAULT_JSON_DECODER": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.DEFAULT_JSON_DECODER", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "ETAG_ANY": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.ETAG_ANY", "kind": "Gdef", "module_public": false}, "ETag": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.ETag", "kind": "Gdef", "module_public": false}, "EmptyStreamReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.EmptyStreamReader", "kind": "Gdef", "module_public": false}, "FileField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_request.FileField", "name": "FileField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.FileField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 78, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "name"}, {"alias": null, "context_column": 4, "context_line": 79, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "filename"}, {"alias": null, "context_column": 4, "context_line": 80, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kw_only": false, "name": "file"}, {"alias": null, "context_column": 4, "context_line": 81, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "content_type"}, {"alias": null, "context_column": 4, "context_line": 82, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}, "kw_only": false, "name": "headers"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.web_request", "mro": ["aiohttp.web_request.FileField", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_web_request_FileField_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_request.FileField.__aiohttp_web_request_FileField_AttrsAttributes__", "name": "__aiohttp_web_request_FileField_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.FileField.__aiohttp_web_request_FileField_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.web_request", "mro": ["aiohttp.web_request.FileField.__aiohttp_web_request_FileField_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "content_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "content_type", "name": "content_type", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "file", "name": "file", "type": {".class": "Instance", "args": ["_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "filename", "name": "filename", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "headers", "name": "headers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "name", "name": "name", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.web_request.FileField.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.web_request.FileField.__aiohttp_web_request_FileField_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.FileField.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of FileField", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.FileField.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of FileField", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "filename", "file", "content_type", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.FileField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "filename", "file", "content_type", "headers"], "arg_types": ["aiohttp.web_request.FileField", "builtins.str", "builtins.str", "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.FileField.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of FileField", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.FileField.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of FileField", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.web_request.FileField.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.web_request.FileField.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "filename"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "file"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "content_type"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "headers"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.web_request.FileField.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "content_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_request.FileField.content_type", "name": "content_type", "type": "builtins.str"}}, "file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_request.FileField.file", "name": "file", "type": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_request.FileField.filename", "name": "filename", "type": "builtins.str"}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_request.FileField.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDictProxy"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.web_request.FileField.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.FileField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_request.FileField", "values": [], "variance": 0}, "slots": ["content_type", "file", "filename", "headers", "name"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_public": false}, "HTTPRequestEntityTooLarge": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPRequestEntityTooLarge", "kind": "Gdef", "module_public": false}, "HeadersMixin": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.HeadersMixin", "kind": "Gdef", "module_public": false}, "HttpVersion": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "JSONDecoder": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.JSONDecoder", "kind": "Gdef", "module_public": false}, "LIST_QUOTED_ETAG_RE": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.LIST_QUOTED_ETAG_RE", "kind": "Gdef", "module_public": false}, "LooseHeaders": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.LooseHeaders", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MappingProxyType": {".class": "SymbolTableNode", "cross_ref": "types.MappingProxyType", "kind": "Gdef", "module_public": false}, "MultiDict": {".class": "SymbolTableNode", "cross_ref": "multidict.MultiDict", "kind": "Gdef", "module_public": false}, "MultiDictProxy": {".class": "SymbolTableNode", "cross_ref": "multidict.MultiDictProxy", "kind": "Gdef", "module_public": false}, "MultiMapping": {".class": "SymbolTableNode", "cross_ref": "multidict.MultiMapping", "kind": "Gdef", "module_public": false}, "MultipartReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.multipart.MultipartReader", "kind": "Gdef", "module_public": false}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "RawHeaders": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.RawHeaders", "kind": "Gdef", "module_public": false}, "RawRequestMessage": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.web_request.BaseRequest"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.web_request.Request", "name": "Request", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.Request", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.web_request", "mro": ["aiohttp.web_request.Request", "aiohttp.web_request.BaseRequest", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "aiohttp.helpers.HeadersMixin", "builtins.object"], "names": {".class": "SymbolTable", "ATTRS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_request.Request.ATTRS", "name": "ATTRS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__setattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.Request.__setattr__", "name": "__setattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "val"], "arg_types": ["aiohttp.web_request.Request", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setattr__ of Request", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request.Request._match_info", "name": "_match_info", "type": {".class": "UnionType", "items": ["aiohttp.web_urldispatcher.UrlMappingMatchInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_prepare_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web_request.Request._prepare_hook", "name": "_prepare_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["aiohttp.web_request.Request", "aiohttp.web_response.StreamResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_hook of Request", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_request.Request.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "app of Request", "ret_type": "aiohttp.web_app.Application", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.Request.app", "name": "app", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "app of Request", "ret_type": "aiohttp.web_app.Application", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "method", "rel_url", "headers", "scheme", "host", "remote", "client_max_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_request.Request.clone", "name": "clone", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "method", "rel_url", "headers", "scheme", "host", "remote", "client_max_size"], "arg_types": ["aiohttp.web_request.Request", {".class": "UnionType", "items": ["builtins.str", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.StrOrURL"}, "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.LooseHeaders"}, "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "aiohttp.helpers._SENTINEL"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clone of Request", "ret_type": "aiohttp.web_request.Request", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.web_request.Request.config_dict", "name": "config_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config_dict of Request", "ret_type": "aiohttp.helpers.ChainMapProxy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.Request.config_dict", "name": "config_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config_dict of Request", "ret_type": "aiohttp.helpers.ChainMapProxy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "match_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp.web_request.Request.match_info", "name": "match_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.web_request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_info of Request", "ret_type": "aiohttp.web_urldispatcher.UrlMappingMatchInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aiohttp.web_request.Request.match_info", "name": "match_info", "type": {".class": "Instance", "args": ["aiohttp.web_urldispatcher.UrlMappingMatchInfo"], "extra_attrs": null, "type_ref": "propcache._helpers_py.under_cached_property"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_request.Request.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.web_request.Request", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestHandler": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_protocol.RequestHandler", "kind": "Gdef", "module_public": false}, "SimpleCookie": {".class": "SymbolTableNode", "cross_ref": "http.cookies.SimpleCookie", "kind": "Gdef", "module_public": false}, "StrOrURL": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.StrOrURL", "kind": "Gdef", "module_public": false}, "StreamReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.StreamReader", "kind": "Gdef", "module_public": false}, "StreamResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_response.StreamResponse", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "URL": {".class": "SymbolTableNode", "cross_ref": "yarl._url.URL", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UrlMappingMatchInfo": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.UrlMappingMatchInfo", "kind": "Gdef", "module_public": false}, "_FORWARDED_PAIR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request._FORWARDED_PAIR", "name": "_FORWARDED_PAIR", "type": "builtins.str"}}, "_FORWARDED_PAIR_RE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request._FORWARDED_PAIR_RE", "name": "_FORWARDED_PAIR_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_QDTEXT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request._QDTEXT", "name": "_QDTEXT", "type": "builtins.str"}}, "_QUOTED_PAIR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": "\\\\[\\t !-~]", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request._QUOTED_PAIR", "name": "_QUOTED_PAIR", "type": "builtins.str"}}, "_QUOTED_PAIR_REPLACE_RE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request._QUOTED_PAIR_REPLACE_RE", "name": "_QUOTED_PAIR_REPLACE_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_QUOTED_STRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request._QUOTED_STRING", "name": "_QUOTED_STRING", "type": "builtins.str"}}, "_SENTINEL": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers._SENTINEL", "kind": "Gdef", "module_public": false}, "_TCHAR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request._TCHAR", "name": "_TCHAR", "type": "builtins.str"}}, "_TOKEN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.web_request._TOKEN", "name": "_TOKEN", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_request.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_request.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_request.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_request.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_request.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_request.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_request.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_public": false}, "attr": {".class": "SymbolTableNode", "cross_ref": "attr", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_public": false}, "hdrs": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs", "kind": "Gdef", "module_public": false}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef", "module_public": false}, "parse_http_date": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.parse_http_date", "kind": "Gdef", "module_public": false}, "parse_qsl": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qsl", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "reify": {".class": "SymbolTableNode", "cross_ref": "propcache._helpers.under_cached_property", "kind": "Gdef", "module_public": false}, "sentinel": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.sentinel", "kind": "Gdef", "module_public": false}, "set_exception": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.set_exception", "kind": "Gdef", "module_public": false}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef", "module_public": false}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef", "module_public": false}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef", "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web_request.py"}