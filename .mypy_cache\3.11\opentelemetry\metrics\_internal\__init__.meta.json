{"data_mtime": **********, "dep_lines": [52, 77, 78, 79, 51, 43, 44, 45, 46, 47, 48, 49, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["opentelemetry.metrics._internal.instrument", "opentelemetry.util._once", "opentelemetry.util._providers", "opentelemetry.util.types", "opentelemetry.environment_variables", "warnings", "abc", "dataclasses", "logging", "os", "threading", "typing", "builtins", "_frozen_importlib", "_thread", "opentelemetry.metrics._internal.observation", "opentelemetry.util"], "hash": "1d5c2594b52fb400f2ff63569d5c401762587856", "id": "opentelemetry.metrics._internal", "ignore_all": true, "interface_hash": "4236f726aeeff3ac0b55eb436816ae68b9dd8815", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\metrics\\_internal\\__init__.py", "plugin_data": null, "size": 30723, "suppressed": [], "version_id": "1.15.0"}