{"data_mtime": 1753366041, "dep_lines": [3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "pathlib", "typing", "pydantic", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "typing_extensions"], "hash": "0cc54dd1de96b20223d08924d3099d71ab47e598", "id": "llm_proxy_server.config", "ignore_all": false, "interface_hash": "da4d439b2925f293a9e41b4b8996ab395f6d9d04", "mtime": 1753366040, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\llm_proxy_server\\config.py", "plugin_data": null, "size": 2161, "suppressed": [], "version_id": "1.15.0"}