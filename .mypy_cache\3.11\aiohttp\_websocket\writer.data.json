{".class": "MypyFile", "_fullname": "aiohttp._websocket.writer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseProtocol": {".class": "SymbolTableNode", "cross_ref": "aiohttp.base_protocol.BaseProtocol", "kind": "Gdef"}, "ClientConnectionResetError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectionResetError", "kind": "Gdef"}, "DEFAULT_LIMIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 65536, "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp._websocket.writer.DEFAULT_LIMIT", "name": "DEFAULT_LIMIT", "type": "builtins.int"}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "MASK_LEN": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.MASK_LEN", "kind": "Gdef"}, "MSG_SIZE": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.MSG_SIZE", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PACK_CLOSE_CODE": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.PACK_CLOSE_CODE", "kind": "Gdef"}, "PACK_LEN1": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.PACK_LEN1", "kind": "Gdef"}, "PACK_LEN2": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.PACK_LEN2", "kind": "Gdef"}, "PACK_LEN3": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.PACK_LEN3", "kind": "Gdef"}, "PACK_RANDBITS": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.PACK_RANDBITS", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WEBSOCKET_MAX_SYNC_CHUNK_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp._websocket.writer.WEBSOCKET_MAX_SYNC_CHUNK_SIZE", "name": "WEBSOCKET_MAX_SYNC_CHUNK_SIZE", "type": "builtins.int"}}, "WSMsgType": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMsgType", "kind": "Gdef"}, "WS_DEFLATE_TRAILING": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WS_DEFLATE_TRAILING", "kind": "Gdef"}, "WebSocketWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp._websocket.writer.WebSocketWriter", "name": "WebSocketWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp._websocket.writer.WebSocketWriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp._websocket.writer", "mro": ["aiohttp._websocket.writer.WebSocketWriter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "protocol", "transport", "use_mask", "limit", "random", "compress", "notakeover"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp._websocket.writer.WebSocketWriter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "protocol", "transport", "use_mask", "limit", "random", "compress", "notakeover"], "arg_types": ["aiohttp._websocket.writer.WebSocketWriter", "aiohttp.base_protocol.BaseProtocol", "asyncio.transports.Transport", "builtins.bool", "builtins.int", "random.Random", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WebSocketWriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_closing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter._closing", "name": "_closing", "type": "builtins.bool"}}, "_compressobj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter._compressobj", "name": "_compressobj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_limit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter._limit", "name": "_limit", "type": "builtins.int"}}, "_make_compress_obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compress"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp._websocket.writer.WebSocketWriter._make_compress_obj", "name": "_make_compress_obj", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "compress"], "arg_types": ["aiohttp._websocket.writer.WebSocketWriter", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_compress_obj of WebSocketWriter", "ret_type": "aiohttp.compression_utils.ZLibCompressor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_output_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter._output_size", "name": "_output_size", "type": "builtins.int"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "code", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp._websocket.writer.WebSocketWriter.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "code", "message"], "arg_types": ["aiohttp._websocket.writer.WebSocketWriter", "builtins.int", {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of WebSocketWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter.compress", "name": "compress", "type": "builtins.int"}}, "get_random_bits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter.get_random_bits", "name": "get_random_bits", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": {".class": "ExtraAttrs", "attrs": {"__mypy_partial": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["random.Random"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getrandbits of Random", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "immutable": [], "mod_name": null}, "type_ref": "functools.partial"}}}, "notakeover": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter.notakeover", "name": "notakeover", "type": "builtins.bool"}}, "protocol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter.protocol", "name": "protocol", "type": "aiohttp.base_protocol.BaseProtocol"}}, "send_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "message", "opcode", "compress"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp._websocket.writer.WebSocketWriter.send_frame", "name": "send_frame", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "message", "opcode", "compress"], "arg_types": ["aiohttp._websocket.writer.WebSocketWriter", "builtins.bytes", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_frame of WebSocketWriter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter.transport", "name": "transport", "type": "asyncio.transports.Transport"}}, "use_mask": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp._websocket.writer.WebSocketWriter.use_mask", "name": "use_mask", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp._websocket.writer.WebSocketWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp._websocket.writer.WebSocketWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZLibCompressor": {".class": "SymbolTableNode", "cross_ref": "aiohttp.compression_utils.ZLibCompressor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.writer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.writer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.writer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.writer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.writer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.writer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "websocket_mask": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.websocket_mask", "kind": "Gdef"}, "zlib": {".class": "SymbolTableNode", "cross_ref": "zlib", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\_websocket\\writer.py"}