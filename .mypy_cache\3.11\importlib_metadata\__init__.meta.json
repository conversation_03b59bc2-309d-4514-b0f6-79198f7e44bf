{"data_mtime": 1753366014, "dep_lines": [42, 42, 25, 28, 32, 33, 34, 38, 39, 41, 42, 540, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 30, 596, 719, 1, 1, 1, 1, 824], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 5, 10, 10, 10, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 5, 30, 30, 30, 20], "dependencies": ["importlib_metadata.compat.py39", "importlib_metadata.compat.py311", "collections.abc", "importlib.abc", "importlib_metadata._meta", "importlib_metadata._collections", "importlib_metadata._compat", "importlib_metadata._functools", "importlib_metadata._itertools", "importlib_metadata._typing", "importlib_metadata.compat", "importlib_metadata._adapters", "__future__", "abc", "collections", "email", "functools", "itertools", "operator", "os", "pathlib", "posixpath", "re", "sys", "textwrap", "types", "contextlib", "importlib", "typing", "csv", "json", "builtins", "_frozen_importlib", "_typeshed", "enum"], "hash": "046ee8aeca9ebaea41bc0ffb833afb1ae1abddc8", "id": "importlib_metadata", "ignore_all": true, "interface_hash": "a977d3cf61ca5d5857ff3a6b13ad6ccec414e4e2", "mtime": 1750258716, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\importlib_metadata\\__init__.py", "plugin_data": null, "size": 37062, "suppressed": ["zipp.compat.overlay"], "version_id": "1.15.0"}