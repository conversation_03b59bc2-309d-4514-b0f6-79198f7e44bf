{".class": "MypyFile", "_fullname": "google.auth.environment_vars", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AWS_ACCESS_KEY_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.AWS_ACCESS_KEY_ID", "name": "AWS_ACCESS_KEY_ID", "type": "builtins.str"}}, "AWS_DEFAULT_REGION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.AWS_DEFAULT_REGION", "name": "AWS_DEFAULT_REGION", "type": "builtins.str"}}, "AWS_REGION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.AWS_REGION", "name": "AWS_REGION", "type": "builtins.str"}}, "AWS_SECRET_ACCESS_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.AWS_SECRET_ACCESS_KEY", "name": "AWS_SECRET_ACCESS_KEY", "type": "builtins.str"}}, "AWS_SESSION_TOKEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.AWS_SESSION_TOKEN", "name": "AWS_SESSION_TOKEN", "type": "builtins.str"}}, "CLOUD_SDK_CONFIG_DIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.CLOUD_SDK_CONFIG_DIR", "name": "CLOUD_SDK_CONFIG_DIR", "type": "builtins.str"}}, "CREDENTIALS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.CREDENTIALS", "name": "CREDENTIALS", "type": "builtins.str"}}, "GCE_METADATA_HOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.GCE_METADATA_HOST", "name": "GCE_METADATA_HOST", "type": "builtins.str"}}, "GCE_METADATA_IP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.GCE_METADATA_IP", "name": "GCE_METADATA_IP", "type": "builtins.str"}}, "GCE_METADATA_ROOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.GCE_METADATA_ROOT", "name": "GCE_METADATA_ROOT", "type": "builtins.str"}}, "GOOGLE_API_USE_CLIENT_CERTIFICATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.GOOGLE_API_USE_CLIENT_CERTIFICATE", "name": "GOOGLE_API_USE_CLIENT_CERTIFICATE", "type": "builtins.str"}}, "GOOGLE_CLOUD_QUOTA_PROJECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.GOOGLE_CLOUD_QUOTA_PROJECT", "name": "GOOGLE_CLOUD_QUOTA_PROJECT", "type": "builtins.str"}}, "LEGACY_APPENGINE_RUNTIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.LEGACY_APPENGINE_RUNTIME", "name": "LEGACY_APPENGINE_RUNTIME", "type": "builtins.str"}}, "LEGACY_PROJECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.LEGACY_PROJECT", "name": "LEGACY_PROJECT", "type": "builtins.str"}}, "PROJECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.environment_vars.PROJECT", "name": "PROJECT", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.environment_vars.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.environment_vars.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.environment_vars.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.environment_vars.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.environment_vars.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.environment_vars.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\google\\auth\\environment_vars.py"}