{".class": "MypyFile", "_fullname": "opentelemetry.context", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef"}, "OTEL_PYTHON_CONTEXT": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.environment_variables.OTEL_PYTHON_CONTEXT", "kind": "Gdef", "module_public": false}, "Token": {".class": "SymbolTableNode", "cross_ref": "_contextvars.Token", "kind": "Gdef", "module_public": false}, "_RUNTIME_CONTEXT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.context._RUNTIME_CONTEXT", "name": "_RUNTIME_CONTEXT", "type": "opentelemetry.context.context._RuntimeContext"}}, "_RuntimeContext": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context._RuntimeContext", "kind": "Gdef", "module_public": false}, "_SUPPRESS_HTTP_INSTRUMENTATION_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.context._SUPPRESS_HTTP_INSTRUMENTATION_KEY", "name": "_SUPPRESS_HTTP_INSTRUMENTATION_KEY", "type": "builtins.str"}}, "_SUPPRESS_INSTRUMENTATION_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.context._SUPPRESS_INSTRUMENTATION_KEY", "name": "_SUPPRESS_INSTRUMENTATION_KEY", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.context.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.context.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.context.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.context.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.context.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.context.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.context.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.context.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_load_runtime_context": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.context._load_runtime_context", "name": "_load_runtime_context", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_runtime_context", "ret_type": "opentelemetry.context.context._RuntimeContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "attach": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.context.attach", "name": "attach", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["context"], "arg_types": ["opentelemetry.context.context.Context"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attach", "ret_type": {".class": "Instance", "args": ["opentelemetry.context.context.Context"], "extra_attrs": null, "type_ref": "_contextvars.Token"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["keyname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.context.create_key", "name": "create_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["keyname"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_key", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detach": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.context.detach", "name": "detach", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["token"], "arg_types": [{".class": "Instance", "args": ["opentelemetry.context.context.Context"], "extra_attrs": null, "type_ref": "_contextvars.Token"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "entry_points": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.entry_points", "kind": "Gdef", "module_public": false}, "environ": {".class": "SymbolTableNode", "cross_ref": "os.environ", "kind": "Gdef", "module_public": false}, "get_current": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.context.get_current", "name": "get_current", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current", "ret_type": "opentelemetry.context.context.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.context.get_value", "name": "get_value", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key", "context"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_value", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.context.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "set_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["key", "value", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.context.set_value", "name": "set_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["key", "value", "context"], "arg_types": ["builtins.str", "builtins.object", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_value", "ret_type": "opentelemetry.context.context.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "uuid4": {".class": "SymbolTableNode", "cross_ref": "uuid.uuid4", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\context\\__init__.py"}