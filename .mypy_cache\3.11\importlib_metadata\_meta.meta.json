{"data_mtime": 1753366012, "dep_lines": [4, 1, 3, 5, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "os", "typing", "builtins", "_frozen_importlib", "abc", "types"], "hash": "2a524d5a1e3ea875d0604ee5c0c62438ad997021", "id": "importlib_metadata._meta", "ignore_all": true, "interface_hash": "194032b1d14ce1f800a4ddfe4dc29243cd5381cd", "mtime": 1750258716, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\importlib_metadata\\_meta.py", "plugin_data": null, "size": 1765, "suppressed": [], "version_id": "1.15.0"}