{".class": "MypyFile", "_fullname": "aiohttp.web_middlewares", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Application": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_app.Application", "kind": "Gdef", "module_public": false}, "HTTPMove": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPMove", "kind": "Gdef", "module_public": false}, "HTTPPermanentRedirect": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPPermanentRedirect", "kind": "Gdef", "module_public": false}, "Handler": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.Handler", "kind": "Gdef", "module_public": false}, "Middleware": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.Middleware", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_request.Request", "kind": "Gdef", "module_public": false}, "StreamResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_response.StreamResponse", "kind": "Gdef", "module_public": false}, "SystemRoute": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.SystemRoute", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "_Func": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_middlewares._Func", "name": "_Func", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.web_middlewares.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_middlewares.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_middlewares.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_middlewares.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_middlewares.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_middlewares.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web_middlewares.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_check_request_resolves": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web_middlewares._check_request_resolves", "name": "_check_request_resolves", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request", "path"], "arg_types": ["aiohttp.web_request.Request", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_request_resolves", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "aiohttp.web_request.Request"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fix_request_current_app": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_middlewares._fix_request_current_app", "name": "_fix_request_current_app", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["app"], "arg_types": ["aiohttp.web_app.Application"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fix_request_current_app", "ret_type": "aiohttp.typedefs.Middleware", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "middleware": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_middlewares.middleware", "name": "middleware", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_middlewares._Func", "id": -1, "name": "_Func", "namespace": "aiohttp.web_middlewares.middleware", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "middleware", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_middlewares._Func", "id": -1, "name": "_Func", "namespace": "aiohttp.web_middlewares.middleware", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.web_middlewares._Func", "id": -1, "name": "_Func", "namespace": "aiohttp.web_middlewares.middleware", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "normalize_path_middleware": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["append_slash", "remove_slash", "merge_slashes", "redirect_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web_middlewares.normalize_path_middleware", "name": "normalize_path_middleware", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["append_slash", "remove_slash", "merge_slashes", "redirect_class"], "arg_types": ["builtins.bool", "builtins.bool", "builtins.bool", {".class": "TypeType", "item": "aiohttp.web_exceptions.HTTPMove"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_path_middleware", "ret_type": "aiohttp.typedefs.Middleware", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web_middlewares.py"}