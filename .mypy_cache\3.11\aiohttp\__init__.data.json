{".class": "MypyFile", "_fullname": "aiohttp", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncIterablePayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.AsyncIterablePayload", "kind": "Gdef"}, "AsyncResolver": {".class": "SymbolTableNode", "cross_ref": "aiohttp.resolver.AsyncResolver", "kind": "Gdef"}, "BadContentDispositionHeader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.multipart.BadContentDispositionHeader", "kind": "Gdef"}, "BadContentDispositionParam": {".class": "SymbolTableNode", "cross_ref": "aiohttp.multipart.BadContentDispositionParam", "kind": "Gdef"}, "BaseConnector": {".class": "SymbolTableNode", "cross_ref": "aiohttp.connector.BaseConnector", "kind": "Gdef"}, "BasicAuth": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.BasicAuth", "kind": "Gdef"}, "BodyPartReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.multipart.BodyPartReader", "kind": "Gdef"}, "BufferedReaderPayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.BufferedReaderPayload", "kind": "Gdef"}, "BytesIOPayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.BytesIOPayload", "kind": "Gdef"}, "BytesPayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.BytesPayload", "kind": "Gdef"}, "ChainMapProxy": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.ChainMapProxy", "kind": "Gdef"}, "ClientConnectionError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectionError", "kind": "Gdef"}, "ClientConnectionResetError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectionResetError", "kind": "Gdef"}, "ClientConnectorCertificateError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectorCertificateError", "kind": "Gdef"}, "ClientConnectorDNSError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectorDNSError", "kind": "Gdef"}, "ClientConnectorError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectorError", "kind": "Gdef"}, "ClientConnectorSSLError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientConnectorSSLError", "kind": "Gdef"}, "ClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientError", "kind": "Gdef"}, "ClientHttpProxyError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientHttpProxyError", "kind": "Gdef"}, "ClientOSError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientOSError", "kind": "Gdef"}, "ClientPayloadError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientPayloadError", "kind": "Gdef"}, "ClientProxyConnectionError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientProxyConnectionError", "kind": "Gdef"}, "ClientRequest": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.ClientRequest", "kind": "Gdef"}, "ClientResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.ClientResponse", "kind": "Gdef"}, "ClientResponseError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientResponseError", "kind": "Gdef"}, "ClientSSLError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientSSLError", "kind": "Gdef"}, "ClientSession": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client.ClientSession", "kind": "Gdef"}, "ClientTimeout": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client.ClientTimeout", "kind": "Gdef"}, "ClientWSTimeout": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_ws.ClientWSTimeout", "kind": "Gdef"}, "ClientWebSocketResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_ws.ClientWebSocketResponse", "kind": "Gdef"}, "ConnectionTimeoutError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ConnectionTimeoutError", "kind": "Gdef"}, "ContentTypeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ContentTypeError", "kind": "Gdef"}, "CookieJar": {".class": "SymbolTableNode", "cross_ref": "aiohttp.cookiejar.CookieJar", "kind": "Gdef"}, "DataQueue": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.DataQueue", "kind": "Gdef"}, "DefaultResolver": {".class": "SymbolTableNode", "cross_ref": "aiohttp.resolver.DefaultResolver", "kind": "Gdef"}, "DummyCookieJar": {".class": "SymbolTableNode", "cross_ref": "aiohttp.cookiejar.DummyCookieJar", "kind": "Gdef"}, "EMPTY_PAYLOAD": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.EMPTY_PAYLOAD", "kind": "Gdef"}, "ETag": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.ETag", "kind": "Gdef"}, "EofStream": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.EofStream", "kind": "Gdef"}, "Fingerprint": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.Fingerprint", "kind": "Gdef"}, "FlowControlDataQueue": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.FlowControlDataQueue", "kind": "Gdef"}, "FormData": {".class": "SymbolTableNode", "cross_ref": "aiohttp.formdata.FormData", "kind": "Gdef"}, "GunicornUVLoopWebWorker": {".class": "SymbolTableNode", "cross_ref": "aiohttp.worker.GunicornUVLoopWebWorker", "kind": "Gdef"}, "GunicornWebWorker": {".class": "SymbolTableNode", "cross_ref": "aiohttp.worker.GunicornWebWorker", "kind": "Gdef"}, "HttpVersion": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion", "kind": "Gdef"}, "HttpVersion10": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion10", "kind": "Gdef"}, "HttpVersion11": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion11", "kind": "Gdef"}, "IOBasePayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.IOBasePayload", "kind": "Gdef"}, "InvalidURL": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.InvalidURL", "kind": "Gdef"}, "InvalidUrlClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.InvalidUrlClientError", "kind": "Gdef"}, "InvalidUrlRedirectClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.InvalidUrlRedirectClientError", "kind": "Gdef"}, "JsonPayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.JsonPayload", "kind": "Gdef"}, "MultipartReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.multipart.MultipartReader", "kind": "Gdef"}, "MultipartWriter": {".class": "SymbolTableNode", "cross_ref": "aiohttp.multipart.MultipartWriter", "kind": "Gdef"}, "NamedPipeConnector": {".class": "SymbolTableNode", "cross_ref": "aiohttp.connector.NamedPipeConnector", "kind": "Gdef"}, "NonHttpUrlClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.NonHttpUrlClientError", "kind": "Gdef"}, "NonHttpUrlRedirectClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.NonHttpUrlRedirectClientError", "kind": "Gdef"}, "PAYLOAD_REGISTRY": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.PAYLOAD_REGISTRY", "kind": "Gdef"}, "Payload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.Payload", "kind": "Gdef"}, "RedirectClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.RedirectClientError", "kind": "Gdef"}, "RequestInfo": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.RequestInfo", "kind": "Gdef"}, "ServerConnectionError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ServerConnectionError", "kind": "Gdef"}, "ServerDisconnectedError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ServerDisconnectedError", "kind": "Gdef"}, "ServerFingerprintMismatch": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ServerFingerprintMismatch", "kind": "Gdef"}, "ServerTimeoutError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ServerTimeoutError", "kind": "Gdef"}, "SocketTimeoutError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.SocketTimeoutError", "kind": "Gdef"}, "StreamReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.StreamReader", "kind": "Gdef"}, "StringIOPayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.StringIOPayload", "kind": "Gdef"}, "StringPayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.StringPayload", "kind": "Gdef"}, "TCPConnector": {".class": "SymbolTableNode", "cross_ref": "aiohttp.connector.TCPConnector", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TextIOPayload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.TextIOPayload", "kind": "Gdef"}, "ThreadedResolver": {".class": "SymbolTableNode", "cross_ref": "aiohttp.resolver.ThreadedResolver", "kind": "Gdef"}, "TooManyRedirects": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.TooManyRedirects", "kind": "Gdef"}, "TraceConfig": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceConfig", "kind": "Gdef"}, "TraceConnectionCreateEndParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceConnectionCreateEndParams", "kind": "Gdef"}, "TraceConnectionCreateStartParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceConnectionCreateStartParams", "kind": "Gdef"}, "TraceConnectionQueuedEndParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceConnectionQueuedEndParams", "kind": "Gdef"}, "TraceConnectionQueuedStartParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceConnectionQueuedStartParams", "kind": "Gdef"}, "TraceConnectionReuseconnParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceConnectionReuseconnParams", "kind": "Gdef"}, "TraceDnsCacheHitParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceDnsCacheHitParams", "kind": "Gdef"}, "TraceDnsCacheMissParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceDnsCacheMissParams", "kind": "Gdef"}, "TraceDnsResolveHostEndParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceDnsResolveHostEndParams", "kind": "Gdef"}, "TraceDnsResolveHostStartParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceDnsResolveHostStartParams", "kind": "Gdef"}, "TraceRequestChunkSentParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceRequestChunkSentParams", "kind": "Gdef"}, "TraceRequestEndParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceRequestEndParams", "kind": "Gdef"}, "TraceRequestExceptionParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceRequestExceptionParams", "kind": "Gdef"}, "TraceRequestHeadersSentParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceRequestHeadersSentParams", "kind": "Gdef"}, "TraceRequestRedirectParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceRequestRedirectParams", "kind": "Gdef"}, "TraceRequestStartParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceRequestStartParams", "kind": "Gdef"}, "TraceResponseChunkReceivedParams": {".class": "SymbolTableNode", "cross_ref": "aiohttp.tracing.TraceResponseChunkReceivedParams", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "UnixConnector": {".class": "SymbolTableNode", "cross_ref": "aiohttp.connector.UnixConnector", "kind": "Gdef"}, "WSCloseCode": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSCloseCode", "kind": "Gdef"}, "WSMessage": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMessage", "kind": "Gdef"}, "WSMessageTypeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.WSMessageTypeError", "kind": "Gdef"}, "WSMsgType": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMsgType", "kind": "Gdef"}, "WSServerHandshakeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.WSServerHandshakeError", "kind": "Gdef"}, "WebSocketError": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WebSocketError", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "aiohttp.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__dir__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.__dir__", "name": "__dir__", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__dir__", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.__version__", "name": "__version__", "type": "builtins.str"}}, "content_disposition_filename": {".class": "SymbolTableNode", "cross_ref": "aiohttp.multipart.content_disposition_filename", "kind": "Gdef"}, "get_payload": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.get_payload", "kind": "Gdef"}, "hdrs": {".class": "SymbolTableNode", "cross_ref": "aiohttp.hdrs", "kind": "Gdef"}, "parse_content_disposition": {".class": "SymbolTableNode", "cross_ref": "aiohttp.multipart.parse_content_disposition", "kind": "Gdef"}, "payload_type": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload.payload_type", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client.request", "kind": "Gdef"}, "streamer": {".class": "SymbolTableNode", "cross_ref": "aiohttp.payload_streamer.streamer", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\__init__.py"}