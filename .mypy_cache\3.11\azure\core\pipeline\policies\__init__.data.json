{".class": "MypyFile", "_fullname": "azure.core.pipeline.policies", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncBearerTokenCredentialPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._authentication_async.AsyncBearerTokenCredentialPolicy", "kind": "Gdef"}, "AsyncHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy", "kind": "Gdef"}, "AsyncRedirectPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._redirect_async.AsyncRedirectPolicy", "kind": "Gdef"}, "AsyncRetryPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._retry_async.AsyncRetryPolicy", "kind": "Gdef"}, "AzureKeyCredentialPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "kind": "Gdef"}, "AzureSasCredentialPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "kind": "Gdef"}, "BearerTokenCredentialPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "kind": "Gdef"}, "ContentDecodePolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.ContentDecodePolicy", "kind": "Gdef"}, "CustomHookPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._custom_hook.CustomHookPolicy", "kind": "Gdef"}, "DistributedTracingPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._distributed_tracing.DistributedTracingPolicy", "kind": "Gdef"}, "HTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.HTTPPolicy", "kind": "Gdef"}, "HeadersPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.HeadersPolicy", "kind": "Gdef"}, "HttpLoggingPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.HttpLoggingPolicy", "kind": "Gdef"}, "NetworkTraceLoggingPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.NetworkTraceLoggingPolicy", "kind": "Gdef"}, "ProxyPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.ProxyPolicy", "kind": "Gdef"}, "RedirectPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._redirect.RedirectPolicy", "kind": "Gdef"}, "RequestHistory": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.RequestHistory", "kind": "Gdef"}, "RequestIdPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.RequestIdPolicy", "kind": "Gdef"}, "RetryMode": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._retry.RetryMode", "kind": "Gdef"}, "RetryPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._retry.RetryPolicy", "kind": "Gdef"}, "SansIOHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "kind": "Gdef"}, "SensitiveHeaderCleanupPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._sensitive_header_cleanup_policy.SensitiveHeaderCleanupPolicy", "kind": "Gdef"}, "UserAgentPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._universal.UserAgentPolicy", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\policies\\__init__.py"}