{".class": "MypyFile", "_fullname": "aiohttp.hdrs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACCEPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCEPT", "name": "ACCEPT", "type": "multidict.istr"}}, "ACCEPT_CHARSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCEPT_CHARSET", "name": "ACCEPT_CHARSET", "type": "multidict.istr"}}, "ACCEPT_ENCODING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCEPT_ENCODING", "name": "ACCEPT_ENCODING", "type": "multidict.istr"}}, "ACCEPT_LANGUAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCEPT_LANGUAGE", "name": "ACCEPT_LANGUAGE", "type": "multidict.istr"}}, "ACCEPT_RANGES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCEPT_RANGES", "name": "ACCEPT_RANGES", "type": "multidict.istr"}}, "ACCESS_CONTROL_ALLOW_CREDENTIALS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCESS_CONTROL_ALLOW_CREDENTIALS", "name": "ACCESS_CONTROL_ALLOW_CREDENTIALS", "type": "multidict.istr"}}, "ACCESS_CONTROL_ALLOW_HEADERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCESS_CONTROL_ALLOW_HEADERS", "name": "ACCESS_CONTROL_ALLOW_HEADERS", "type": "multidict.istr"}}, "ACCESS_CONTROL_ALLOW_METHODS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCESS_CONTROL_ALLOW_METHODS", "name": "ACCESS_CONTROL_ALLOW_METHODS", "type": "multidict.istr"}}, "ACCESS_CONTROL_ALLOW_ORIGIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCESS_CONTROL_ALLOW_ORIGIN", "name": "ACCESS_CONTROL_ALLOW_ORIGIN", "type": "multidict.istr"}}, "ACCESS_CONTROL_EXPOSE_HEADERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCESS_CONTROL_EXPOSE_HEADERS", "name": "ACCESS_CONTROL_EXPOSE_HEADERS", "type": "multidict.istr"}}, "ACCESS_CONTROL_MAX_AGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCESS_CONTROL_MAX_AGE", "name": "ACCESS_CONTROL_MAX_AGE", "type": "multidict.istr"}}, "ACCESS_CONTROL_REQUEST_HEADERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCESS_CONTROL_REQUEST_HEADERS", "name": "ACCESS_CONTROL_REQUEST_HEADERS", "type": "multidict.istr"}}, "ACCESS_CONTROL_REQUEST_METHOD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ACCESS_CONTROL_REQUEST_METHOD", "name": "ACCESS_CONTROL_REQUEST_METHOD", "type": "multidict.istr"}}, "AGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.AGE", "name": "AGE", "type": "multidict.istr"}}, "ALLOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ALLOW", "name": "ALLOW", "type": "multidict.istr"}}, "AUTHORIZATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.AUTHORIZATION", "name": "AUTHORIZATION", "type": "multidict.istr"}}, "CACHE_CONTROL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CACHE_CONTROL", "name": "CACHE_CONTROL", "type": "multidict.istr"}}, "CONNECTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONNECTION", "name": "CONNECTION", "type": "multidict.istr"}}, "CONTENT_DISPOSITION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONTENT_DISPOSITION", "name": "CONTENT_DISPOSITION", "type": "multidict.istr"}}, "CONTENT_ENCODING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONTENT_ENCODING", "name": "CONTENT_ENCODING", "type": "multidict.istr"}}, "CONTENT_LANGUAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONTENT_LANGUAGE", "name": "CONTENT_LANGUAGE", "type": "multidict.istr"}}, "CONTENT_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONTENT_LENGTH", "name": "CONTENT_LENGTH", "type": "multidict.istr"}}, "CONTENT_LOCATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONTENT_LOCATION", "name": "CONTENT_LOCATION", "type": "multidict.istr"}}, "CONTENT_MD5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONTENT_MD5", "name": "CONTENT_MD5", "type": "multidict.istr"}}, "CONTENT_RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONTENT_RANGE", "name": "CONTENT_RANGE", "type": "multidict.istr"}}, "CONTENT_TRANSFER_ENCODING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONTENT_TRANSFER_ENCODING", "name": "CONTENT_TRANSFER_ENCODING", "type": "multidict.istr"}}, "CONTENT_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.CONTENT_TYPE", "name": "CONTENT_TYPE", "type": "multidict.istr"}}, "COOKIE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.COOKIE", "name": "COOKIE", "type": "multidict.istr"}}, "DATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.DATE", "name": "DATE", "type": "multidict.istr"}}, "DESTINATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.DESTINATION", "name": "DESTINATION", "type": "multidict.istr"}}, "DIGEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.DIGEST", "name": "DIGEST", "type": "multidict.istr"}}, "ETAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ETAG", "name": "ETAG", "type": "multidict.istr"}}, "EXPECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.EXPECT", "name": "EXPECT", "type": "multidict.istr"}}, "EXPIRES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.EXPIRES", "name": "EXPIRES", "type": "multidict.istr"}}, "FORWARDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.FORWARDED", "name": "FORWARDED", "type": "multidict.istr"}}, "FROM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.FROM", "name": "FROM", "type": "multidict.istr"}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "HOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.HOST", "name": "HOST", "type": "multidict.istr"}}, "HOST_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.hdrs.HOST_ALL", "name": "HOST_ALL", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "IF_MATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.IF_MATCH", "name": "IF_MATCH", "type": "multidict.istr"}}, "IF_MODIFIED_SINCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.IF_MODIFIED_SINCE", "name": "IF_MODIFIED_SINCE", "type": "multidict.istr"}}, "IF_NONE_MATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.IF_NONE_MATCH", "name": "IF_NONE_MATCH", "type": "multidict.istr"}}, "IF_RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.IF_RANGE", "name": "IF_RANGE", "type": "multidict.istr"}}, "IF_UNMODIFIED_SINCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.IF_UNMODIFIED_SINCE", "name": "IF_UNMODIFIED_SINCE", "type": "multidict.istr"}}, "KEEP_ALIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.KEEP_ALIVE", "name": "KEEP_ALIVE", "type": "multidict.istr"}}, "LAST_EVENT_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.LAST_EVENT_ID", "name": "LAST_EVENT_ID", "type": "multidict.istr"}}, "LAST_MODIFIED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.LAST_MODIFIED", "name": "LAST_MODIFIED", "type": "multidict.istr"}}, "LINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.LINK", "name": "LINK", "type": "multidict.istr"}}, "LOCATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.LOCATION", "name": "LOCATION", "type": "multidict.istr"}}, "MAX_FORWARDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.MAX_FORWARDS", "name": "MAX_FORWARDS", "type": "multidict.istr"}}, "METH_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_ALL", "name": "METH_ALL", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "METH_ANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "*", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_ANY", "name": "METH_ANY", "type": "builtins.str"}}, "METH_CONNECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "CONNECT", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_CONNECT", "name": "METH_CONNECT", "type": "builtins.str"}}, "METH_CONNECT_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_CONNECT_ALL", "name": "METH_CONNECT_ALL", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "METH_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "DELETE", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_DELETE", "name": "METH_DELETE", "type": "builtins.str"}}, "METH_GET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "GET", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_GET", "name": "METH_GET", "type": "builtins.str"}}, "METH_HEAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "HEAD", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_HEAD", "name": "METH_HEAD", "type": "builtins.str"}}, "METH_HEAD_ALL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_HEAD_ALL", "name": "METH_HEAD_ALL", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "METH_OPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "OPTIONS", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_OPTIONS", "name": "METH_OPTIONS", "type": "builtins.str"}}, "METH_PATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "PATCH", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_PATCH", "name": "METH_PATCH", "type": "builtins.str"}}, "METH_POST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "POST", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_POST", "name": "METH_POST", "type": "builtins.str"}}, "METH_PUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "PUT", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_PUT", "name": "METH_PUT", "type": "builtins.str"}}, "METH_TRACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "TRACE", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.METH_TRACE", "name": "METH_TRACE", "type": "builtins.str"}}, "ORIGIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.ORIGIN", "name": "ORIGIN", "type": "multidict.istr"}}, "PRAGMA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.PRAGMA", "name": "PRAGMA", "type": "multidict.istr"}}, "PROXY_AUTHENTICATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.PROXY_AUTHENTICATE", "name": "PROXY_AUTHENTICATE", "type": "multidict.istr"}}, "PROXY_AUTHORIZATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.PROXY_AUTHORIZATION", "name": "PROXY_AUTHORIZATION", "type": "multidict.istr"}}, "RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.RANGE", "name": "RANGE", "type": "multidict.istr"}}, "REFERER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.REFERER", "name": "REFERER", "type": "multidict.istr"}}, "RETRY_AFTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.RETRY_AFTER", "name": "RETRY_AFTER", "type": "multidict.istr"}}, "SEC_WEBSOCKET_ACCEPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.SEC_WEBSOCKET_ACCEPT", "name": "SEC_WEBSOCKET_ACCEPT", "type": "multidict.istr"}}, "SEC_WEBSOCKET_EXTENSIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.SEC_WEBSOCKET_EXTENSIONS", "name": "SEC_WEBSOCKET_EXTENSIONS", "type": "multidict.istr"}}, "SEC_WEBSOCKET_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.SEC_WEBSOCKET_KEY", "name": "SEC_WEBSOCKET_KEY", "type": "multidict.istr"}}, "SEC_WEBSOCKET_KEY1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.SEC_WEBSOCKET_KEY1", "name": "SEC_WEBSOCKET_KEY1", "type": "multidict.istr"}}, "SEC_WEBSOCKET_PROTOCOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.SEC_WEBSOCKET_PROTOCOL", "name": "SEC_WEBSOCKET_PROTOCOL", "type": "multidict.istr"}}, "SEC_WEBSOCKET_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.SEC_WEBSOCKET_VERSION", "name": "SEC_WEBSOCKET_VERSION", "type": "multidict.istr"}}, "SERVER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.SERVER", "name": "SERVER", "type": "multidict.istr"}}, "SET_COOKIE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.SET_COOKIE", "name": "SET_COOKIE", "type": "multidict.istr"}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.TE", "name": "TE", "type": "multidict.istr"}}, "TRAILER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.TRAILER", "name": "TRAILER", "type": "multidict.istr"}}, "TRANSFER_ENCODING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.TRANSFER_ENCODING", "name": "TRANSFER_ENCODING", "type": "multidict.istr"}}, "UPGRADE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.UPGRADE", "name": "UPGRADE", "type": "multidict.istr"}}, "URI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.URI", "name": "URI", "type": "multidict.istr"}}, "USER_AGENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.USER_AGENT", "name": "USER_AGENT", "type": "multidict.istr"}}, "VARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.VARY", "name": "VARY", "type": "multidict.istr"}}, "VIA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.VIA", "name": "VIA", "type": "multidict.istr"}}, "WANT_DIGEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.WANT_DIGEST", "name": "WANT_DIGEST", "type": "multidict.istr"}}, "WARNING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.WARNING", "name": "WARNING", "type": "multidict.istr"}}, "WWW_AUTHENTICATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.WWW_AUTHENTICATE", "name": "WWW_AUTHENTICATE", "type": "multidict.istr"}}, "X_FORWARDED_FOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.X_FORWARDED_FOR", "name": "X_FORWARDED_FOR", "type": "multidict.istr"}}, "X_FORWARDED_HOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.X_FORWARDED_HOST", "name": "X_FORWARDED_HOST", "type": "multidict.istr"}}, "X_FORWARDED_PROTO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp.hdrs.X_FORWARDED_PROTO", "name": "X_FORWARDED_PROTO", "type": "multidict.istr"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.hdrs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.hdrs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.hdrs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.hdrs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.hdrs.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.hdrs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "istr": {".class": "SymbolTableNode", "cross_ref": "multidict.istr", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\hdrs.py"}