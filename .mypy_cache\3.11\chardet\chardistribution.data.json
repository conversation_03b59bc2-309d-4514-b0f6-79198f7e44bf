{".class": "MypyFile", "_fullname": "chardet.chardistribution", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BIG5_CHAR_TO_FREQ_ORDER": {".class": "SymbolTableNode", "cross_ref": "chardet.big5freq.BIG5_CHAR_TO_FREQ_ORDER", "kind": "Gdef"}, "BIG5_TABLE_SIZE": {".class": "SymbolTableNode", "cross_ref": "chardet.big5freq.BIG5_TABLE_SIZE", "kind": "Gdef"}, "BIG5_TYPICAL_DISTRIBUTION_RATIO": {".class": "SymbolTableNode", "cross_ref": "chardet.big5freq.BIG5_TYPICAL_DISTRIBUTION_RATIO", "kind": "Gdef"}, "Big5DistributionAnalysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.chardistribution.CharDistributionAnalysis"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.chardistribution.Big5DistributionAnalysis", "name": "Big5DistributionAnalysis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.Big5DistributionAnalysis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.chardistribution", "mro": ["chardet.chardistribution.Big5DistributionAnalysis", "chardet.chardistribution.CharDistributionAnalysis", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.Big5DistributionAnalysis.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.Big5DistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Big5DistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.Big5DistributionAnalysis.get_order", "name": "get_order", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "arg_types": ["chardet.chardistribution.Big5DistributionAnalysis", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order of Big5DistributionAnalysis", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.chardistribution.Big5DistributionAnalysis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.chardistribution.Big5DistributionAnalysis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CharDistributionAnalysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.chardistribution.CharDistributionAnalysis", "name": "CharDistributionAnalysis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.CharDistributionAnalysis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.chardistribution", "mro": ["chardet.chardistribution.CharDistributionAnalysis", "builtins.object"], "names": {".class": "SymbolTable", "ENOUGH_DATA_THRESHOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "chardet.chardistribution.CharDistributionAnalysis.ENOUGH_DATA_THRESHOLD", "name": "ENOUGH_DATA_THRESHOLD", "type": "builtins.int"}}, "MINIMUM_DATA_THRESHOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "chardet.chardistribution.CharDistributionAnalysis.MINIMUM_DATA_THRESHOLD", "name": "MINIMUM_DATA_THRESHOLD", "type": "builtins.int"}}, "SURE_NO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "chardet.chardistribution.CharDistributionAnalysis.SURE_NO", "name": "SURE_NO", "type": "builtins.float"}}, "SURE_YES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "chardet.chardistribution.CharDistributionAnalysis.SURE_YES", "name": "SURE_YES", "type": "builtins.float"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.CharDistributionAnalysis.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.CharDistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CharDistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_char_to_freq_order": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "chardet.chardistribution.CharDistributionAnalysis._char_to_freq_order", "name": "_char_to_freq_order", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_done": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.chardistribution.CharDistributionAnalysis._done", "name": "_done", "type": "builtins.bool"}}, "_freq_chars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.chardistribution.CharDistributionAnalysis._freq_chars", "name": "_freq_chars", "type": "builtins.int"}}, "_table_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.chardistribution.CharDistributionAnalysis._table_size", "name": "_table_size", "type": "builtins.int"}}, "_total_chars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.chardistribution.CharDistributionAnalysis._total_chars", "name": "_total_chars", "type": "builtins.int"}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "char", "char_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.CharDistributionAnalysis.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "char", "char_len"], "arg_types": ["chardet.chardistribution.CharDistributionAnalysis", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of CharDistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_confidence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.CharDistributionAnalysis.get_confidence", "name": "get_confidence", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.CharDistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_confidence of CharDistributionAnalysis", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.CharDistributionAnalysis.get_order", "name": "get_order", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_"], "arg_types": ["chardet.chardistribution.CharDistributionAnalysis", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order of CharDistributionAnalysis", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "got_enough_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.CharDistributionAnalysis.got_enough_data", "name": "got_enough_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.CharDistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "got_enough_data of CharDistributionAnalysis", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.CharDistributionAnalysis.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.CharDistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of CharDistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typical_distribution_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.chardistribution.CharDistributionAnalysis.typical_distribution_ratio", "name": "typical_distribution_ratio", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.chardistribution.CharDistributionAnalysis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.chardistribution.CharDistributionAnalysis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EUCJPDistributionAnalysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.chardistribution.CharDistributionAnalysis"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.chardistribution.EUCJPDistributionAnalysis", "name": "EUCJPDistributionAnalysis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.EUCJPDistributionAnalysis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.chardistribution", "mro": ["chardet.chardistribution.EUCJPDistributionAnalysis", "chardet.chardistribution.CharDistributionAnalysis", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.EUCJPDistributionAnalysis.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.EUCJPDistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EUCJPDistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.EUCJPDistributionAnalysis.get_order", "name": "get_order", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "arg_types": ["chardet.chardistribution.EUCJPDistributionAnalysis", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order of EUCJPDistributionAnalysis", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.chardistribution.EUCJPDistributionAnalysis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.chardistribution.EUCJPDistributionAnalysis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EUCKRDistributionAnalysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.chardistribution.CharDistributionAnalysis"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.chardistribution.EUCKRDistributionAnalysis", "name": "EUCKRDistributionAnalysis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.EUCKRDistributionAnalysis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.chardistribution", "mro": ["chardet.chardistribution.EUCKRDistributionAnalysis", "chardet.chardistribution.CharDistributionAnalysis", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.EUCKRDistributionAnalysis.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.EUCKRDistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EUCKRDistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.EUCKRDistributionAnalysis.get_order", "name": "get_order", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "arg_types": ["chardet.chardistribution.EUCKRDistributionAnalysis", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order of EUCKRDistributionAnalysis", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.chardistribution.EUCKRDistributionAnalysis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.chardistribution.EUCKRDistributionAnalysis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EUCKR_CHAR_TO_FREQ_ORDER": {".class": "SymbolTableNode", "cross_ref": "chardet.euckrfreq.EUCKR_CHAR_TO_FREQ_ORDER", "kind": "Gdef"}, "EUCKR_TABLE_SIZE": {".class": "SymbolTableNode", "cross_ref": "chardet.euckrfreq.EUCKR_TABLE_SIZE", "kind": "Gdef"}, "EUCKR_TYPICAL_DISTRIBUTION_RATIO": {".class": "SymbolTableNode", "cross_ref": "chardet.euckrfreq.EUCKR_TYPICAL_DISTRIBUTION_RATIO", "kind": "Gdef"}, "EUCTWDistributionAnalysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.chardistribution.CharDistributionAnalysis"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.chardistribution.EUCTWDistributionAnalysis", "name": "EUCTWDistributionAnalysis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.EUCTWDistributionAnalysis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.chardistribution", "mro": ["chardet.chardistribution.EUCTWDistributionAnalysis", "chardet.chardistribution.CharDistributionAnalysis", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.EUCTWDistributionAnalysis.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.EUCTWDistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EUCTWDistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.EUCTWDistributionAnalysis.get_order", "name": "get_order", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "arg_types": ["chardet.chardistribution.EUCTWDistributionAnalysis", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order of EUCTWDistributionAnalysis", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.chardistribution.EUCTWDistributionAnalysis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.chardistribution.EUCTWDistributionAnalysis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EUCTW_CHAR_TO_FREQ_ORDER": {".class": "SymbolTableNode", "cross_ref": "chardet.euctwfreq.EUCTW_CHAR_TO_FREQ_ORDER", "kind": "Gdef"}, "EUCTW_TABLE_SIZE": {".class": "SymbolTableNode", "cross_ref": "chardet.euctwfreq.EUCTW_TABLE_SIZE", "kind": "Gdef"}, "EUCTW_TYPICAL_DISTRIBUTION_RATIO": {".class": "SymbolTableNode", "cross_ref": "chardet.euctwfreq.EUCTW_TYPICAL_DISTRIBUTION_RATIO", "kind": "Gdef"}, "GB2312DistributionAnalysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.chardistribution.CharDistributionAnalysis"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.chardistribution.GB2312DistributionAnalysis", "name": "GB2312DistributionAnalysis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.GB2312DistributionAnalysis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.chardistribution", "mro": ["chardet.chardistribution.GB2312DistributionAnalysis", "chardet.chardistribution.CharDistributionAnalysis", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.GB2312DistributionAnalysis.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.GB2312DistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GB2312DistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.GB2312DistributionAnalysis.get_order", "name": "get_order", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "arg_types": ["chardet.chardistribution.GB2312DistributionAnalysis", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order of GB2312DistributionAnalysis", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.chardistribution.GB2312DistributionAnalysis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.chardistribution.GB2312DistributionAnalysis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GB2312_CHAR_TO_FREQ_ORDER": {".class": "SymbolTableNode", "cross_ref": "chardet.gb2312freq.GB2312_CHAR_TO_FREQ_ORDER", "kind": "Gdef"}, "GB2312_TABLE_SIZE": {".class": "SymbolTableNode", "cross_ref": "chardet.gb2312freq.GB2312_TABLE_SIZE", "kind": "Gdef"}, "GB2312_TYPICAL_DISTRIBUTION_RATIO": {".class": "SymbolTableNode", "cross_ref": "chardet.gb2312freq.GB2312_TYPICAL_DISTRIBUTION_RATIO", "kind": "Gdef"}, "JIS_CHAR_TO_FREQ_ORDER": {".class": "SymbolTableNode", "cross_ref": "chardet.jisfreq.JIS_CHAR_TO_FREQ_ORDER", "kind": "Gdef"}, "JIS_TABLE_SIZE": {".class": "SymbolTableNode", "cross_ref": "chardet.jisfreq.JIS_TABLE_SIZE", "kind": "Gdef"}, "JIS_TYPICAL_DISTRIBUTION_RATIO": {".class": "SymbolTableNode", "cross_ref": "chardet.jisfreq.JIS_TYPICAL_DISTRIBUTION_RATIO", "kind": "Gdef"}, "JOHABDistributionAnalysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.chardistribution.CharDistributionAnalysis"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.chardistribution.JOHABDistributionAnalysis", "name": "JOHABDistributionAnalysis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.JOHABDistributionAnalysis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.chardistribution", "mro": ["chardet.chardistribution.JOHABDistributionAnalysis", "chardet.chardistribution.CharDistributionAnalysis", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.JOHABDistributionAnalysis.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.JOHABDistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of JOHABDistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.JOHABDistributionAnalysis.get_order", "name": "get_order", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "arg_types": ["chardet.chardistribution.JOHABDistributionAnalysis", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order of JOHABDistributionAnalysis", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.chardistribution.JOHABDistributionAnalysis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.chardistribution.JOHABDistributionAnalysis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JOHAB_TO_EUCKR_ORDER_TABLE": {".class": "SymbolTableNode", "cross_ref": "chardet.johabfreq.JOHAB_TO_EUCKR_ORDER_TABLE", "kind": "Gdef"}, "SJISDistributionAnalysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.chardistribution.CharDistributionAnalysis"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.chardistribution.SJISDistributionAnalysis", "name": "SJISDistributionAnalysis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.SJISDistributionAnalysis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.chardistribution", "mro": ["chardet.chardistribution.SJISDistributionAnalysis", "chardet.chardistribution.CharDistributionAnalysis", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.SJISDistributionAnalysis.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.chardistribution.SJISDistributionAnalysis"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SJISDistributionAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.chardistribution.SJISDistributionAnalysis.get_order", "name": "get_order", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "arg_types": ["chardet.chardistribution.SJISDistributionAnalysis", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_order of SJISDistributionAnalysis", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.chardistribution.SJISDistributionAnalysis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.chardistribution.SJISDistributionAnalysis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.chardistribution.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.chardistribution.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.chardistribution.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.chardistribution.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.chardistribution.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.chardistribution.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\chardet\\chardistribution.py"}