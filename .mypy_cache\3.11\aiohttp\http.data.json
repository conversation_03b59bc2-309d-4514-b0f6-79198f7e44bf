{".class": "MypyFile", "_fullname": "aiohttp.http", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HTTPStatus": {".class": "SymbolTableNode", "cross_ref": "http.HTTPStatus", "kind": "Gdef", "module_public": false}, "HeadersParser": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.HeadersParser", "kind": "Gdef"}, "HttpParser": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.HttpParser", "kind": "Gdef"}, "HttpProcessingError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_exceptions.HttpProcessingError", "kind": "Gdef"}, "HttpRequestParser": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.HttpRequestParser", "kind": "Gdef"}, "HttpResponseParser": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.HttpResponseParser", "kind": "Gdef"}, "HttpVersion": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion", "kind": "Gdef"}, "HttpVersion10": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion10", "kind": "Gdef"}, "HttpVersion11": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.HttpVersion11", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "RESPONSES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "aiohttp.http.RESPONSES", "name": "RESPONSES", "type": {".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "RawRequestMessage": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawRequestMessage", "kind": "Gdef"}, "RawResponseMessage": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_parser.RawResponseMessage", "kind": "Gdef"}, "SERVER_SOFTWARE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "aiohttp.http.SERVER_SOFTWARE", "name": "SERVER_SOFTWARE", "type": "builtins.str"}}, "StreamWriter": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_writer.StreamWriter", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "WSCloseCode": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSCloseCode", "kind": "Gdef"}, "WSMessage": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMessage", "kind": "Gdef"}, "WSMsgType": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMsgType", "kind": "Gdef"}, "WS_CLOSED_MESSAGE": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WS_CLOSED_MESSAGE", "kind": "Gdef"}, "WS_CLOSING_MESSAGE": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WS_CLOSING_MESSAGE", "kind": "Gdef"}, "WS_KEY": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.WS_KEY", "kind": "Gdef"}, "WebSocketError": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WebSocketError", "kind": "Gdef"}, "WebSocketReader": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.reader.WebSocketReader", "kind": "Gdef"}, "WebSocketWriter": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.writer.WebSocketWriter", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.http.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "aiohttp.__version__", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "ws_ext_gen": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.ws_ext_gen", "kind": "Gdef"}, "ws_ext_parse": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.helpers.ws_ext_parse", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\http.py"}