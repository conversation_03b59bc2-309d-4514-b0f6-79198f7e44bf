{".class": "MypyFile", "_fullname": "azure.core.pipeline.policies._authentication", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccessToken": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessToken", "kind": "Gdef"}, "AccessTokenInfo": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AccessTokenInfo", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AzureKeyCredential": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AzureKeyCredential", "kind": "Gdef"}, "AzureKeyCredentialPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "name": "AzureKeyCredentialPolicy", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._authentication", "mro": ["azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "credential", "name", "prefix", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "credential", "name", "prefix", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy"}, "azure.core.credentials.AzureKeyCredential", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AzureKeyCredentialPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_credential": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy._credential", "name": "_credential", "type": "azure.core.credentials.AzureKeyCredential"}}, "_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy._name", "name": "_name", "type": "builtins.str"}}, "_prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy._prefix", "name": "_prefix", "type": "builtins.str"}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request of AzureKeyCredentialPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.AzureKeyCredentialPolicy"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "HTTPResponseType"], "typeddict_type": null}}, "AzureSasCredential": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.AzureSasCredential", "kind": "Gdef"}, "AzureSasCredentialPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "name": "AzureSasCredentialPolicy", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._authentication", "mro": ["azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "credential", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "credential", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy"}, "azure.core.credentials.AzureSasCredential", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AzureSasCredentialPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_credential": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy._credential", "name": "_credential", "type": "azure.core.credentials.AzureSasCredential"}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request of AzureSasCredentialPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.AzureSasCredentialPolicy"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "HTTPResponseType"], "typeddict_type": null}}, "BearerTokenCredentialPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.HTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "name": "BearerTokenCredentialPolicy", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.policies._authentication", "mro": ["azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase", "azure.core.pipeline.policies._base.HTTPPolicy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "authorize_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "request", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy.authorize_request", "name": "authorize_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "request", "scopes", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authorize_request of BearerTokenCredentialPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_challenge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy.on_challenge", "name": "on_challenge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_challenge of BearerTokenCredentialPolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy.on_exception", "name": "on_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_exception of BearerTokenCredentialPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy.on_request", "name": "on_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request of BearerTokenCredentialPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy.on_response", "name": "on_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "response"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_response of BearerTokenCredentialPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of BearerTokenCredentialPolicy", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._authentication.BearerTokenCredentialPolicy"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "HTTPResponseType"], "typeddict_type": null}}, "HTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.HTTPPolicy", "kind": "Gdef"}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}}, "HTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPResponseType", "name": "HTTPResponseType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}}, "HttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpRequest", "kind": "Gdef"}, "HttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpResponse", "kind": "Gdef"}, "LegacyHttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "LegacyHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpResponse", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PipelineRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineRequest", "kind": "Gdef"}, "PipelineResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineResponse", "kind": "Gdef"}, "SansIOHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "kind": "Gdef"}, "ServiceRequestError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ServiceRequestError", "kind": "Gdef"}, "SupportsTokenInfo": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.SupportsTokenInfo", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TokenCredential": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.TokenCredential", "kind": "Gdef"}, "TokenProvider": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.TokenProvider", "kind": "Gdef"}, "TokenRequestOptions": {".class": "SymbolTableNode", "cross_ref": "azure.core.credentials.TokenRequestOptions", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_BearerTokenCredentialPolicyBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase", "name": "_BearerTokenCredentialPolicyBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._authentication", "mro": ["azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "credential", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "credential", "scopes", "kwargs"], "arg_types": ["azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase", {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.TokenProvider"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _BearerTokenCredentialPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_credential": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._credential", "name": "_credential", "type": {".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.TokenProvider"}}}, "_enable_cae": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._enable_cae", "name": "_enable_cae", "type": "builtins.bool"}}, "_enforce_https": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._enforce_https", "name": "_enforce_https", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._enforce_https", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_enforce_https of _BearerTokenCredentialPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._enforce_https", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._enforce_https", "name": "_enforce_https", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._enforce_https", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_enforce_https of _BearerTokenCredentialPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._enforce_https", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}]}}}}, "_get_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._get_token", "name": "_get_token", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "arg_types": ["azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_token of _BearerTokenCredentialPolicyBase", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.AccessToken"}, "azure.core.credentials.AccessTokenInfo"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_need_new_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._need_new_token", "name": "_need_new_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_need_new_token of _BearerTokenCredentialPolicyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._need_new_token", "name": "_need_new_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_need_new_token of _BearerTokenCredentialPolicyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_request_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._request_token", "name": "_request_token", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "scopes", "kwargs"], "arg_types": ["azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_request_token of _BearerTokenCredentialPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_scopes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._scopes", "name": "_scopes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._token", "name": "_token", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.credentials.AccessToken"}, "azure.core.credentials.AccessTokenInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_update_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["headers", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._update_headers", "name": "_update_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["headers", "token"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_headers of _BearerTokenCredentialPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase._update_headers", "name": "_update_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["headers", "token"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_headers of _BearerTokenCredentialPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._authentication._BearerTokenCredentialPolicyBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._authentication.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._authentication.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._authentication.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._authentication.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._authentication.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._authentication.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "get_challenge_parameter": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._utils.get_challenge_parameter", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\policies\\_authentication.py"}