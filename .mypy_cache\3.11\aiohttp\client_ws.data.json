{".class": "MypyFile", "_fullname": "aiohttp.client_ws", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ClientError", "kind": "Gdef"}, "ClientResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.ClientResponse", "kind": "Gdef"}, "ClientWSTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.client_ws.ClientWSTimeout", "name": "ClientWSTimeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWSTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 39, "converter_init_type": null, "has_converter": false, "has_default": true, "init": true, "init_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "kw_only": false, "name": "ws_receive"}, {"alias": null, "context_column": 4, "context_line": 40, "converter_init_type": null, "has_converter": false, "has_default": true, "init": true, "init_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "kw_only": false, "name": "ws_close"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.client_ws", "mro": ["aiohttp.client_ws.ClientWSTimeout", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_client_ws_ClientWSTimeout_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.client_ws.ClientWSTimeout.__aiohttp_client_ws_ClientWSTimeout_AttrsAttributes__", "name": "__aiohttp_client_ws_ClientWSTimeout_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWSTimeout.__aiohttp_client_ws_ClientWSTimeout_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.client_ws", "mro": ["aiohttp.client_ws.ClientWSTimeout.__aiohttp_client_ws_ClientWSTimeout_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "ws_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ws_close", "name": "ws_close", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "ws_receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "ws_receive", "name": "ws_receive", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.client_ws.ClientWSTimeout.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.client_ws.ClientWSTimeout.__aiohttp_client_ws_ClientWSTimeout_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWSTimeout.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of ClientWSTimeout", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWSTimeout.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of ClientWSTimeout", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "ws_receive", "ws_close"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWSTimeout.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "ws_receive", "ws_close"], "arg_types": ["aiohttp.client_ws.ClientWSTimeout", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientWSTimeout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWSTimeout.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of ClientWSTimeout", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWSTimeout.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of ClientWSTimeout", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.client_ws.ClientWSTimeout.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.client_ws.ClientWSTimeout.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ws_receive"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ws_close"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.client_ws.ClientWSTimeout.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "ws_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.client_ws.ClientWSTimeout.ws_close", "name": "ws_close", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ws_receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.client_ws.ClientWSTimeout.ws_receive", "name": "ws_receive", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWSTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.client_ws.ClientWSTimeout", "values": [], "variance": 0}, "slots": ["ws_close", "ws_receive"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClientWebSocketResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.client_ws.ClientWebSocketResponse", "name": "ClientWebSocketResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.client_ws", "mro": ["aiohttp.client_ws.ClientWebSocketResponse", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "aiohttp.client_ws.ClientWebSocketResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aiter__ of ClientWebSocketResponse", "ret_type": "aiohttp.client_ws.ClientWebSocketResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__anext__ of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp._websocket.models.WSMessage"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "reader", "writer", "protocol", "response", "timeout", "autoclose", "autoping", "loop", "heartbeat", "compress", "client_notakeover"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "reader", "writer", "protocol", "response", "timeout", "autoclose", "autoping", "loop", "heartbeat", "compress", "client_notakeover"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", "aiohttp._websocket.reader_py.WebSocketDataQueue", "aiohttp._websocket.writer.WebSocketWriter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "aiohttp.client_reqrep.ClientResponse", "aiohttp.client_ws.ClientWSTimeout", "builtins.bool", "builtins.bool", "asyncio.events.AbstractEventLoop", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_autoclose": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._autoclose", "name": "_autoclose", "type": "builtins.bool"}}, "_autoping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._autoping", "name": "_autoping", "type": "builtins.bool"}}, "_cancel_heartbeat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._cancel_heartbeat", "name": "_cancel_heartbeat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cancel_heartbeat of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cancel_pong_response_cb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._cancel_pong_response_cb", "name": "_cancel_pong_response_cb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cancel_pong_response_cb of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client_notakeover": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._client_notakeover", "name": "_client_notakeover", "type": "builtins.bool"}}, "_close_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._close_code", "name": "_close_code", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_close_wait": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._close_wait", "name": "_close_wait", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_closed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._closed", "name": "_closed", "type": "builtins.bool"}}, "_closing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._closing", "name": "_closing", "type": "builtins.bool"}}, "_compress": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._compress", "name": "_compress", "type": "builtins.int"}}, "_conn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._conn", "name": "_conn", "type": {".class": "UnionType", "items": ["aiohttp.connector.Connection", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_exception": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._exception", "name": "_exception", "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_handle_ping_pong_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._handle_ping_pong_exception", "name": "_handle_ping_pong_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_ping_pong_exception of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_heartbeat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._heartbeat", "name": "_heartbeat", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_heartbeat_cb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._heartbeat_cb", "name": "_heartbeat_cb", "type": {".class": "UnionType", "items": ["asyncio.events.TimerHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_heartbeat_when": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._heartbeat_when", "name": "_heartbeat_when", "type": "builtins.float"}}, "_loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._loop", "name": "_loop", "type": "asyncio.events.AbstractEventLoop"}}, "_ping_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._ping_task", "name": "_ping_task", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_ping_task_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._ping_task_done", "name": "_ping_task_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "task"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ping_task_done of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pong_heartbeat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._pong_heartbeat", "name": "_pong_heartbeat", "type": "builtins.float"}}, "_pong_not_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._pong_not_received", "name": "_pong_not_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pong_not_received of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pong_response_cb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._pong_response_cb", "name": "_pong_response_cb", "type": {".class": "UnionType", "items": ["asyncio.events.TimerHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_protocol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._protocol", "name": "_protocol", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_reader": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._reader", "name": "_reader", "type": "aiohttp._websocket.reader_py.WebSocketDataQueue"}}, "_reset_heartbeat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._reset_heartbeat", "name": "_reset_heartbeat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reset_heartbeat of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._response", "name": "_response", "type": "aiohttp.client_reqrep.ClientResponse"}}, "_send_heartbeat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._send_heartbeat", "name": "_send_heartbeat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_send_heartbeat of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._set_closed", "name": "_set_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_closed of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_closing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._set_closing", "name": "_set_closing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_closing of ClientWebSocketResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._timeout", "name": "_timeout", "type": "aiohttp.client_ws.ClientWSTimeout"}}, "_waiting": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._waiting", "name": "_waiting", "type": "builtins.bool"}}, "_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse._writer", "name": "_writer", "type": "aiohttp._websocket.writer.WebSocketWriter"}}, "client_notakeover": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.client_notakeover", "name": "client_notakeover", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "client_notakeover of ClientWebSocketResponse", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.client_notakeover", "name": "client_notakeover", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "client_notakeover of ClientWebSocketResponse", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "code", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "code", "message"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", "builtins.int", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.close_code", "name": "close_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close_code of ClientWebSocketResponse", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.close_code", "name": "close_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close_code of ClientWebSocketResponse", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.closed", "name": "closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "closed of ClientWebSocketResponse", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.closed", "name": "closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "closed of ClientWebSocketResponse", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "compress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.compress", "name": "compress", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compress of ClientWebSocketResponse", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.compress", "name": "compress", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compress of ClientWebSocketResponse", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.exception", "name": "exception", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exception of ClientWebSocketResponse", "ret_type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_extra_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.get_extra_info", "name": "get_extra_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "default"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_extra_info of ClientWebSocketResponse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.ping", "name": "ping", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "message"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ping of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pong": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.pong", "name": "pong", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "message"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pong of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "protocol": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.protocol", "name": "protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "protocol of ClientWebSocketResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.protocol", "name": "protocol", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "protocol of ClientWebSocketResponse", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp._websocket.models.WSMessage"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "receive_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.receive_bytes", "name": "receive_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "timeout"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive_bytes of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "receive_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "loads", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.receive_json", "name": "receive_json", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "loads", "timeout"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.JSONDecoder"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive_json of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "receive_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.receive_str", "name": "receive_str", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "timeout"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive_str of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "compress"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.send_bytes", "name": "send_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "compress"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", "builtins.bytes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_bytes of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "message", "opcode", "compress"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.send_frame", "name": "send_frame", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "message", "opcode", "compress"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", "builtins.bytes", "aiohttp._websocket.models.WSMsgType", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_frame of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "data", "compress", "dumps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.send_json", "name": "send_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "data", "compress", "dumps"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.JSONEncoder"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_json of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "compress"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.client_ws.ClientWebSocketResponse.send_str", "name": "send_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "data", "compress"], "arg_types": ["aiohttp.client_ws.ClientWebSocketResponse", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_str of ClientWebSocketResponse", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.client_ws.ClientWebSocketResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.client_ws.ClientWebSocketResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DEFAULT_JSON_DECODER": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.DEFAULT_JSON_DECODER", "kind": "Gdef"}, "DEFAULT_JSON_ENCODER": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.DEFAULT_JSON_ENCODER", "kind": "Gdef"}, "DEFAULT_WS_CLIENT_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.client_ws.DEFAULT_WS_CLIENT_TIMEOUT", "name": "DEFAULT_WS_CLIENT_TIMEOUT", "type": "aiohttp.client_ws.ClientWSTimeout"}}, "EofStream": {".class": "SymbolTableNode", "cross_ref": "aiohttp.streams.EofStream", "kind": "Gdef"}, "JSONDecoder": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.JSONDecoder", "kind": "Gdef"}, "JSONEncoder": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.JSONEncoder", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ServerTimeoutError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.ServerTimeoutError", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "WSCloseCode": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSCloseCode", "kind": "Gdef"}, "WSMessage": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMessage", "kind": "Gdef"}, "WSMessageTypeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_exceptions.WSMessageTypeError", "kind": "Gdef"}, "WSMsgType": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMsgType", "kind": "Gdef"}, "WS_CLOSED_MESSAGE": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WS_CLOSED_MESSAGE", "kind": "Gdef"}, "WS_CLOSING_MESSAGE": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WS_CLOSING_MESSAGE", "kind": "Gdef"}, "WebSocketDataQueue": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.reader.WebSocketDataQueue", "kind": "Gdef"}, "WebSocketError": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WebSocketError", "kind": "Gdef"}, "WebSocketWriter": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.writer.WebSocketWriter", "kind": "Gdef"}, "_INTERNAL_RECEIVE_TYPES": {".class": "SymbolTableNode", "cross_ref": "aiohttp.http_websocket._INTERNAL_RECEIVE_TYPES", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client_ws.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client_ws.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client_ws.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client_ws.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client_ws.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.client_ws.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "async_timeout": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "attr": {".class": "SymbolTableNode", "cross_ref": "attr", "kind": "Gdef"}, "calculate_timeout_when": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.calculate_timeout_when", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "set_result": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.set_result", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\client_ws.py"}