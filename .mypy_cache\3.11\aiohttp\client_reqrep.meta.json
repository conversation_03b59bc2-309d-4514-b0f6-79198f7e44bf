{"data_mtime": 1753366020, "dep_lines": [11, 32, 32, 32, 32, 32, 33, 34, 42, 43, 64, 65, 66, 91, 92, 93, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 28, 29, 30, 32, 76, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 25, 25, 25, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["http.cookies", "aiohttp.hdrs", "aiohttp.helpers", "aiohttp.http", "aiohttp.multipart", "aiohttp.payload", "aiohttp.abc", "aiohttp.client_exceptions", "aiohttp.compression_utils", "aiohttp.formdata", "aiohttp.log", "aiohttp.streams", "aiohttp.typedefs", "aiohttp.client", "aiohttp.connector", "aiohttp.tracing", "asyncio", "codecs", "contextlib", "functools", "io", "re", "sys", "traceback", "warnings", "<PERSON><PERSON><PERSON>", "types", "typing", "attr", "multidict", "yarl", "aiohttp", "ssl", "builtins", "_asyncio", "_collections_abc", "_contextvars", "_frozen_importlib", "_hashlib", "_ssl", "_typeshed", "abc", "aiohttp.base_protocol", "aiohttp.client_proto", "aiohttp.http_exceptions", "aiohttp.http_parser", "aiohttp.http_writer", "asyncio.events", "asyncio.exceptions", "asyncio.protocols", "asyncio.transports", "attr.setters", "enum", "http", "itertools", "json", "json.decoder", "logging", "propcache", "propcache._helpers", "propcache._helpers_py", "typing_extensions", "urllib", "urllib.parse", "yarl._url"], "hash": "084aac25adf27fb8b85422bf8d12a69f1cfb337d", "id": "aiohttp.client_reqrep", "ignore_all": true, "interface_hash": "083561425f750d2a9d57d4d462f99e76d0d0e4fa", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\client_reqrep.py", "plugin_data": null, "size": 45240, "suppressed": [], "version_id": "1.15.0"}