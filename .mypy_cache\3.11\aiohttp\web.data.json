{".class": "MypyFile", "_fullname": "aiohttp.web", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractAccessLogger": {".class": "SymbolTableNode", "cross_ref": "aiohttp.abc.AbstractAccessLogger", "kind": "Gdef", "module_public": false}, "AbstractResource": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.AbstractResource", "kind": "Gdef"}, "AbstractRoute": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.AbstractRoute", "kind": "Gdef"}, "AbstractRouteDef": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.AbstractRouteDef", "kind": "Gdef"}, "AccessLogger": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_log.AccessLogger", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AppKey": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.AppKey", "kind": "Gdef"}, "AppRunner": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_runner.AppRunner", "kind": "Gdef"}, "Application": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_app.Application", "kind": "Gdef"}, "ArgumentParser": {".class": "SymbolTableNode", "cross_ref": "argparse.ArgumentParser", "kind": "Gdef", "module_public": false}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_public": false}, "BaseRequest": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_request.BaseRequest", "kind": "Gdef"}, "BaseRunner": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_runner.BaseRunner", "kind": "Gdef"}, "BaseSite": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_runner.BaseSite", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CleanupError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_app.CleanupError", "kind": "Gdef"}, "ContentCoding": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_response.ContentCoding", "kind": "Gdef"}, "DynamicResource": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.DynamicResource", "kind": "Gdef"}, "FileField": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_request.FileField", "kind": "Gdef"}, "FileResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_fileresponse.FileResponse", "kind": "Gdef"}, "GracefulExit": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_runner.GracefulExit", "kind": "Gdef"}, "HTTPAccepted": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPAccepted", "kind": "Gdef"}, "HTTPBadGateway": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPBadGateway", "kind": "Gdef"}, "HTTPBadRequest": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPBadRequest", "kind": "Gdef"}, "HTTPClientError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPClientError", "kind": "Gdef"}, "HTTPConflict": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPConflict", "kind": "Gdef"}, "HTTPCreated": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPCreated", "kind": "Gdef"}, "HTTPError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPError", "kind": "Gdef"}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPException", "kind": "Gdef"}, "HTTPExpectationFailed": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPExpectationFailed", "kind": "Gdef"}, "HTTPFailedDependency": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPFailedDependency", "kind": "Gdef"}, "HTTPForbidden": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPForbidden", "kind": "Gdef"}, "HTTPFound": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPFound", "kind": "Gdef"}, "HTTPGatewayTimeout": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPGatewayTimeout", "kind": "Gdef"}, "HTTPGone": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPGone", "kind": "Gdef"}, "HTTPInsufficientStorage": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPInsufficientStorage", "kind": "Gdef"}, "HTTPInternalServerError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPInternalServerError", "kind": "Gdef"}, "HTTPLengthRequired": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPLengthRequired", "kind": "Gdef"}, "HTTPMethodNotAllowed": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPMethodNotAllowed", "kind": "Gdef"}, "HTTPMisdirectedRequest": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPMisdirectedRequest", "kind": "Gdef"}, "HTTPMove": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPMove", "kind": "Gdef"}, "HTTPMovedPermanently": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPMovedPermanently", "kind": "Gdef"}, "HTTPMultipleChoices": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPMultipleChoices", "kind": "Gdef"}, "HTTPNetworkAuthenticationRequired": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPNetworkAuthenticationRequired", "kind": "Gdef"}, "HTTPNoContent": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPNoContent", "kind": "Gdef"}, "HTTPNonAuthoritativeInformation": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPNonAuthoritativeInformation", "kind": "Gdef"}, "HTTPNotAcceptable": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPNotAcceptable", "kind": "Gdef"}, "HTTPNotExtended": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPNotExtended", "kind": "Gdef"}, "HTTPNotFound": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPNotFound", "kind": "Gdef"}, "HTTPNotImplemented": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPNotImplemented", "kind": "Gdef"}, "HTTPNotModified": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPNotModified", "kind": "Gdef"}, "HTTPOk": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPOk", "kind": "Gdef"}, "HTTPPartialContent": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPPartialContent", "kind": "Gdef"}, "HTTPPaymentRequired": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPPaymentRequired", "kind": "Gdef"}, "HTTPPermanentRedirect": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPPermanentRedirect", "kind": "Gdef"}, "HTTPPreconditionFailed": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPPreconditionFailed", "kind": "Gdef"}, "HTTPPreconditionRequired": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPPreconditionRequired", "kind": "Gdef"}, "HTTPProxyAuthenticationRequired": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPProxyAuthenticationRequired", "kind": "Gdef"}, "HTTPRedirection": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPRedirection", "kind": "Gdef"}, "HTTPRequestEntityTooLarge": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPRequestEntityTooLarge", "kind": "Gdef"}, "HTTPRequestHeaderFieldsTooLarge": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPRequestHeaderFieldsTooLarge", "kind": "Gdef"}, "HTTPRequestRangeNotSatisfiable": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPRequestRangeNotSatisfiable", "kind": "Gdef"}, "HTTPRequestTimeout": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPRequestTimeout", "kind": "Gdef"}, "HTTPRequestURITooLong": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPRequestURITooLong", "kind": "Gdef"}, "HTTPResetContent": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPResetContent", "kind": "Gdef"}, "HTTPSeeOther": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPSeeOther", "kind": "Gdef"}, "HTTPServerError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPServerError", "kind": "Gdef"}, "HTTPServiceUnavailable": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPServiceUnavailable", "kind": "Gdef"}, "HTTPSuccessful": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPSuccessful", "kind": "Gdef"}, "HTTPTemporaryRedirect": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPTemporaryRedirect", "kind": "Gdef"}, "HTTPTooManyRequests": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPTooManyRequests", "kind": "Gdef"}, "HTTPUnauthorized": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPUnauthorized", "kind": "Gdef"}, "HTTPUnavailableForLegalReasons": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPUnavailableForLegalReasons", "kind": "Gdef"}, "HTTPUnprocessableEntity": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPUnprocessableEntity", "kind": "Gdef"}, "HTTPUnsupportedMediaType": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPUnsupportedMediaType", "kind": "Gdef"}, "HTTPUpgradeRequired": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPUpgradeRequired", "kind": "Gdef"}, "HTTPUseProxy": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPUseProxy", "kind": "Gdef"}, "HTTPVariantAlsoNegotiates": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPVariantAlsoNegotiates", "kind": "Gdef"}, "HTTPVersionNotSupported": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.HTTPVersionNotSupported", "kind": "Gdef"}, "HostSequence": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "aiohttp.web.HostSequence", "line": 302, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "NamedPipeSite": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_runner.NamedPipeSite", "kind": "Gdef"}, "NotAppKeyWarning": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_exceptions.NotAppKeyWarning", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs.PathLike", "kind": "Gdef", "module_public": false}, "PayloadAccessError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_protocol.PayloadAccessError", "kind": "Gdef"}, "PlainResource": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.PlainResource", "kind": "Gdef"}, "PrefixedSubAppResource": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.PrefixedSubAppResource", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_request.Request", "kind": "Gdef"}, "RequestHandler": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_protocol.RequestHandler", "kind": "Gdef"}, "RequestPayloadError": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_protocol.RequestPayloadError", "kind": "Gdef"}, "Resource": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.Resource", "kind": "Gdef"}, "ResourceRoute": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.ResourceRoute", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_response.Response", "kind": "Gdef"}, "RouteDef": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.RouteDef", "kind": "Gdef"}, "RouteTableDef": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.RouteTableDef", "kind": "Gdef"}, "SSLContext": {".class": "SymbolTableNode", "cross_ref": "ssl.SSLContext", "kind": "Gdef", "module_public": false}, "Server": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_server.Server", "kind": "Gdef"}, "ServerRunner": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_runner.ServerRunner", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "SockSite": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_runner.SockSite", "kind": "Gdef"}, "StaticDef": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.StaticDef", "kind": "Gdef"}, "StaticResource": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.StaticResource", "kind": "Gdef"}, "StreamResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_response.StreamResponse", "kind": "Gdef"}, "TCPSite": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_runner.TCPSite", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypingIterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnixSite": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_runner.UnixSite", "kind": "Gdef"}, "UrlDispatcher": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.UrlDispatcher", "kind": "Gdef"}, "UrlMappingMatchInfo": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.UrlMappingMatchInfo", "kind": "Gdef"}, "View": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_urldispatcher.View", "kind": "Gdef"}, "WSMsgType": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSMsgType", "kind": "Gdef"}, "WebSocketReady": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_ws.WebSocketReady", "kind": "Gdef"}, "WebSocketResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_ws.WebSocketResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.web.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.web.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_cancel_tasks": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["to_cancel", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web._cancel_tasks", "name": "_cancel_tasks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["to_cancel", "loop"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}, "asyncio.events.AbstractEventLoop"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cancel_tasks", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_run_app": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["app", "host", "port", "path", "sock", "shutdown_timeout", "keepalive_timeout", "ssl_context", "print", "backlog", "access_log_class", "access_log_format", "access_log", "handle_signals", "reuse_address", "reuse_port", "handler_cancellation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.web._run_app", "name": "_run_app", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["app", "host", "port", "path", "sock", "shutdown_timeout", "keepalive_timeout", "ssl_context", "print", "backlog", "access_log_class", "access_log_format", "access_log", "handle_signals", "reuse_address", "reuse_port", "handler_cancellation"], "arg_types": [{".class": "UnionType", "items": ["aiohttp.web_app.Application", {".class": "Instance", "args": ["aiohttp.web_app.Application"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web.HostSequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["socket.socket", {".class": "Instance", "args": ["socket.socket"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "TypeType", "item": "aiohttp.abc.AbstractAccessLogger"}, "builtins.str", {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_app", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "access_logger": {".class": "SymbolTableNode", "cross_ref": "aiohttp.log.access_logger", "kind": "Gdef", "module_public": false}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "delete": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.delete", "kind": "Gdef"}, "get": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.get", "kind": "Gdef"}, "head": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.head", "kind": "Gdef"}, "import_module": {".class": "SymbolTableNode", "cross_ref": "importlib.import_module", "kind": "Gdef", "module_public": false}, "json_response": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_response.json_response", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["argv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web.main", "name": "main", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["argv"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "main", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "middleware": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_middlewares.middleware", "kind": "Gdef"}, "normalize_path_middleware": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_middlewares.normalize_path_middleware", "kind": "Gdef"}, "options": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.options", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "patch": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.patch", "kind": "Gdef"}, "post": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.post", "kind": "Gdef"}, "put": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.put", "kind": "Gdef"}, "route": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.route", "kind": "Gdef"}, "run_app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["app", "host", "port", "path", "sock", "shutdown_timeout", "keepalive_timeout", "ssl_context", "print", "backlog", "access_log_class", "access_log_format", "access_log", "handle_signals", "reuse_address", "reuse_port", "handler_cancellation", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.web.run_app", "name": "run_app", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["app", "host", "port", "path", "sock", "shutdown_timeout", "keepalive_timeout", "ssl_context", "print", "backlog", "access_log_class", "access_log_format", "access_log", "handle_signals", "reuse_address", "reuse_port", "handler_cancellation", "loop"], "arg_types": [{".class": "UnionType", "items": ["aiohttp.web_app.Application", {".class": "Instance", "args": ["aiohttp.web_app.Application"], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.web.HostSequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs.PathLike"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["socket.socket", {".class": "Instance", "args": ["socket.socket"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "TypeType", "item": "aiohttp.abc.AbstractAccessLogger"}, "builtins.str", {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_app", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef", "module_public": false}, "static": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.static", "kind": "Gdef"}, "suppress": {".class": "SymbolTableNode", "cross_ref": "contextlib.suppress", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "view": {".class": "SymbolTableNode", "cross_ref": "aiohttp.web_routedef.view", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web.py"}