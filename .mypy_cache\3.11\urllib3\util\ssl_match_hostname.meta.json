{"data_mtime": 1753366016, "dep_lines": [15, 7, 9, 10, 11, 1, 1, 1], "dep_prios": [25, 5, 5, 10, 10, 5, 30, 30], "dependencies": ["urllib3.util.ssl_", "__future__", "ipaddress", "re", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "8800fc6261b5ab0389b6b4ff252c31ec33ca015e", "id": "urllib3.util.ssl_match_hostname", "ignore_all": true, "interface_hash": "cc32ab893b62c40e6b0d9090c19c98c3d9173fbe", "mtime": 1750470649, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\urllib3\\util\\ssl_match_hostname.py", "plugin_data": null, "size": 5845, "suppressed": [], "version_id": "1.15.0"}