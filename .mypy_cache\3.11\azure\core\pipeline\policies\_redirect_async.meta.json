{"data_mtime": 1753366020, "dep_lines": [35, 36, 29, 34, 27, 28, 33, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies._redirect", "azure.core.pipeline.policies._utils", "azure.core.pipeline.transport", "azure.core.pipeline.policies", "azure.core.exceptions", "azure.core.pipeline", "azure.core.rest", "typing", "builtins", "_frozen_importlib", "abc", "azure.core.pipeline.policies._base", "azure.core.pipeline.policies._base_async", "azure.core.pipeline.transport._base", "azure.core.pipeline.transport._base_async", "azure.core.rest._helpers", "azure.core.rest._rest_py3", "contextlib"], "hash": "63ac3c984f412abe4319f19363da35ee2613644e", "id": "azure.core.pipeline.policies._redirect_async", "ignore_all": true, "interface_hash": "4b9936d4b116a4812f07b844afa3ef331ce907ea", "mtime": 1750470991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\policies\\_redirect_async.py", "plugin_data": null, "size": 4476, "suppressed": [], "version_id": "1.15.0"}