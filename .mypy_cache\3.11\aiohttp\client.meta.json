{"data_mtime": 1753366020, "dep_lines": [40, 39, 39, 39, 41, 42, 73, 80, 85, 92, 93, 104, 105, 106, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 35, 36, 37, 39, 160, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["aiohttp._websocket.reader", "aiohttp.hdrs", "aiohttp.http", "aiohttp.payload", "aiohttp.abc", "aiohttp.client_exceptions", "aiohttp.client_reqrep", "aiohttp.client_ws", "aiohttp.connector", "aiohttp.cookiejar", "aiohttp.helpers", "aiohttp.http_websocket", "aiohttp.tracing", "aiohttp.typedefs", "asyncio", "base64", "<PERSON><PERSON><PERSON>", "json", "os", "sys", "traceback", "warnings", "contextlib", "types", "typing", "attr", "multidict", "yarl", "aiohttp", "ssl", "builtins", "_asyncio", "_collections_abc", "_frozen_importlib", "_hashlib", "_random", "_ssl", "_typeshed", "abc", "aiohttp._websocket", "aiohttp._websocket.helpers", "aiohttp._websocket.models", "aiohttp._websocket.reader_py", "aiohttp._websocket.writer", "aiohttp.base_protocol", "aiohttp.client_proto", "aiohttp.http_parser", "aiohttp.http_writer", "aiohttp.streams", "asyncio.events", "asyncio.protocols", "asyncio.transports", "attr.setters", "enum", "http", "http.cookies", "json.encoder", "random", "socket", "typing_extensions", "urllib", "urllib.parse", "yarl._url"], "hash": "f17cf5bc1bd5ff3b1db652e5c8c670fc215990da", "id": "aiohttp.client", "ignore_all": true, "interface_hash": "87a51f8a2938a00d86c7535cd150fa8fcba2b213", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\client.py", "plugin_data": null, "size": 56582, "suppressed": [], "version_id": "1.15.0"}