{"data_mtime": 1753366021, "dep_lines": [34, 35, 42, 44, 33, 39, 40, 41, 43, 29, 47, 27, 28, 29, 30, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 25, 10, 10, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies", "azure.core.pipeline.transport", "azure.core.tracing.common", "azure.core.tracing._models", "azure.core.pipeline", "azure.core.rest", "azure.core.settings", "azure.core.tracing", "azure.core.instrumentation", "urllib.parse", "opentelemetry.trace", "logging", "sys", "urllib", "typing", "types", "builtins", "_frozen_importlib", "abc", "azure.core.pipeline.policies._base", "azure.core.pipeline.transport._base", "azure.core.rest._helpers", "azure.core.rest._rest_py3", "opentelemetry", "opentelemetry.trace.span"], "hash": "da9b6b26f9fc3f8dc4f6952506f0c6e40c12eb20", "id": "azure.core.pipeline.policies._distributed_tracing", "ignore_all": true, "interface_hash": "9c43bf271f821501de10bc730c62272b34db36a7", "mtime": 1750470991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\policies\\_distributed_tracing.py", "plugin_data": null, "size": 12102, "suppressed": [], "version_id": "1.15.0"}