{".class": "MypyFile", "_fullname": "calendar", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Calendar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "calendar.Calendar", "name": "Calendar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "calendar.Calendar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "calendar", "mro": ["calendar.Calendar", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "firstweekday"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "firstweekday"], "arg_types": ["calendar.Calendar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Calendar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "firstweekday": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "calendar.Calendar.firstweekday", "name": "firstweekday", "type": "builtins.int"}}, "getfirstweekday": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.getfirstweekday", "name": "getfirstweekday", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["calendar.Calendar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfirstweekday of Calendar", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itermonthdates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.itermonthdates", "name": "iter<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "itermonthdates of Calendar", "ret_type": {".class": "Instance", "args": ["datetime.date"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itermonthdays": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.itermonthdays", "name": "itermonthdays", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "itermonthdays of Calendar", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itermonthdays2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.itermonthdays2", "name": "itermonthdays2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "itermonthdays2 of Calendar", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itermonthdays3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.itermonthdays3", "name": "itermonthdays3", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "itermonthdays3 of Calendar", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itermonthdays4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.itermonthdays4", "name": "itermonthdays4", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "itermonthdays4 of Calendar", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iterweekdays": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.iterweekdays", "name": "iterweekdays", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["calendar.Calendar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iterweekdays of Calendar", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "monthdatescalendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.monthdatescalendar", "name": "monthdatescalendar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "monthdatescalendar of Calendar", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["datetime.date"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "monthdays2calendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.monthdays2calendar", "name": "monthdays2calendar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "monthdays2calendar of Calendar", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "monthdayscalendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.monthdayscalendar", "name": "monthdayscalendar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "year", "month"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "monthdayscalendar of Calendar", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setfirstweekday": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "firstweekday"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.setfirstweekday", "name": "setfirstweekday", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "firstweekday"], "arg_types": ["calendar.Calendar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setfirstweekday of Calendar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "yeardatescalendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "year", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.yeardatescalendar", "name": "yeardatescalendar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "year", "width"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "yeardatescalendar of Calendar", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["datetime.date"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "yeardays2calendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "year", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.yeardays2calendar", "name": "yeardays2calendar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "year", "width"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "yeardays2calendar of Calendar", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "yeardayscalendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "year", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.Calendar.yeardayscalendar", "name": "yeardayscalendar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "year", "width"], "arg_types": ["calendar.Calendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "yeardayscalendar of Calendar", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "calendar.Calendar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "calendar.Calendar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EPOCH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "final_value": 1970, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "calendar.EPOCH", "name": "EPOCH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1970}, "type_ref": "builtins.int"}}}, "FRIDAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "calendar.FRIDAY", "name": "FRIDAY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HTMLCalendar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["calendar.Calendar"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "calendar.HTMLCalendar", "name": "HTMLCalendar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "calendar.HTMLCalendar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "calendar", "mro": ["calendar.HTMLCalendar", "calendar.Calendar", "builtins.object"], "names": {".class": "SymbolTable", "cssclass_month": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "calendar.HTMLCalendar.cssclass_month", "name": "cssclass_month", "type": "builtins.str"}}, "cssclass_month_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "calendar.HTMLCalendar.cssclass_month_head", "name": "cssclass_month_head", "type": "builtins.str"}}, "cssclass_noday": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "calendar.HTMLCalendar.cssclass_noday", "name": "cssclass_noday", "type": "builtins.str"}}, "cssclass_year": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "calendar.HTMLCalendar.cssclass_year", "name": "cssclass_year", "type": "builtins.str"}}, "cssclass_year_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "calendar.HTMLCalendar.cssclass_year_head", "name": "cssclass_year_head", "type": "builtins.str"}}, "cssclasses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "calendar.HTMLCalendar.cssclasses", "name": "cssclasses", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "cssclasses_weekday_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "calendar.HTMLCalendar.cssclasses_weekday_head", "name": "cssclasses_weekday_head", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "formatday": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "day", "weekday"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.HTMLCalendar.formatday", "name": "formatday", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "day", "weekday"], "arg_types": ["calendar.HTMLCalendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatday of HTMLCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatmonth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "theyear", "themonth", "withyear"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.HTMLCalendar.formatmonth", "name": "formatmonth", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "theyear", "themonth", "withyear"], "arg_types": ["calendar.HTMLCalendar", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatmonth of HTMLCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatmonthname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "theyear", "themonth", "withyear"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.HTMLCalendar.formatmonthname", "name": "formatmonthname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "theyear", "themonth", "withyear"], "arg_types": ["calendar.HTMLCalendar", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatmonthname of HTMLCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatweek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "theweek"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.HTMLCalendar.formatweek", "name": "formatweek", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "theweek"], "arg_types": ["calendar.HTMLCalendar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatweek of HTMLCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatweekday": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "day"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.HTMLCalendar.formatweekday", "name": "formatweekday", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "day"], "arg_types": ["calendar.HTMLCalendar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatweekday of HTMLCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatweekheader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.HTMLCalendar.formatweekheader", "name": "formatweekheader", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["calendar.HTMLCalendar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatweekheader of HTMLCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatyear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "theyear", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.HTMLCalendar.formatyear", "name": "formatyear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "theyear", "width"], "arg_types": ["calendar.HTMLCalendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatyear of HTMLCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatyearpage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "theyear", "width", "css", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.HTMLCalendar.formatyearpage", "name": "formatyearpage", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "theyear", "width", "css", "encoding"], "arg_types": ["calendar.HTMLCalendar", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatyearpage of HTMLCalendar", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "calendar.HTMLCalendar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "calendar.HTMLCalendar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IllegalMonthError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "calendar.IllegalMonthError", "name": "IllegalMonthError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "calendar.IllegalMonthError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "calendar", "mro": ["calendar.IllegalMonthError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.IllegalMonthError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "month"], "arg_types": ["calendar.IllegalMonthError", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IllegalMonthError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "calendar.IllegalMonthError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "calendar.IllegalMonthError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IllegalWeekdayError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "calendar.IllegalWeekdayError", "name": "IllegalWeekdayError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "calendar.IllegalWeekdayError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "calendar", "mro": ["calendar.IllegalWeekdayError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "weekday"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.IllegalWeekdayError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "weekday"], "arg_types": ["calendar.IllegalWeekdayError", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IllegalWeekdayError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "calendar.IllegalWeekdayError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "calendar.IllegalWeekdayError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LocaleHTMLCalendar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["calendar.HTMLCalendar"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "calendar.LocaleHTMLCalendar", "name": "LocaleHTMLCalendar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "calendar.LocaleHTMLCalendar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "calendar", "mro": ["calendar.LocaleHTMLCalendar", "calendar.HTMLCalendar", "calendar.Calendar", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "firstweekday", "locale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.LocaleHTMLCalendar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "firstweekday", "locale"], "arg_types": ["calendar.LocaleHTMLCalendar", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "calendar._LocaleType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LocaleHTMLCalendar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatmonthname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "theyear", "themonth", "withyear"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.LocaleHTMLCalendar.formatmonthname", "name": "formatmonthname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "theyear", "themonth", "withyear"], "arg_types": ["calendar.LocaleHTMLCalendar", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatmonthname of LocaleHTMLCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatweekday": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "day"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.LocaleHTMLCalendar.formatweekday", "name": "formatweekday", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "day"], "arg_types": ["calendar.LocaleHTMLCalendar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatweekday of LocaleHTMLCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "calendar.LocaleHTMLCalendar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "calendar.LocaleHTMLCalendar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LocaleTextCalendar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["calendar.TextCalendar"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "calendar.LocaleTextCalendar", "name": "LocaleTextCalendar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "calendar.LocaleTextCalendar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "calendar", "mro": ["calendar.LocaleTextCalendar", "calendar.TextCalendar", "calendar.Calendar", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "firstweekday", "locale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.LocaleTextCalendar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "firstweekday", "locale"], "arg_types": ["calendar.LocaleTextCalendar", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "calendar._LocaleType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LocaleTextCalendar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "calendar.LocaleTextCalendar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "calendar.LocaleTextCalendar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MONDAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "calendar.MONDAY", "name": "MONDAY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "SATURDAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 5, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "calendar.SATURDAY", "name": "SATURDAY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "SUNDAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 6, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "calendar.SUNDAY", "name": "SUNDAY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "THURSDAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 3, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "calendar.THURSDAY", "name": "THURSDAY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "TUESDAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "calendar.TUESDAY", "name": "TUESDAY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "TextCalendar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["calendar.Calendar"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "calendar.TextCalendar", "name": "TextCalendar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "calendar", "mro": ["calendar.TextCalendar", "calendar.Calendar", "builtins.object"], "names": {".class": "SymbolTable", "formatday": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "day", "weekday", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.formatday", "name": "formatday", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "day", "weekday", "width"], "arg_types": ["calendar.TextCalendar", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatday of TextCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatmonth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "theyear", "themonth", "w", "l"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.formatmonth", "name": "formatmonth", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "theyear", "themonth", "w", "l"], "arg_types": ["calendar.TextCalendar", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatmonth of TextCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatmonthname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "theyear", "themonth", "width", "withyear"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.formatmonthname", "name": "formatmonthname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "theyear", "themonth", "width", "withyear"], "arg_types": ["calendar.TextCalendar", "builtins.int", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatmonthname of TextCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatweek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "theweek", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.formatweek", "name": "formatweek", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "theweek", "width"], "arg_types": ["calendar.TextCalendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatweek of TextCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatweekday": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "day", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.formatweekday", "name": "formatweekday", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "day", "width"], "arg_types": ["calendar.TextCalendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatweekday of TextCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatweekheader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.formatweekheader", "name": "formatweekheader", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["calendar.TextCalendar", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatweekheader of TextCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatyear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "theyear", "w", "l", "c", "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.formatyear", "name": "formatyear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "theyear", "w", "l", "c", "m"], "arg_types": ["calendar.TextCalendar", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatyear of TextCalendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prmonth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "theyear", "themonth", "w", "l"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.prmonth", "name": "prmonth", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "theyear", "themonth", "w", "l"], "arg_types": ["calendar.TextCalendar", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prmonth of TextCalendar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prweek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "theweek", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.prweek", "name": "prweek", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "theweek", "width"], "arg_types": ["calendar.TextCalendar", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prweek of TextCalendar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pryear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "theyear", "w", "l", "c", "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.TextCalendar.pryear", "name": "pryear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "theyear", "w", "l", "c", "m"], "arg_types": ["calendar.TextCalendar", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pryear of TextCalendar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "calendar.TextCalendar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "calendar.TextCalendar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WEDNESDAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "calendar.WEDNESDAY", "name": "WEDNESDAY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "_LocaleType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "calendar._LocaleType", "line": 57, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "calendar.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "c": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.c", "name": "c", "type": "calendar.TextCalendar"}}, "calendar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["theyear", "w", "l", "c", "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.calendar", "name": "calendar", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["theyear", "w", "l", "c", "m"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calendar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "day_abbr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.day_abbr", "name": "day_abbr", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "day_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.day_name", "name": "day_name", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "different_locale": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "calendar.different_locale", "name": "different_locale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "calendar.different_locale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "calendar", "mro": ["calendar.different_locale", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.different_locale.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["calendar.different_locale"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of different_locale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.different_locale.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["calendar.different_locale", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of different_locale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "locale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.different_locale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "locale"], "arg_types": ["calendar.different_locale", {".class": "TypeAliasType", "args": [], "type_ref": "calendar._LocaleType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of different_locale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "calendar.different_locale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "calendar.different_locale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_hidden": true, "module_public": false}, "firstweekday": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.firstweekday", "name": "firstweekday", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "firstweekday", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cols", "colwidth", "spacing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cols", "colwidth", "spacing"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatstring": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cols", "colwidth", "spacing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.formatstring", "name": "formatstring", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cols", "colwidth", "spacing"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "formatstring", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isleap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["year"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.isleap", "name": "isleap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["year"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isleap", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leapdays": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["y1", "y2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.leapdays", "name": "leapdays", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["y1", "y2"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "leapdays", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "month": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["theyear", "themonth", "w", "l"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.month", "name": "month", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["theyear", "themonth", "w", "l"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "month", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "month_abbr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.month_abbr", "name": "month_abbr", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "month_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "calendar.month_name", "name": "month_name", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "monthcalendar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.monthcalendar", "name": "monthcalendar", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["year", "month"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "monthcalendar", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "monthrange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["year", "month"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.monthrange", "name": "monthrange", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["year", "month"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "monthrange", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prcal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["theyear", "w", "l", "c", "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.prcal", "name": "prcal", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["theyear", "w", "l", "c", "m"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prcal", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prmonth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["theyear", "themonth", "w", "l"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.prmonth", "name": "prmonth", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["theyear", "themonth", "w", "l"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prmonth", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prweek": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["theweek", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.prweek", "name": "prweek", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["theweek", "width"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prweek", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setfirstweekday": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["firstweekday"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.setfirstweekday", "name": "setfirstweekday", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["firstweekday"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setfirstweekday", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "struct_time": {".class": "SymbolTableNode", "cross_ref": "time.struct_time", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "timegm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tuple"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.timegm", "name": "timegm", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tuple"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "TypeAliasType", "args": [], "type_ref": "time.struct_time"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timegm", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "week": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["theweek", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.week", "name": "week", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["theweek", "width"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "week", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "weekday": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["year", "month", "day"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.weekday", "name": "weekday", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["year", "month", "day"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "weekday", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "weekheader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "calendar.weekheader", "name": "weekheader", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["width"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "weekheader", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\calendar.pyi"}