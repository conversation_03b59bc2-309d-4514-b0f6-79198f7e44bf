{".class": "MypyFile", "_fullname": "opentelemetry.propagators.composite", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CompositeHTTPPropagator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.propagators.composite.CompositePropagator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.propagators.composite.CompositeHTTPPropagator", "name": "CompositeHTTPPropagator", "type_vars": []}, "deletable_attributes": [], "deprecated": "class opentelemetry.propagators.composite.CompositeHTTPPropagator is deprecated: You should use CompositePropagator. Deprecated since version 1.2.0.", "flags": [], "fullname": "opentelemetry.propagators.composite.CompositeHTTPPropagator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.propagators.composite", "mro": ["opentelemetry.propagators.composite.CompositeHTTPPropagator", "opentelemetry.propagators.composite.CompositePropagator", "opentelemetry.propagators.textmap.TextMapPropagator", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.composite.CompositeHTTPPropagator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.propagators.composite.CompositeHTTPPropagator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompositePropagator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.propagators.textmap.TextMapPropagator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.propagators.composite.CompositePropagator", "name": "CompositePropagator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.propagators.composite.CompositePropagator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.propagators.composite", "mro": ["opentelemetry.propagators.composite.CompositePropagator", "opentelemetry.propagators.textmap.TextMapPropagator", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "propagators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagators.composite.CompositePropagator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "propagators"], "arg_types": ["opentelemetry.propagators.composite.CompositePropagator", {".class": "Instance", "args": ["opentelemetry.propagators.textmap.TextMapPropagator"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CompositePropagator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_propagators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.propagators.composite.CompositePropagator._propagators", "name": "_propagators", "type": {".class": "Instance", "args": ["opentelemetry.propagators.textmap.TextMapPropagator"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "extract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "getter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagators.composite.CompositePropagator.extract", "name": "extract", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "getter"], "arg_types": ["opentelemetry.propagators.composite.CompositePropagator", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagators.composite.CompositePropagator.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagators.composite.CompositePropagator.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Getter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract of CompositePropagator", "ret_type": "opentelemetry.context.context.Context", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagators.composite.CompositePropagator.extract", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.propagators.composite.CompositePropagator.fields", "name": "fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.propagators.composite.CompositePropagator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fields of CompositePropagator", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.propagators.composite.CompositePropagator.fields", "name": "fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.propagators.composite.CompositePropagator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fields of CompositePropagator", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "inject": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "setter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.propagators.composite.CompositePropagator.inject", "name": "inject", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "carrier", "context", "setter"], "arg_types": ["opentelemetry.propagators.composite.CompositePropagator", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagators.composite.CompositePropagator.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagators.composite.CompositePropagator.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.propagators.textmap.Setter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject of CompositePropagator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.textmap.CarrierT", "id": -1, "name": "textmap.CarrierT", "namespace": "opentelemetry.propagators.composite.CompositePropagator.inject", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.propagators.composite.CompositePropagator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.propagators.composite.CompositePropagator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.composite.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.composite.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.composite.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.composite.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.composite.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.propagators.composite.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.propagators.composite.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "textmap": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.propagators.textmap", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\propagators\\composite.py"}