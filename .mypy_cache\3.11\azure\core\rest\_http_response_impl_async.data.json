{".class": "MypyFile", "_fullname": "azure.core.rest._http_response_impl_async", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncHttpResponseBackcompatMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin", "name": "AsyncHttpResponseBackcompatMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.rest._http_response_impl_async", "mro": ["azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin", "azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin.__getattr__", "name": "__getattr__", "type": null}}, "parts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin.parts", "name": "parts", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncHttpResponseImpl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.rest._http_response_impl._HttpResponseBaseImpl", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl", "name": "AsyncHttpResponseImpl", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.rest._http_response_impl_async", "mro": ["azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl", "azure.core.rest._http_response_impl._HttpResponseBaseImpl", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.rest._rest_py3._HttpResponseBase", "typing.AsyncContextManager", "contextlib.AbstractAsyncContextManager", "abc.ABC", "azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin", "azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase", "builtins.object"], "names": {".class": "SymbolTable", "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "arg_types": ["azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncHttpResponseImpl", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of AsyncHttpResponseImpl", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_read_checks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl._set_read_checks", "name": "_set_read_checks", "type": null}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of AsyncHttpResponseImpl", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl.iter_bytes", "name": "iter_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_bytes of AsyncHttpResponseImpl", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl.iter_raw", "name": "iter_raw", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_raw of AsyncHttpResponseImpl", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of AsyncHttpResponseImpl", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RestAsyncHttpClientTransportResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.rest._http_response_impl._RestHttpClientTransportResponseBase", "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.rest._http_response_impl_async.RestAsyncHttpClientTransportResponse", "name": "RestAsyncHttpClientTransportResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.rest._http_response_impl_async.RestAsyncHttpClientTransportResponse", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.rest._http_response_impl_async", "mro": ["azure.core.rest._http_response_impl_async.RestAsyncHttpClientTransportResponse", "azure.core.rest._http_response_impl._RestHttpClientTransportResponseBase", "azure.core.rest._http_response_impl_async.AsyncHttpResponseImpl", "azure.core.rest._http_response_impl._HttpResponseBaseImpl", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.rest._rest_py3._HttpResponseBase", "typing.AsyncContextManager", "contextlib.AbstractAsyncContextManager", "abc.ABC", "azure.core.rest._http_response_impl._RestHttpClientTransportResponseBackcompatBaseMixin", "azure.core.rest._http_response_impl_async.AsyncHttpResponseBackcompatMixin", "azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase", "builtins.object"], "names": {".class": "SymbolTable", "iter_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.rest._http_response_impl_async.RestAsyncHttpClientTransportResponse.iter_bytes", "name": "iter_bytes", "type": null}}, "iter_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.rest._http_response_impl_async.RestAsyncHttpClientTransportResponse.iter_raw", "name": "iter_raw", "type": null}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "azure.core.rest._http_response_impl_async.RestAsyncHttpClientTransportResponse.read", "name": "read", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.rest._http_response_impl_async.RestAsyncHttpClientTransportResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.rest._http_response_impl_async.RestAsyncHttpClientTransportResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "_AsyncHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.AsyncHttpResponse", "kind": "Gdef"}, "_HttpResponseBackcompatMixinBase": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._http_response_impl._HttpResponseBackcompatMixinBase", "kind": "Gdef"}, "_HttpResponseBaseImpl": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._http_response_impl._HttpResponseBaseImpl", "kind": "Gdef"}, "_PartGenerator": {".class": "SymbolTableNode", "cross_ref": "azure.core.utils._pipeline_transport_rest_shared_async._PartGenerator", "kind": "Gdef"}, "_RestHttpClientTransportResponseBase": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._http_response_impl._RestHttpClientTransportResponseBase", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._http_response_impl_async.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._http_response_impl_async.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._http_response_impl_async.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._http_response_impl_async.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._http_response_impl_async.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.rest._http_response_impl_async.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_pad_attr_name": {".class": "SymbolTableNode", "cross_ref": "azure.core.utils._pipeline_transport_rest_shared._pad_attr_name", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\rest\\_http_response_impl_async.py"}