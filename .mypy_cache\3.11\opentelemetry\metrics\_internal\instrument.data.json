{".class": "MypyFile", "_fullname": "opentelemetry.metrics._internal.instrument", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Asynchronous": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1]], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Instrument"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.Asynchronous", "name": "Asynchronous", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.Asynchronous", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "opentelemetry.metrics._internal.instrument.Asynchronous.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.Asynchronous", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Asynchronous", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument.Asynchronous.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.Asynchronous", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Asynchronous", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.Asynchronous.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.Asynchronous", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Attributes": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.util.types.Attributes", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CallbackOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.CallbackOptions", "name": "CallbackOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.CallbackOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "timeout_millis", "type": "builtins.float"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.CallbackOptions", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "opentelemetry.metrics._internal.instrument.CallbackOptions.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout_millis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.CallbackOptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "timeout_millis"], "arg_types": ["opentelemetry.metrics._internal.instrument.CallbackOptions", "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CallbackOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "opentelemetry.metrics._internal.instrument.CallbackOptions.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "timeout_millis"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["timeout_millis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "opentelemetry.metrics._internal.instrument.CallbackOptions.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["timeout_millis"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CallbackOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "opentelemetry.metrics._internal.instrument.CallbackOptions.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["timeout_millis"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CallbackOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "timeout_millis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "opentelemetry.metrics._internal.instrument.CallbackOptions.timeout_millis", "name": "timeout_millis", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.CallbackOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.CallbackOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CallbackT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "opentelemetry.metrics._internal.instrument.CallbackT", "line": 67, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["opentelemetry.metrics._internal.instrument.CallbackOptions"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["opentelemetry.metrics._internal.observation.Observation"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "Instance", "args": ["opentelemetry.metrics._internal.observation.Observation"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "opentelemetry.metrics._internal.instrument.CallbackOptions", {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}], "uses_pep604_syntax": false}}}, "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1], ["add", 1]], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Synchronous"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.Counter", "name": "Counter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.Counter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.Counter", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.instrument.Counter.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.Counter", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of Counter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument.Counter.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.Counter", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of Counter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.Counter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.Counter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Gauge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1], ["set", 1]], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Synchronous"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.Gauge", "name": "Gauge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.Gauge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.Gauge", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.instrument.Gauge.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.Gauge", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of Gauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument.Gauge.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.Gauge", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of Gauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.Gauge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.Gauge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Histogram": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1], ["record", 1]], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Synchronous"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.Histogram", "name": "Histogram", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.Histogram", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.Histogram", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.instrument.Histogram.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "arg_types": ["opentelemetry.metrics._internal.instrument.Histogram", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Histogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument.Histogram.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "arg_types": ["opentelemetry.metrics._internal.instrument.Histogram", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Histogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "record": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.instrument.Histogram.record", "name": "record", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.Histogram", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record of Histogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument.Histogram.record", "name": "record", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.Histogram", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record of Histogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.Histogram.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.Histogram", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Instrument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.Instrument", "name": "Instrument", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.Instrument", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.instrument.Instrument.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.Instrument", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Instrument", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument.Instrument.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.Instrument", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Instrument", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_check_name_unit_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "opentelemetry.metrics._internal.instrument.Instrument._check_name_unit_description", "name": "_check_name_unit_description", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "unit", "description"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_name_unit_description of Instrument", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument.Instrument._check_name_unit_description", "name": "_check_name_unit_description", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "unit", "description"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_name_unit_description of Instrument", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.Instrument.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InstrumentT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "name": "InstrumentT", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "NoOpCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Counter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.NoOpCounter", "name": "NoOpCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.NoOpCounter", "opentelemetry.metrics._internal.instrument.Counter", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpCounter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpCounter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoOpCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpCounter.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpCounter", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of NoOpCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.NoOpCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.NoOpCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpGauge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Gauge"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.NoOpGauge", "name": "NoOpGauge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpGauge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.NoOpGauge", "opentelemetry.metrics._internal.instrument.Gauge", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpGauge.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpGauge", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoOpGauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpGauge.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpGauge", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of NoOpGauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.NoOpGauge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.NoOpGauge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpHistogram": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Histogram"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.NoOpHistogram", "name": "NoOpHistogram", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpHistogram", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.NoOpHistogram", "opentelemetry.metrics._internal.instrument.Histogram", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpHistogram.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpHistogram", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoOpHistogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "record": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpHistogram.record", "name": "record", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpHistogram", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record of NoOpHistogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.NoOpHistogram.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.NoOpHistogram", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpObservableCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.ObservableCounter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableCounter", "name": "NoOpObservableCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.NoOpObservableCounter", "opentelemetry.metrics._internal.instrument.ObservableCounter", "opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableCounter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpObservableCounter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoOpObservableCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.NoOpObservableCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpObservableGauge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.ObservableGauge"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableGauge", "name": "NoOpObservableGauge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableGauge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.NoOpObservableGauge", "opentelemetry.metrics._internal.instrument.ObservableGauge", "opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableGauge.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpObservableGauge", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoOpObservableGauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableGauge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.NoOpObservableGauge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpObservableUpDownCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.ObservableUpDownCounter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableUpDownCounter", "name": "NoOpObservableUpDownCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableUpDownCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.NoOpObservableUpDownCounter", "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableUpDownCounter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpObservableUpDownCounter", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoOpObservableUpDownCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.NoOpObservableUpDownCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.NoOpObservableUpDownCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpUpDownCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.UpDownCounter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.NoOpUpDownCounter", "name": "NoOpUpDownCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpUpDownCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.NoOpUpDownCounter", "opentelemetry.metrics._internal.instrument.UpDownCounter", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpUpDownCounter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpUpDownCounter", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoOpUpDownCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument.NoOpUpDownCounter.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.NoOpUpDownCounter", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of NoOpUpDownCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.NoOpUpDownCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.NoOpUpDownCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObservableCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1]], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Asynchronous"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.ObservableCounter", "name": "ObservableCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.ObservableCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.ObservableCounter", "opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.ObservableCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.ObservableCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObservableGauge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1]], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Asynchronous"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.ObservableGauge", "name": "ObservableGauge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.ObservableGauge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.ObservableGauge", "opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.ObservableGauge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.ObservableGauge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObservableUpDownCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1]], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Asynchronous"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "name": "ObservableUpDownCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Observation": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics._internal.observation.Observation", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Synchronous": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1]], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Instrument"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.Synchronous", "name": "Synchronous", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.Synchronous", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.Synchronous.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.Synchronous", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpDownCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__init__", 1], ["add", 1]], "alt_promote": null, "bases": ["opentelemetry.metrics._internal.instrument.Synchronous"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument.UpDownCounter", "name": "UpDownCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument.UpDownCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument.UpDownCounter", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.instrument.UpDownCounter.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.UpDownCounter", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of UpDownCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument.UpDownCounter.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument.UpDownCounter", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of UpDownCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.UpDownCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument.UpDownCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MetricsHistogramAdvisory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", "name": "_MetricsHistogramAdvisory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "explicit_bucket_boundaries", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "explicit_bucket_boundaries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "explicit_bucket_boundaries"], "arg_types": ["opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _MetricsHistogramAdvisory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "explicit_bucket_boundaries"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["explicit_bucket_boundaries"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["explicit_bucket_boundaries"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _MetricsHistogramAdvisory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["explicit_bucket_boundaries"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _MetricsHistogramAdvisory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "explicit_bucket_boundaries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory.explicit_bucket_boundaries", "name": "explicit_bucket_boundaries", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument._MetricsHistogramAdvisory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyAsynchronousInstrument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_create_real_instrument", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "name": "_ProxyAsynchronousInstrument", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "opentelemetry.metrics._internal.instrument._ProxyInstrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "callbacks", "unit", "description"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ProxyAsynchronousInstrument", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_callbacks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument._callbacks", "name": "_callbacks", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.metrics._internal.instrument.CallbackT"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["InstrumentT"], "typeddict_type": null}}, "_ProxyCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["opentelemetry.metrics._internal.instrument.Counter"], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}, "opentelemetry.metrics._internal.instrument.Counter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._ProxyCounter", "name": "_ProxyCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._ProxyCounter", "opentelemetry.metrics._internal.instrument._ProxyInstrument", "opentelemetry.metrics._internal.instrument.Counter", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_create_real_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyCounter._create_real_instrument", "name": "_create_real_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyCounter", "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_real_instrument of _ProxyCounter", "ret_type": "opentelemetry.metrics._internal.instrument.Counter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyCounter.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyCounter", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of _ProxyCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._ProxyCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument._ProxyCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyGauge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["opentelemetry.metrics._internal.instrument.Gauge"], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}, "opentelemetry.metrics._internal.instrument.Gauge"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._ProxyGauge", "name": "_ProxyGauge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyGauge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._ProxyGauge", "opentelemetry.metrics._internal.instrument._ProxyInstrument", "opentelemetry.metrics._internal.instrument.Gauge", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_create_real_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyGauge._create_real_instrument", "name": "_create_real_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyGauge", "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_real_instrument of _ProxyGauge", "ret_type": "opentelemetry.metrics._internal.instrument.Gauge", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyGauge.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyGauge", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of _ProxyGauge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._ProxyGauge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument._ProxyGauge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyHistogram": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["opentelemetry.metrics._internal.instrument.Histogram"], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}, "opentelemetry.metrics._internal.instrument.Histogram"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._ProxyHistogram", "name": "_ProxyHistogram", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyHistogram", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._ProxyHistogram", "opentelemetry.metrics._internal.instrument._ProxyInstrument", "opentelemetry.metrics._internal.instrument.Histogram", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyHistogram.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "name", "unit", "description", "explicit_bucket_boundaries_advisory"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyHistogram", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ProxyHistogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_real_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyHistogram._create_real_instrument", "name": "_create_real_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyHistogram", "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_real_instrument of _ProxyHistogram", "ret_type": "opentelemetry.metrics._internal.instrument.Histogram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_explicit_bucket_boundaries_advisory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyHistogram._explicit_bucket_boundaries_advisory", "name": "_explicit_bucket_boundaries_advisory", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "record": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyHistogram.record", "name": "record", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyHistogram", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record of _ProxyHistogram", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._ProxyHistogram.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument._ProxyHistogram", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyInstrument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_create_real_instrument", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "name": "_ProxyInstrument", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._ProxyInstrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "unit", "description"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ProxyInstrument", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_real_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument._create_real_instrument", "name": "_create_real_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}, "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_real_instrument of _ProxyInstrument", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument._create_real_instrument", "name": "_create_real_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}, "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_real_instrument of _ProxyInstrument", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument._description", "name": "_description", "type": "builtins.str"}}, "_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument._name", "name": "_name", "type": "builtins.str"}}, "_real_instrument": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument._real_instrument", "name": "_real_instrument", "type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_unit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument._unit", "name": "_unit", "type": "builtins.str"}}, "on_meter_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument.on_meter_set", "name": "on_meter_set", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}, "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_meter_set of _ProxyInstrument", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._ProxyInstrument.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument.InstrumentT", "id": 1, "name": "InstrumentT", "namespace": "opentelemetry.metrics._internal.instrument._ProxyInstrument", "upper_bound": "opentelemetry.metrics._internal.instrument.Instrument", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["InstrumentT"], "typeddict_type": null}}, "_ProxyObservableCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["opentelemetry.metrics._internal.instrument.ObservableCounter"], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument"}, "opentelemetry.metrics._internal.instrument.ObservableCounter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableCounter", "name": "_ProxyObservableCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._ProxyObservableCounter", "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "opentelemetry.metrics._internal.instrument._ProxyInstrument", "opentelemetry.metrics._internal.instrument.ObservableCounter", "opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_create_real_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableCounter._create_real_instrument", "name": "_create_real_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyObservableCounter", "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_real_instrument of _ProxyObservableCounter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument._ProxyObservableCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyObservableGauge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["opentelemetry.metrics._internal.instrument.ObservableGauge"], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument"}, "opentelemetry.metrics._internal.instrument.ObservableGauge"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableGauge", "name": "_ProxyObservableGauge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableGauge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._ProxyObservableGauge", "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "opentelemetry.metrics._internal.instrument._ProxyInstrument", "opentelemetry.metrics._internal.instrument.ObservableGauge", "opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_create_real_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableGauge._create_real_instrument", "name": "_create_real_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyObservableGauge", "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_real_instrument of _ProxyObservableGauge", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableGauge", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableGauge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument._ProxyObservableGauge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyObservableUpDownCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["opentelemetry.metrics._internal.instrument.ObservableUpDownCounter"], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument"}, "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableUpDownCounter", "name": "_ProxyObservableUpDownCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableUpDownCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._ProxyObservableUpDownCounter", "opentelemetry.metrics._internal.instrument._ProxyAsynchronousInstrument", "opentelemetry.metrics._internal.instrument._ProxyInstrument", "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "opentelemetry.metrics._internal.instrument.Asynchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_create_real_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableUpDownCounter._create_real_instrument", "name": "_create_real_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyObservableUpDownCounter", "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_real_instrument of _ProxyObservableUpDownCounter", "ret_type": "opentelemetry.metrics._internal.instrument.ObservableUpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._ProxyObservableUpDownCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument._ProxyObservableUpDownCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyUpDownCounter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["opentelemetry.metrics._internal.instrument.UpDownCounter"], "extra_attrs": null, "type_ref": "opentelemetry.metrics._internal.instrument._ProxyInstrument"}, "opentelemetry.metrics._internal.instrument.UpDownCounter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.metrics._internal.instrument._ProxyUpDownCounter", "name": "_ProxyUpDownCounter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyUpDownCounter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.metrics._internal.instrument", "mro": ["opentelemetry.metrics._internal.instrument._ProxyUpDownCounter", "opentelemetry.metrics._internal.instrument._ProxyInstrument", "opentelemetry.metrics._internal.instrument.UpDownCounter", "opentelemetry.metrics._internal.instrument.Synchronous", "opentelemetry.metrics._internal.instrument.Instrument", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_create_real_instrument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyUpDownCounter._create_real_instrument", "name": "_create_real_instrument", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "meter"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyUpDownCounter", "opentelemetry.metrics._internal.Meter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_real_instrument of _ProxyUpDownCounter", "ret_type": "opentelemetry.metrics._internal.instrument.UpDownCounter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.metrics._internal.instrument._ProxyUpDownCounter.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "amount", "attributes", "context"], "arg_types": ["opentelemetry.metrics._internal.instrument._ProxyUpDownCounter", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of _ProxyUpDownCounter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.metrics._internal.instrument._ProxyUpDownCounter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.metrics._internal.instrument._ProxyUpDownCounter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.instrument.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.instrument.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.instrument.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.instrument.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.instrument.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.metrics._internal.instrument.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.metrics._internal.instrument._logger", "name": "_logger", "type": "logging.Logger"}}, "_name_regex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.metrics._internal.instrument._name_regex", "name": "_name_regex", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_unit_regex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.metrics._internal.instrument._unit_regex", "name": "_unit_regex", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "getLogger": {".class": "SymbolTableNode", "cross_ref": "logging.getLogger", "kind": "Gdef"}, "metrics": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.metrics", "kind": "Gdef"}, "re_compile": {".class": "SymbolTableNode", "cross_ref": "re.compile", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\metrics\\_internal\\instrument.py"}