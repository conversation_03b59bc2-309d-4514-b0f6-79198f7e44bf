{"data_mtime": 1753366020, "dep_lines": [2, 10, 12, 26, 26, 27, 28, 29, 41, 43, 52, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 13, 24, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 5, 5, 5, 5, 5, 25, 10, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "concurrent.futures", "http.cookies", "aiohttp.hdrs", "aiohttp.payload", "aiohttp.abc", "aiohttp.compression_utils", "aiohttp.helpers", "aiohttp.http", "aiohttp.typedefs", "aiohttp.web_request", "asyncio", "collections", "datetime", "enum", "json", "math", "time", "warnings", "zlib", "http", "typing", "multidict", "aiohttp", "builtins", "_asyncio", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "aiohttp.http_writer", "concurrent", "concurrent.futures._base", "functools", "itertools", "json.encoder", "types", "typing_extensions"], "hash": "61ec6679fea51f5177659019310b20a44e8c807a", "id": "aiohttp.web_response", "ignore_all": true, "interface_hash": "42348f3e4bea5d3731803ddb66899d1a92fd51fc", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web_response.py", "plugin_data": null, "size": 29536, "suppressed": [], "version_id": "1.15.0"}