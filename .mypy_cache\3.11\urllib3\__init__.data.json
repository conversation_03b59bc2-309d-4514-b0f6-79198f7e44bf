{".class": "MypyFile", "_fullname": "urllib3", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseHTTPResponse": {".class": "SymbolTableNode", "cross_ref": "urllib3.response.BaseHTTPResponse", "kind": "Gdef"}, "HTTPConnectionPool": {".class": "SymbolTableNode", "cross_ref": "urllib3.connectionpool.HTTPConnectionPool", "kind": "Gdef"}, "HTTPHeaderDict": {".class": "SymbolTableNode", "cross_ref": "urllib3._collections.HTTPHeaderDict", "kind": "Gdef"}, "HTTPResponse": {".class": "SymbolTableNode", "cross_ref": "urllib3.response.HTTPResponse", "kind": "Gdef"}, "HTTPSConnectionPool": {".class": "SymbolTableNode", "cross_ref": "urllib3.connectionpool.HTTPSConnectionPool", "kind": "Gdef"}, "NullHandler": {".class": "SymbolTableNode", "cross_ref": "logging.NullHandler", "kind": "Gdef", "module_public": false}, "PoolManager": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolManager", "kind": "Gdef"}, "ProxyManager": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.ProxyManager", "kind": "Gdef"}, "Retry": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.retry.Retry", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout.Timeout", "kind": "Gdef"}, "_DEFAULT_POOL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3._DEFAULT_POOL", "name": "_DEFAULT_POOL", "type": "urllib3.poolmanager.PoolManager"}}, "_TYPE_BODY": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection._TYPE_BODY", "kind": "Gdef", "module_public": false}, "_TYPE_FIELDS": {".class": "SymbolTableNode", "cross_ref": "urllib3.filepost._TYPE_FIELDS", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.__author__", "name": "__author__", "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.__file__", "name": "__file__", "type": "builtins.str"}}, "__license__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.__license__", "name": "__license__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "urllib3._version.__version__", "kind": "Gdef", "module_public": false}, "add_stderr_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.add_stderr_logger", "name": "add_stderr_logger", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["level"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_stderr_logger", "ret_type": {".class": "Instance", "args": ["typing.TextIO"], "extra_attrs": null, "type_ref": "logging.StreamHandler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "connection_from_url": {".class": "SymbolTableNode", "cross_ref": "urllib3.connectionpool.connection_from_url", "kind": "Gdef"}, "disable_warnings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["category"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.disable_warnings", "name": "disable_warnings", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["category"], "arg_types": [{".class": "TypeType", "item": "builtins.Warning"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_warnings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode_multipart_formdata": {".class": "SymbolTableNode", "cross_ref": "urllib3.filepost.encode_multipart_formdata", "kind": "Gdef"}, "exceptions": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions", "kind": "Gdef", "module_public": false}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "make_headers": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.request.make_headers", "kind": "Gdef"}, "proxy_from_url": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.proxy_from_url", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "body", "fields", "headers", "preload_content", "decode_content", "redirect", "retries", "timeout", "json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["method", "url", "body", "fields", "headers", "preload_content", "decode_content", "redirect", "retries", "timeout", "json"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection._TYPE_BODY"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.filepost._TYPE_FIELDS"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.bool", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["urllib3.util.timeout.Timeout", "builtins.float", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request", "ret_type": "urllib3.response.BaseHTTPResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\urllib3\\__init__.py"}