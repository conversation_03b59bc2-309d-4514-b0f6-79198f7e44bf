{"data_mtime": **********, "dep_lines": [87, 89, 94, 109, 110, 111, 112, 113, 85, 86, 88, 110, 76, 77, 78, 79, 80, 83, 85, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 10, 5, 5, 20, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30], "dependencies": ["opentelemetry.context.context", "opentelemetry.trace.propagation", "opentelemetry.trace.span", "opentelemetry.trace.status", "opentelemetry.util.types", "opentelemetry.util._decorator", "opentelemetry.util._once", "opentelemetry.util._providers", "opentelemetry.context", "opentelemetry.attributes", "opentelemetry.environment_variables", "opentelemetry.util", "os", "typing", "abc", "enum", "logging", "typing_extensions", "opentelemetry", "builtins", "_frozen_importlib", "contextlib"], "hash": "16ee7c38139e2ae35232ed1d6d076ce96d9b08a5", "id": "opentelemetry.trace", "ignore_all": true, "interface_hash": "55bfd802d1ab61a0d9b736fe50c9558706e39cd0", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\trace\\__init__.py", "plugin_data": null, "size": 22925, "suppressed": [], "version_id": "1.15.0"}