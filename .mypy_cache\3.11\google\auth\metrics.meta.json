{"data_mtime": 1753366016, "dep_lines": [21, 21, 19, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 5, 30, 30, 30], "dependencies": ["google.auth.version", "google.auth", "platform", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "1be1f6c1de3b9085e8c29b316d4446fe3eb67695", "id": "google.auth.metrics", "ignore_all": true, "interface_hash": "a31392331ae7bc29d9cf17ef4a366c48c8e25643", "mtime": 1750258720, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\google\\auth\\metrics.py", "plugin_data": null, "size": 5614, "suppressed": [], "version_id": "1.15.0"}