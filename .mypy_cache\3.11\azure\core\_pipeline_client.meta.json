{"data_mtime": 1753366021, "dep_lines": [32, 176, 33, 34, 30, 31, 28, 26, 27, 29, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 5, 5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.transport._base", "azure.core.pipeline.transport._requests_basic", "azure.core.pipeline.transport", "azure.core.pipeline.policies", "azure.core.configuration", "azure.core.pipeline", "collections.abc", "__future__", "logging", "typing", "builtins", "_frozen_importlib", "abc", "azure.core.pipeline._base", "contextlib"], "hash": "a4835388924f95d0100a4cc46356015d64f1c2ed", "id": "azure.core._pipeline_client", "ignore_all": true, "interface_hash": "7e4787a049eb9b2f75d8f30dc5aa72dac2621d5c", "mtime": 1750470990, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\_pipeline_client.py", "plugin_data": null, "size": 8721, "suppressed": [], "version_id": "1.15.0"}