{".class": "MypyFile", "_fullname": "aiohttp._websocket.helpers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MASK_LEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.MASK_LEN", "name": "MASK_LEN", "type": "builtins.int"}}, "MSG_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16384, "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.MSG_SIZE", "name": "MSG_SIZE", "type": "builtins.int"}}, "NO_EXTENSIONS": {".class": "SymbolTableNode", "cross_ref": "aiohttp.helpers.NO_EXTENSIONS", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PACK_CLOSE_CODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.PACK_CLOSE_CODE", "name": "PACK_CLOSE_CODE", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["_struct.Struct"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PACK_LEN1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.PACK_LEN1", "name": "PACK_LEN1", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["_struct.Struct"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PACK_LEN2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.PACK_LEN2", "name": "PACK_LEN2", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["_struct.Struct"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PACK_LEN3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.PACK_LEN3", "name": "PACK_LEN3", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["_struct.Struct"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "PACK_RANDBITS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.PACK_RANDBITS", "name": "PACK_RANDBITS", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["_struct.Struct"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "Struct": {".class": "SymbolTableNode", "cross_ref": "_struct.Struct", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "UNPACK_CLOSE_CODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.UNPACK_CLOSE_CODE", "name": "UNPACK_CLOSE_CODE", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing_extensions.Buffer"], "bound_args": ["_struct.Struct"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "UNPACK_LEN3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.UNPACK_LEN3", "name": "UNPACK_LEN3", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["buffer", "offset"], "arg_types": ["typing_extensions.Buffer", "builtins.int"], "bound_args": ["_struct.Struct"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WSHandshakeError": {".class": "SymbolTableNode", "cross_ref": "aiohttp._websocket.models.WSHandshakeError", "kind": "Gdef"}, "WS_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.WS_KEY", "name": "WS_KEY", "type": "builtins.bytes"}}, "_WS_EXT_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers._WS_EXT_RE", "name": "_WS_EXT_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_WS_EXT_RE_SPLIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers._WS_EXT_RE_SPLIT", "name": "_WS_EXT_RE_SPLIT", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.helpers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.helpers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.helpers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.helpers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.helpers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp._websocket.helpers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_websocket_mask_python": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mask", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp._websocket.helpers._websocket_mask_python", "name": "_websocket_mask_python", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mask", "data"], "arg_types": ["builtins.bytes", "builtins.bytearray"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_websocket_mask_python", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_xor_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiohttp._websocket.helpers._xor_table", "name": "_xor_table", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_xor_table", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiohttp._websocket.helpers._xor_table", "name": "_xor_table", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "websocket_mask": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp._websocket.helpers.websocket_mask", "name": "websocket_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mask", "data"], "arg_types": ["builtins.bytes", "builtins.bytearray"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ws_ext_gen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["compress", "isserver", "server_notakeover"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp._websocket.helpers.ws_ext_gen", "name": "ws_ext_gen", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["compress", "isserver", "server_notakeover"], "arg_types": ["builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ws_ext_gen", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ws_ext_parse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["extstr", "isserver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp._websocket.helpers.ws_ext_parse", "name": "ws_ext_parse", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["extstr", "isserver"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ws_ext_parse", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\_websocket\\helpers.py"}