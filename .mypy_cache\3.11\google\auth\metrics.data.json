{".class": "MypyFile", "_fullname": "google.auth.metrics", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "API_CLIENT_HEADER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.API_CLIENT_HEADER", "name": "API_CLIENT_HEADER", "type": "builtins.str"}}, "BYOID_HEADER_SECTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.BYOID_HEADER_SECTION", "name": "BYOID_HEADER_SECTION", "type": "builtins.str"}}, "CRED_TYPE_SA_ASSERTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.CRED_TYPE_SA_ASSERTION", "name": "CRED_TYPE_SA_ASSERTION", "type": "builtins.str"}}, "CRED_TYPE_SA_IMPERSONATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.CRED_TYPE_SA_IMPERSONATE", "name": "CRED_TYPE_SA_IMPERSONATE", "type": "builtins.str"}}, "CRED_TYPE_SA_JWT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.CRED_TYPE_SA_JWT", "name": "CRED_TYPE_SA_JWT", "type": "builtins.str"}}, "CRED_TYPE_SA_MDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.CRED_TYPE_SA_MDS", "name": "CRED_TYPE_SA_MDS", "type": "builtins.str"}}, "CRED_TYPE_USER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.CRED_TYPE_USER", "name": "CRED_TYPE_USER", "type": "builtins.str"}}, "REQUEST_TYPE_ACCESS_TOKEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.REQUEST_TYPE_ACCESS_TOKEN", "name": "REQUEST_TYPE_ACCESS_TOKEN", "type": "builtins.str"}}, "REQUEST_TYPE_ID_TOKEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.REQUEST_TYPE_ID_TOKEN", "name": "REQUEST_TYPE_ID_TOKEN", "type": "builtins.str"}}, "REQUEST_TYPE_MDS_PING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.REQUEST_TYPE_MDS_PING", "name": "REQUEST_TYPE_MDS_PING", "type": "builtins.str"}}, "REQUEST_TYPE_REAUTH_CONTINUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.REQUEST_TYPE_REAUTH_CONTINUE", "name": "REQUEST_TYPE_REAUTH_CONTINUE", "type": "builtins.str"}}, "REQUEST_TYPE_REAUTH_START": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth.metrics.REQUEST_TYPE_REAUTH_START", "name": "REQUEST_TYPE_REAUTH_START", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.metrics.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.metrics.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.metrics.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.metrics.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.metrics.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth.metrics.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_metric_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["headers", "metric_header_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.add_metric_header", "name": "add_metric_header", "type": null}}, "byoid_metrics_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["metrics_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.byoid_metrics_header", "name": "byoid_metrics_header", "type": null}}, "mds_ping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.mds_ping", "name": "mds_ping", "type": null}}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef"}, "python_and_auth_lib_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.python_and_auth_lib_version", "name": "python_and_auth_lib_version", "type": null}}, "reauth_continue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.reauth_continue", "name": "reauth_continue", "type": null}}, "reauth_start": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.reauth_start", "name": "reauth_start", "type": null}}, "token_request_access_token_impersonate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.token_request_access_token_impersonate", "name": "token_request_access_token_impersonate", "type": null}}, "token_request_access_token_mds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.token_request_access_token_mds", "name": "token_request_access_token_mds", "type": null}}, "token_request_access_token_sa_assertion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.token_request_access_token_sa_assertion", "name": "token_request_access_token_sa_assertion", "type": null}}, "token_request_id_token_impersonate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.token_request_id_token_impersonate", "name": "token_request_id_token_impersonate", "type": null}}, "token_request_id_token_mds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.token_request_id_token_mds", "name": "token_request_id_token_mds", "type": null}}, "token_request_id_token_sa_assertion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.token_request_id_token_sa_assertion", "name": "token_request_id_token_sa_assertion", "type": null}}, "token_request_user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth.metrics.token_request_user", "name": "token_request_user", "type": null}}, "version": {".class": "SymbolTableNode", "cross_ref": "google.auth.version", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\google\\auth\\metrics.py"}