{".class": "MypyFile", "_fullname": "aiohttp.http_exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadHttpMessage": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.HttpProcessingError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.BadHttpMessage", "name": "BadHttpMessage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.BadHttpMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "message", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.BadHttpMessage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "message", "headers"], "arg_types": ["aiohttp.http_exceptions.BadHttpMessage", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs._CIMultiDict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BadHttpMessage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_exceptions.BadHttpMessage.code", "name": "code", "type": "builtins.int"}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_exceptions.BadHttpMessage.message", "name": "message", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.BadHttpMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.BadHttpMessage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadHttpMethod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.BadStatusLine"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.BadHttpMethod", "name": "BadHttpMethod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.BadHttpMethod", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.BadHttpMethod", "aiohttp.http_exceptions.BadStatusLine", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "line", "error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.BadHttpMethod.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "line", "error"], "arg_types": ["aiohttp.http_exceptions.BadHttpMethod", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BadHttpMethod", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.BadHttpMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.BadHttpMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadStatusLine": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.BadHttpMessage"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.BadStatusLine", "name": "BadStatusLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.BadStatusLine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.BadStatusLine", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "line", "error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.BadStatusLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "line", "error"], "arg_types": ["aiohttp.http_exceptions.BadStatusLine", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BadStatusLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_exceptions.BadStatusLine.line", "name": "line", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.BadStatusLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.BadStatusLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContentEncodingError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.PayloadEncodingError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.ContentEncodingError", "name": "ContentEncodingError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.ContentEncodingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.ContentEncodingError", "aiohttp.http_exceptions.PayloadEncodingError", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.ContentEncodingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.ContentEncodingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContentLengthError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.PayloadEncodingError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.ContentLengthError", "name": "ContentLengthError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.ContentLengthError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.ContentLengthError", "aiohttp.http_exceptions.PayloadEncodingError", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.ContentLengthError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.ContentLengthError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HttpBadRequest": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.BadHttpMessage"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.HttpBadRequest", "name": "HttpBadRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.HttpBadRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.HttpBadRequest", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_exceptions.HttpBadRequest.code", "name": "code", "type": "builtins.int"}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_exceptions.HttpBadRequest.message", "name": "message", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.HttpBadRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.HttpBadRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HttpProcessingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.HttpProcessingError", "name": "HttpProcessingError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.HttpProcessingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "code", "message", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.HttpProcessingError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "code", "message", "headers"], "arg_types": ["aiohttp.http_exceptions.HttpProcessingError", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs._CIMultiDict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HttpProcessingError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.HttpProcessingError.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.http_exceptions.HttpProcessingError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of HttpProcessingError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.HttpProcessingError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["aiohttp.http_exceptions.HttpProcessingError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of HttpProcessingError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_exceptions.HttpProcessingError.code", "name": "code", "type": "builtins.int"}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_exceptions.HttpProcessingError.headers", "name": "headers", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "aiohttp.typedefs._CIMultiDict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_exceptions.HttpProcessingError.message", "name": "message", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.HttpProcessingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.HttpProcessingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHeader": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.BadHttpMessage"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.InvalidHeader", "name": "InvalidHeader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.InvalidHeader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.InvalidHeader", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hdr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.InvalidHeader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hdr"], "arg_types": ["aiohttp.http_exceptions.InvalidHeader", {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidHeader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hdr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.http_exceptions.InvalidHeader.hdr", "name": "hdr", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.InvalidHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.InvalidHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidURLError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.BadHttpMessage"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.InvalidURLError", "name": "InvalidURLError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.InvalidURLError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.InvalidURLError", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.InvalidURLError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.InvalidURLError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LineTooLong": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.BadHttpMessage"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.LineTooLong", "name": "LineTooLong", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.LineTooLong", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.LineTooLong", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "line", "limit", "actual_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.LineTooLong.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "line", "limit", "actual_size"], "arg_types": ["aiohttp.http_exceptions.LineTooLong", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LineTooLong", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.LineTooLong.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.LineTooLong", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PayloadEncodingError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.BadHttpMessage"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.PayloadEncodingError", "name": "PayloadEncodingError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.PayloadEncodingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.PayloadEncodingError", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.PayloadEncodingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.PayloadEncodingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransferEncodingError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["aiohttp.http_exceptions.PayloadEncodingError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.http_exceptions.TransferEncodingError", "name": "TransferEncodingError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.http_exceptions.TransferEncodingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.http_exceptions", "mro": ["aiohttp.http_exceptions.TransferEncodingError", "aiohttp.http_exceptions.PayloadEncodingError", "aiohttp.http_exceptions.BadHttpMessage", "aiohttp.http_exceptions.HttpProcessingError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.http_exceptions.TransferEncodingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.http_exceptions.TransferEncodingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_CIMultiDict": {".class": "SymbolTableNode", "cross_ref": "aiohttp.typedefs._CIMultiDict", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.http_exceptions.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.http_exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "indent": {".class": "SymbolTableNode", "cross_ref": "textwrap.indent", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\http_exceptions.py"}