{"data_mtime": 1753366020, "dep_lines": [13, 14, 12, 15, 16, 17, 18, 31, 32, 33, 34, 35, 36, 37, 1, 2, 3, 4, 5, 6, 7, 9, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["aiohttp._websocket.reader", "aiohttp._websocket.writer", "aiohttp.hdrs", "aiohttp.abc", "aiohttp.client_exceptions", "aiohttp.helpers", "aiohttp.http", "aiohttp.http_websocket", "aiohttp.log", "aiohttp.streams", "aiohttp.typedefs", "aiohttp.web_exceptions", "aiohttp.web_request", "aiohttp.web_response", "asyncio", "base64", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "json", "sys", "typing", "attr", "multidict", "aiohttp", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_random", "_typeshed", "abc", "aiohttp._websocket", "aiohttp._websocket.models", "aiohttp._websocket.reader_py", "aiohttp.base_protocol", "aiohttp.web_protocol", "asyncio.events", "asyncio.exceptions", "asyncio.protocols", "asyncio.timeouts", "asyncio.transports", "attr.setters", "enum", "json.decoder", "json.encoder", "random"], "hash": "5c36c23f62ead4b50e73e3688669d51e633d8845", "id": "aiohttp.web_ws", "ignore_all": true, "interface_hash": "b4205c63d148fb6e1d3037af365eea67216e150b", "mtime": 1750470998, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\web_ws.py", "plugin_data": null, "size": 23110, "suppressed": [], "version_id": "1.15.0"}