"""Monitoring and metrics for LLM Proxy Server"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from collections import defaultdict, deque
import structlog

logger = structlog.get_logger()


class MetricsManager:
    """Manages metrics and monitoring for the proxy server"""
    
    def __init__(self):
        self.start_time = time.time()
        
        # Request metrics
        self.request_count = defaultdict(int)  # endpoint -> count
        self.request_duration = defaultdict(list)  # endpoint -> [durations]
        self.error_count = defaultdict(int)  # endpoint -> error count
        self.status_codes = defaultdict(int)  # status_code -> count
        
        # Host metrics
        self.host_request_count = defaultdict(int)  # host -> count
        self.host_error_count = defaultdict(int)  # host -> error count
        self.host_response_times = defaultdict(list)  # host -> [response_times]
        
        # Model metrics
        self.model_request_count = defaultdict(int)  # model -> count
        self.model_error_count = defaultdict(int)  # model -> error count
        
        # Recent activity (keep last 1000 entries)
        self.recent_requests = deque(maxlen=1000)
        
        # Health status
        self.health_checks = defaultdict(list)  # host -> [check_results]
        
        logger.info("Metrics manager initialized")
    
    def record_request(self, endpoint: str, method: str, status_code: int, 
                      duration: float, host: Optional[str] = None, 
                      model: Optional[str] = None, error: Optional[str] = None):
        """Record a request with its metrics"""
        
        # Basic request metrics
        request_key = f"{method} {endpoint}"
        self.request_count[request_key] += 1
        self.request_duration[request_key].append(duration)
        self.status_codes[status_code] += 1
        
        # Error tracking
        if status_code >= 400 or error:
            self.error_count[request_key] += 1
        
        # Host metrics
        if host:
            self.host_request_count[host] += 1
            self.host_response_times[host].append(duration)
            if status_code >= 400 or error:
                self.host_error_count[host] += 1
        
        # Model metrics
        if model:
            self.model_request_count[model] += 1
            if status_code >= 400 or error:
                self.model_error_count[model] += 1
        
        # Recent activity
        self.recent_requests.append({
            'timestamp': datetime.now(),
            'endpoint': endpoint,
            'method': method,
            'status_code': status_code,
            'duration': duration,
            'host': host,
            'model': model,
            'error': error
        })
        
        # Keep only recent durations (last 100 per endpoint/host)
        if len(self.request_duration[request_key]) > 100:
            self.request_duration[request_key] = self.request_duration[request_key][-100:]
        
        if host and len(self.host_response_times[host]) > 100:
            self.host_response_times[host] = self.host_response_times[host][-100:]
    
    def record_health_check(self, host: str, healthy: bool, response_time: Optional[float] = None):
        """Record a health check result"""
        check_result = {
            'timestamp': datetime.now(),
            'healthy': healthy,
            'response_time': response_time
        }
        
        self.health_checks[host].append(check_result)
        
        # Keep only recent health checks (last 50 per host)
        if len(self.health_checks[host]) > 50:
            self.health_checks[host] = self.health_checks[host][-50:]
    
    def get_request_stats(self) -> Dict:
        """Get request statistics"""
        total_requests = sum(self.request_count.values())
        total_errors = sum(self.error_count.values())
        
        # Calculate average response times
        avg_response_times = {}
        for endpoint, durations in self.request_duration.items():
            if durations:
                avg_response_times[endpoint] = sum(durations) / len(durations)
        
        return {
            'total_requests': total_requests,
            'total_errors': total_errors,
            'error_rate': total_errors / total_requests if total_requests > 0 else 0,
            'requests_by_endpoint': dict(self.request_count),
            'errors_by_endpoint': dict(self.error_count),
            'avg_response_times': avg_response_times,
            'status_codes': dict(self.status_codes)
        }
    
    def get_host_stats(self) -> Dict:
        """Get host statistics"""
        host_stats = {}
        
        for host in set(list(self.host_request_count.keys()) + list(self.health_checks.keys())):
            requests = self.host_request_count.get(host, 0)
            errors = self.host_error_count.get(host, 0)
            response_times = self.host_response_times.get(host, [])
            health_checks = self.health_checks.get(host, [])
            
            # Calculate health status
            recent_checks = [check for check in health_checks 
                           if check['timestamp'] > datetime.now() - timedelta(minutes=5)]
            is_healthy = len(recent_checks) > 0 and recent_checks[-1]['healthy']
            
            host_stats[host] = {
                'requests': requests,
                'errors': errors,
                'error_rate': errors / requests if requests > 0 else 0,
                'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
                'is_healthy': is_healthy,
                'last_health_check': recent_checks[-1]['timestamp'] if recent_checks else None
            }
        
        return host_stats
    
    def get_model_stats(self) -> Dict:
        """Get model statistics"""
        model_stats = {}
        
        for model in self.model_request_count.keys():
            requests = self.model_request_count[model]
            errors = self.model_error_count.get(model, 0)
            
            model_stats[model] = {
                'requests': requests,
                'errors': errors,
                'error_rate': errors / requests if requests > 0 else 0
            }
        
        return model_stats
    
    def get_health_status(self) -> Dict:
        """Get overall health status"""
        uptime = time.time() - self.start_time
        total_requests = sum(self.request_count.values())
        total_errors = sum(self.error_count.values())
        
        # Determine overall status
        error_rate = total_errors / total_requests if total_requests > 0 else 0
        
        if error_rate > 0.1:  # More than 10% error rate
            status = "unhealthy"
        elif error_rate > 0.05:  # More than 5% error rate
            status = "degraded"
        else:
            status = "healthy"
        
        return {
            'status': status,
            'uptime': uptime,
            'total_requests': total_requests,
            'total_errors': total_errors,
            'error_rate': error_rate
        }
    
    def get_recent_activity(self, limit: int = 50) -> List[Dict]:
        """Get recent request activity"""
        return list(self.recent_requests)[-limit:]
    
    def generate_metrics(self) -> Dict:
        """Generate comprehensive metrics data"""
        return {
            'request_stats': self.get_request_stats(),
            'host_stats': self.get_host_stats(),
            'model_stats': self.get_model_stats(),
            'health_status': self.get_health_status(),
            'recent_activity': self.get_recent_activity()
        }

    def reset_metrics(self):
        """Reset all metrics"""
        self.request_count.clear()
        self.request_duration.clear()
        self.error_count.clear()
        self.status_codes.clear()
        self.host_request_count.clear()
        self.host_error_count.clear()
        self.host_response_times.clear()
        self.model_request_count.clear()
        self.model_error_count.clear()
        self.recent_requests.clear()
        self.health_checks.clear()

        logger.info("Metrics reset")
