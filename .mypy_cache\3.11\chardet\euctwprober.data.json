{".class": "MypyFile", "_fullname": "chardet.euctwp<PERSON>r", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CodingStateMachine": {".class": "SymbolTableNode", "cross_ref": "chardet.codingstatemachine.CodingStateMachine", "kind": "Gdef"}, "EUCTWDistributionAnalysis": {".class": "SymbolTableNode", "cross_ref": "chardet.chardistribution.EUCTWDistributionAnalysis", "kind": "Gdef"}, "EUCTWProber": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["chardet.mbcharsetprober.MultiByteCharSetProber"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.euctwprober.EUCTWProber", "name": "EUCTWProber", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.euctwprober.EUCTWProber", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.euctwp<PERSON>r", "mro": ["chardet.euctwprober.EUCTWProber", "chardet.mbcharsetprober.MultiByteCharSetProber", "chardet.charsetprober.CharSetProber", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.euctwprober.EUCTWProber.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.euctwprober.EUCTWProber"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EUCTWProber", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "charset_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "chardet.euctwprober.EUCTWProber.charset_name", "name": "charset_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.euctwprober.EUCTWProber"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "charset_name of EUCTWProber", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "chardet.euctwprober.EUCTWProber.charset_name", "name": "charset_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.euctwprober.EUCTWProber"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "charset_name of EUCTWProber", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "chardet.euctwprober.EUCTWProber.language", "name": "language", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.euctwprober.EUCTWProber"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "language of EUCTWProber", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "chardet.euctwprober.EUCTWProber.language", "name": "language", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.euctwprober.EUCTWProber"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "language of EUCTWProber", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.euctwprober.EUCTWProber.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.euctwprober.EUCTWProber", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EUCTW_SM_MODEL": {".class": "SymbolTableNode", "cross_ref": "chardet.mbcssm.EUCTW_SM_MODEL", "kind": "Gdef"}, "MultiByteCharSetProber": {".class": "SymbolTableNode", "cross_ref": "chardet.mbcharsetprober.MultiByteCharSetProber", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.euctwprober.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.euctwprober.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.euctwprober.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.euctwprober.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.euctwprober.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.euctwprober.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\chardet\\euctwprober.py"}