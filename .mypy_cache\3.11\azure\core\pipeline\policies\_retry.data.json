{".class": "MypyFile", "_fullname": "azure.core.pipeline.policies._retry", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AllHttpResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.AllHttpResponseType", "name": "AllHttpResponseType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.AsyncHttpResponse", "kind": "Gdef"}, "AzureError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.AzureError", "kind": "Gdef"}, "CaseInsensitiveEnumMeta": {".class": "SymbolTableNode", "cross_ref": "azure.core._enum_meta.CaseInsensitiveEnumMeta", "kind": "Gdef"}, "ClientAuthenticationError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ClientAuthenticationError", "kind": "Gdef"}, "ClsRetryPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.ClsRetryPolicy", "name": "ClsRetryPolicy", "upper_bound": "azure.core.pipeline.policies._retry.RetryPolicyBase", "values": [], "variance": 0}}, "ConnectionConfiguration": {".class": "SymbolTableNode", "cross_ref": "azure.core.configuration.ConnectionConfiguration", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "HTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.HTTPPolicy", "kind": "Gdef"}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}}, "HTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "name": "HTTPResponseType", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}}, "HttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpRequest", "kind": "Gdef"}, "HttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.rest._rest_py3.HttpResponse", "kind": "Gdef"}, "HttpTransport": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpTransport", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "LegacyAsyncHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base_async.AsyncHttpResponse", "kind": "Gdef"}, "LegacyHttpRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpRequest", "kind": "Gdef"}, "LegacyHttpResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.transport._base.HttpResponse", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PipelineContext": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineContext", "kind": "Gdef"}, "PipelineRequest": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineRequest", "kind": "Gdef"}, "PipelineResponse": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.PipelineResponse", "kind": "Gdef"}, "RequestHistory": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.RequestHistory", "kind": "Gdef"}, "RetryMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": "azure.core._enum_meta.CaseInsensitiveEnumMeta", "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._retry.RetryMode", "name": "RetryMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "azure.core.pipeline.policies._retry.RetryMode", "has_param_spec_type": false, "metaclass_type": "azure.core._enum_meta.CaseInsensitiveEnumMeta", "metadata": {}, "module_name": "azure.core.pipeline.policies._retry", "mro": ["azure.core.pipeline.policies._retry.RetryMode", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "Exponential": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._retry.RetryMode.Exponential", "name": "Exponential", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "exponential"}, "type_ref": "builtins.str"}}}, "Fixed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._retry.RetryMode.Fixed", "name": "Fixed", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "fixed"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.RetryMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._retry.RetryMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RetryPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.HTTPPolicy"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._retry.RetryPolicy", "name": "RetryPolicy", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "azure.core.pipeline.policies._retry", "mro": ["azure.core.pipeline.policies._retry.RetryPolicy", "azure.core.pipeline.policies._retry.RetryPolicyBase", "azure.core.pipeline.policies._base.HTTPPolicy", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_sleep_backoff": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "settings", "transport"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicy._sleep_backoff", "name": "_sleep_backoff", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "settings", "transport"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._retry.RetryPolicy"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base.HttpTransport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sleep_backoff of RetryPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sleep_for_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "transport"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicy._sleep_for_retry", "name": "_sleep_for_retry", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "transport"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._retry.RetryPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base.HttpTransport"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sleep_for_retry of RetryPolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicy.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._retry.RetryPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send of RetryPolicy", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sleep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "settings", "transport", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicy.sleep", "name": "sleep", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "settings", "transport", "response"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._retry.RetryPolicy"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.transport._base.HttpTransport"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sleep of <PERSON>try<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.RetryPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicy", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._retry.RetryPolicy"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "HTTPResponseType"], "typeddict_type": null}}, "RetryPolicyBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase", "name": "RetryPolicyBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.pipeline.policies._retry", "mro": ["azure.core.pipeline.policies._retry.RetryPolicyBase", "builtins.object"], "names": {".class": "SymbolTable", "BACKOFF_MAX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.BACKOFF_MAX", "name": "BACKOFF_MAX", "type": "builtins.int"}}, "_RETRY_CODES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._RETRY_CODES", "name": "_RETRY_CODES", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_SAFE_CODES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._SAFE_CODES", "name": "_SAFE_CODES", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RetryPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_configure_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "retry_settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._configure_positions", "name": "_configure_positions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "retry_settings"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase._configure_positions", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_configure_positions of RetryPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase._configure_positions", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}]}}}, "_configure_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "absolute_timeout", "is_response_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._configure_timeout", "name": "_configure_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "absolute_timeout", "is_response_error"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase._configure_timeout", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_configure_timeout of RetryPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase._configure_timeout", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}]}}}, "_is_connection_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "err"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._is_connection_error", "name": "_is_connection_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "err"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_connection_error of RetryPolicyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_method_retryable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "settings", "request", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._is_method_retryable", "name": "_is_method_retryable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "settings", "request", "response"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase._is_method_retryable", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.AllHttpResponseType", "id": -2, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase._is_method_retryable", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_method_retryable of RetryPolicyBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase._is_method_retryable", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.AllHttpResponseType", "id": -2, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase._is_method_retryable", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}]}}}, "_is_read_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "err"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._is_read_error", "name": "_is_read_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "err"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_read_error of RetryPolicyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_method_whitelist": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._method_whitelist", "name": "_method_whitelist", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_respect_retry_after_header": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._respect_retry_after_header", "name": "_respect_retry_after_header", "type": "builtins.bool"}}, "_retry_on_status_codes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase._retry_on_status_codes", "name": "_retry_on_status_codes", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "backoff_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.backoff_factor", "name": "backoff_factor", "type": "builtins.float"}}, "backoff_max": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.backoff_max", "name": "backoff_max", "type": "builtins.int"}}, "configure_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.configure_retries", "name": "configure_retries", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "options"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configure_retries of RetryPolicyBase", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect_retries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.connect_retries", "name": "connect_retries", "type": "builtins.int"}}, "get_backoff_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.get_backoff_time", "name": "get_backoff_time", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "settings"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_backoff_time of RetryPolicyBase", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_retry_after": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.get_retry_after", "name": "get_retry_after", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.AllHttpResponseType", "id": -1, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.get_retry_after", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_retry_after of RetryPolicyBase", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.AllHttpResponseType", "id": -1, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.get_retry_after", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}]}}}, "increment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "settings", "response", "error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.increment", "name": "increment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "settings", "response", "error"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.increment", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineRequest"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.increment", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.AllHttpResponseType", "id": -2, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.increment", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "increment of RetryPolicyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.increment", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.AllHttpResponseType", "id": -2, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.increment", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}]}}}, "is_exhausted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.is_exhausted", "name": "is_exhausted", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "settings"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_exhausted of RetryPolicyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "settings", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.is_retry", "name": "is_retry", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "settings", "response"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.is_retry", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.AllHttpResponseType", "id": -2, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.is_retry", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.PipelineResponse"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_retry of RetryPolicyBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.HTTPRequestType", "id": -1, "name": "HTTPRequestType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.is_retry", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpRequest", "azure.core.pipeline.transport._base.HttpRequest"], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.AllHttpResponseType", "id": -2, "name": "AllHttpResponseType", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.is_retry", "upper_bound": "builtins.object", "values": ["azure.core.rest._rest_py3.HttpResponse", "azure.core.pipeline.transport._base.HttpResponse", "azure.core.rest._rest_py3.AsyncHttpResponse", "azure.core.pipeline.transport._base_async.AsyncHttpResponse"], "variance": 0}]}}}, "no_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.no_retries", "name": "no_retries", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.ClsRetryPolicy", "id": -1, "name": "ClsRetryPolicy", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.no_retries", "upper_bound": "azure.core.pipeline.policies._retry.RetryPolicyBase", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_retries of RetryPolicyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.ClsRetryPolicy", "id": -1, "name": "ClsRetryPolicy", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.no_retries", "upper_bound": "azure.core.pipeline.policies._retry.RetryPolicyBase", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.ClsRetryPolicy", "id": -1, "name": "ClsRetryPolicy", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.no_retries", "upper_bound": "azure.core.pipeline.policies._retry.RetryPolicyBase", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.no_retries", "name": "no_retries", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.ClsRetryPolicy", "id": -1, "name": "ClsRetryPolicy", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.no_retries", "upper_bound": "azure.core.pipeline.policies._retry.RetryPolicyBase", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_retries of RetryPolicyBase", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.ClsRetryPolicy", "id": -1, "name": "ClsRetryPolicy", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.no_retries", "upper_bound": "azure.core.pipeline.policies._retry.RetryPolicyBase", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.ClsRetryPolicy", "id": -1, "name": "ClsRetryPolicy", "namespace": "azure.core.pipeline.policies._retry.RetryPolicyBase.no_retries", "upper_bound": "azure.core.pipeline.policies._retry.RetryPolicyBase", "values": [], "variance": 0}]}}}}, "parse_retry_after": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "retry_after"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.parse_retry_after", "name": "parse_retry_after", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "retry_after"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_retry_after of RetryPolicyBase", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_retries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.read_retries", "name": "read_retries", "type": "builtins.int"}}, "retry_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.retry_mode", "name": "retry_mode", "type": "azure.core.pipeline.policies._retry.RetryMode"}}, "status_retries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.status_retries", "name": "status_retries", "type": "builtins.int"}}, "timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.timeout", "name": "timeout", "type": "builtins.int"}}, "total_retries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.total_retries", "name": "total_retries", "type": "builtins.int"}}, "update_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "retry_settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.update_context", "name": "update_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "retry_settings"], "arg_types": ["azure.core.pipeline.policies._retry.RetryPolicyBase", "azure.core.pipeline.PipelineContext", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_context of RetryPolicyBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.pipeline.policies._retry.RetryPolicyBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.pipeline.policies._retry.RetryPolicyBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SEEK_SET": {".class": "SymbolTableNode", "cross_ref": "io.SEEK_SET", "kind": "Gdef"}, "ServiceRequestError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ServiceRequestError", "kind": "Gdef"}, "ServiceRequestTimeoutError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ServiceRequestTimeoutError", "kind": "Gdef"}, "ServiceResponseError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ServiceResponseError", "kind": "Gdef"}, "ServiceResponseTimeoutError": {".class": "SymbolTableNode", "cross_ref": "azure.core.exceptions.ServiceResponseTimeoutError", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnsupportedOperation": {".class": "SymbolTableNode", "cross_ref": "io.UnsupportedOperation", "kind": "Gdef"}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.pipeline.policies._retry._LOGGER", "name": "_LOGGER", "type": "logging.Logger"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._retry.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._retry.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._retry.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._retry.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._retry.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.pipeline.policies._retry.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_utils": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._utils", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\policies\\_retry.py"}