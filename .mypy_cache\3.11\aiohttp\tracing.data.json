{".class": "MypyFile", "_fullname": "aiohttp.tracing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef", "module_public": false}, "CIMultiDict": {".class": "SymbolTableNode", "cross_ref": "multidict.CIMultiDict", "kind": "Gdef", "module_public": false}, "ClientResponse": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client_reqrep.ClientResponse", "kind": "Gdef", "module_public": false}, "ClientSession": {".class": "SymbolTableNode", "cross_ref": "aiohttp.client.ClientSession", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_public": false}, "Signal": {".class": "SymbolTableNode", "cross_ref": "aiosignal.Signal", "kind": "Gdef", "module_public": false}, "SimpleNamespace": {".class": "SymbolTableNode", "cross_ref": "types.SimpleNamespace", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Trace": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.Trace", "name": "Trace", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.Trace", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.Trace", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "session", "trace_config", "trace_config_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.Trace.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "session", "trace_config", "trace_config_ctx"], "arg_types": ["aiohttp.tracing.Trace", "aiohttp.client.ClientSession", "aiohttp.tracing.TraceConfig", "types.SimpleNamespace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Trace", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.tracing.Trace._session", "name": "_session", "type": "aiohttp.client.ClientSession"}}, "_trace_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.tracing.Trace._trace_config", "name": "_trace_config", "type": "aiohttp.tracing.TraceConfig"}}, "_trace_config_ctx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.tracing.Trace._trace_config_ctx", "name": "_trace_config_ctx", "type": "types.SimpleNamespace"}}, "send_connection_create_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_connection_create_end", "name": "send_connection_create_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.Trace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_connection_create_end of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_connection_create_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_connection_create_start", "name": "send_connection_create_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.Trace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_connection_create_start of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_connection_queued_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_connection_queued_end", "name": "send_connection_queued_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.Trace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_connection_queued_end of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_connection_queued_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_connection_queued_start", "name": "send_connection_queued_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.Trace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_connection_queued_start of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_connection_reuseconn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_connection_reuseconn", "name": "send_connection_reuseconn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.Trace"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_connection_reuseconn of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_dns_cache_hit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_dns_cache_hit", "name": "send_dns_cache_hit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_dns_cache_hit of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_dns_cache_miss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_dns_cache_miss", "name": "send_dns_cache_miss", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_dns_cache_miss of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_dns_resolvehost_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_dns_resolvehost_end", "name": "send_dns_resolvehost_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_dns_resolvehost_end of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_dns_resolvehost_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_dns_resolvehost_start", "name": "send_dns_resolvehost_start", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_dns_resolvehost_start of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_request_chunk_sent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_request_chunk_sent", "name": "send_request_chunk_sent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "chunk"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str", "yarl._url.URL", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_request_chunk_sent of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_request_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_request_end", "name": "send_request_end", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "response"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_request_end of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_request_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_request_exception", "name": "send_request_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "exception"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_request_exception of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_request_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_request_headers", "name": "send_request_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_request_headers of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_request_redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_request_redirect", "name": "send_request_redirect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "response"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_request_redirect of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_request_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_request_start", "name": "send_request_start", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_request_start of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_response_chunk_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "aiohttp.tracing.Trace.send_response_chunk_received", "name": "send_response_chunk_received", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "chunk"], "arg_types": ["aiohttp.tracing.Trace", "builtins.str", "yarl._url.URL", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_response_chunk_received of Trace", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.Trace.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.Trace", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConfig", "name": "TraceConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConfig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "trace_config_ctx_factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "trace_config_ctx_factory"], "arg_types": ["aiohttp.tracing.TraceConfig", {".class": "TypeType", "item": "types.SimpleNamespace"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_connection_create_end": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_connection_create_end", "name": "_on_connection_create_end", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionCreateEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_connection_create_start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_connection_create_start", "name": "_on_connection_create_start", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionCreateStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_connection_queued_end": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_connection_queued_end", "name": "_on_connection_queued_end", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionQueuedEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_connection_queued_start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_connection_queued_start", "name": "_on_connection_queued_start", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionQueuedStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_connection_reuseconn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_connection_reuseconn", "name": "_on_connection_reuseconn", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionReuseconnParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_dns_cache_hit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_dns_cache_hit", "name": "_on_dns_cache_hit", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsCacheHitParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_dns_cache_miss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_dns_cache_miss", "name": "_on_dns_cache_miss", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsCacheMissParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_dns_resolvehost_end": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_dns_resolvehost_end", "name": "_on_dns_resolvehost_end", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsResolveHostEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_dns_resolvehost_start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_dns_resolvehost_start", "name": "_on_dns_resolvehost_start", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsResolveHostStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_request_chunk_sent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_request_chunk_sent", "name": "_on_request_chunk_sent", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestChunkSentParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_request_end": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_request_end", "name": "_on_request_end", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_request_exception": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_request_exception", "name": "_on_request_exception", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestExceptionParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_request_headers_sent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_request_headers_sent", "name": "_on_request_headers_sent", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestHeadersSentParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_request_redirect": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_request_redirect", "name": "_on_request_redirect", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestRedirectParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_request_start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_request_start", "name": "_on_request_start", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_on_response_chunk_received": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._on_response_chunk_received", "name": "_on_response_chunk_received", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceResponseChunkReceivedParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}}}, "_trace_config_ctx_factory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiohttp.tracing.TraceConfig._trace_config_ctx_factory", "name": "_trace_config_ctx_factory", "type": {".class": "TypeType", "item": "types.SimpleNamespace"}}}, "freeze": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConfig.freeze", "name": "freeze", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "freeze of TraceConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_connection_create_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_create_end", "name": "on_connection_create_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_create_end of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionCreateEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_create_end", "name": "on_connection_create_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_create_end of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionCreateEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_connection_create_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_create_start", "name": "on_connection_create_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_create_start of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionCreateStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_create_start", "name": "on_connection_create_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_create_start of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionCreateStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_connection_queued_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_queued_end", "name": "on_connection_queued_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_queued_end of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionQueuedEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_queued_end", "name": "on_connection_queued_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_queued_end of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionQueuedEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_connection_queued_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_queued_start", "name": "on_connection_queued_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_queued_start of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionQueuedStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_queued_start", "name": "on_connection_queued_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_queued_start of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionQueuedStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_connection_reuseconn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_reuseconn", "name": "on_connection_reuseconn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_reuseconn of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionReuseconnParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_connection_reuseconn", "name": "on_connection_reuseconn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_connection_reuseconn of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceConnectionReuseconnParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_dns_cache_hit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_dns_cache_hit", "name": "on_dns_cache_hit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_dns_cache_hit of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsCacheHitParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_dns_cache_hit", "name": "on_dns_cache_hit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_dns_cache_hit of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsCacheHitParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_dns_cache_miss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_dns_cache_miss", "name": "on_dns_cache_miss", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_dns_cache_miss of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsCacheMissParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_dns_cache_miss", "name": "on_dns_cache_miss", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_dns_cache_miss of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsCacheMissParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_dns_resolvehost_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_dns_resolvehost_end", "name": "on_dns_resolvehost_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_dns_resolvehost_end of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsResolveHostEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_dns_resolvehost_end", "name": "on_dns_resolvehost_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_dns_resolvehost_end of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsResolveHostEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_dns_resolvehost_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_dns_resolvehost_start", "name": "on_dns_resolvehost_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_dns_resolvehost_start of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsResolveHostStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_dns_resolvehost_start", "name": "on_dns_resolvehost_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_dns_resolvehost_start of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceDnsResolveHostStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_request_chunk_sent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_request_chunk_sent", "name": "on_request_chunk_sent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_chunk_sent of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestChunkSentParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_request_chunk_sent", "name": "on_request_chunk_sent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_chunk_sent of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestChunkSentParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_request_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_request_end", "name": "on_request_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_end of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_request_end", "name": "on_request_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_end of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestEndParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_request_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_request_exception", "name": "on_request_exception", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_exception of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestExceptionParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_request_exception", "name": "on_request_exception", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_exception of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestExceptionParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_request_headers_sent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_request_headers_sent", "name": "on_request_headers_sent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_headers_sent of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestHeadersSentParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_request_headers_sent", "name": "on_request_headers_sent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_headers_sent of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestHeadersSentParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_request_redirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_request_redirect", "name": "on_request_redirect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_redirect of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestRedirectParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_request_redirect", "name": "on_request_redirect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_redirect of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestRedirectParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_request_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_request_start", "name": "on_request_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_start of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_request_start", "name": "on_request_start", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_request_start of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceRequestStartParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on_response_chunk_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aiohttp.tracing.TraceConfig.on_response_chunk_received", "name": "on_response_chunk_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_response_chunk_received of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceResponseChunkReceivedParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aiohttp.tracing.TraceConfig.on_response_chunk_received", "name": "on_response_chunk_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_response_chunk_received of TraceConfig", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["aiohttp.tracing.TraceResponseChunkReceivedParams"], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}], "extra_attrs": null, "type_ref": "aiosignal.Signal"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "trace_config_ctx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "trace_request_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConfig.trace_config_ctx", "name": "trace_config_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "trace_request_ctx"], "arg_types": ["aiohttp.tracing.TraceConfig", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trace_config_ctx of TraceConfig", "ret_type": "types.SimpleNamespace", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceConnectionCreateEndParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams", "name": "TraceConnectionCreateEndParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionCreateEndParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceConnectionCreateEndParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__aiohttp_tracing_TraceConnectionCreateEndParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceConnectionCreateEndParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__aiohttp_tracing_TraceConnectionCreateEndParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionCreateEndParams.__aiohttp_tracing_TraceConnectionCreateEndParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": "aiohttp.tracing.TraceConnectionCreateEndParams.__aiohttp_tracing_TraceConnectionCreateEndParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceConnectionCreateEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceConnectionCreateEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConnectionCreateEndParams"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceConnectionCreateEndParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceConnectionCreateEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceConnectionCreateEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateEndParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceConnectionCreateEndParams", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceConnectionCreateStartParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams", "name": "TraceConnectionCreateStartParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionCreateStartParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceConnectionCreateStartParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__aiohttp_tracing_TraceConnectionCreateStartParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceConnectionCreateStartParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__aiohttp_tracing_TraceConnectionCreateStartParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionCreateStartParams.__aiohttp_tracing_TraceConnectionCreateStartParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": "aiohttp.tracing.TraceConnectionCreateStartParams.__aiohttp_tracing_TraceConnectionCreateStartParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceConnectionCreateStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceConnectionCreateStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConnectionCreateStartParams"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceConnectionCreateStartParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceConnectionCreateStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceConnectionCreateStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionCreateStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionCreateStartParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceConnectionCreateStartParams", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceConnectionQueuedEndParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams", "name": "TraceConnectionQueuedEndParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionQueuedEndParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceConnectionQueuedEndParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__aiohttp_tracing_TraceConnectionQueuedEndParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceConnectionQueuedEndParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__aiohttp_tracing_TraceConnectionQueuedEndParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionQueuedEndParams.__aiohttp_tracing_TraceConnectionQueuedEndParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": "aiohttp.tracing.TraceConnectionQueuedEndParams.__aiohttp_tracing_TraceConnectionQueuedEndParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceConnectionQueuedEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceConnectionQueuedEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConnectionQueuedEndParams"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceConnectionQueuedEndParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceConnectionQueuedEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceConnectionQueuedEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedEndParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceConnectionQueuedEndParams", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceConnectionQueuedStartParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams", "name": "TraceConnectionQueuedStartParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionQueuedStartParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceConnectionQueuedStartParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__aiohttp_tracing_TraceConnectionQueuedStartParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceConnectionQueuedStartParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__aiohttp_tracing_TraceConnectionQueuedStartParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionQueuedStartParams.__aiohttp_tracing_TraceConnectionQueuedStartParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": "aiohttp.tracing.TraceConnectionQueuedStartParams.__aiohttp_tracing_TraceConnectionQueuedStartParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceConnectionQueuedStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceConnectionQueuedStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConnectionQueuedStartParams"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceConnectionQueuedStartParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceConnectionQueuedStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceConnectionQueuedStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionQueuedStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionQueuedStartParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceConnectionQueuedStartParams", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceConnectionReuseconnParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams", "name": "TraceConnectionReuseconnParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionReuseconnParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceConnectionReuseconnParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__aiohttp_tracing_TraceConnectionReuseconnParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceConnectionReuseconnParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__aiohttp_tracing_TraceConnectionReuseconnParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceConnectionReuseconnParams.__aiohttp_tracing_TraceConnectionReuseconnParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": "aiohttp.tracing.TraceConnectionReuseconnParams.__aiohttp_tracing_TraceConnectionReuseconnParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceConnectionReuseconnParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceConnectionReuseconnParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiohttp.tracing.TraceConnectionReuseconnParams"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceConnectionReuseconnParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceConnectionReuseconnParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceConnectionReuseconnParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceConnectionReuseconnParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceConnectionReuseconnParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceConnectionReuseconnParams", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceDnsCacheHitParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceDnsCacheHitParams", "name": "TraceDnsCacheHitParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 316, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "host"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceDnsCacheHitParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceDnsCacheHitParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__aiohttp_tracing_TraceDnsCacheHitParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceDnsCacheHitParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__aiohttp_tracing_TraceDnsCacheHitParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceDnsCacheHitParams.__aiohttp_tracing_TraceDnsCacheHitParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "host", "name": "host", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceDnsCacheHitParams.__aiohttp_tracing_TraceDnsCacheHitParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceDnsCacheHitParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceDnsCacheHitParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["aiohttp.tracing.TraceDnsCacheHitParams", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceDnsCacheHitParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceDnsCacheHitParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceDnsCacheHitParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheHitParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "host"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.host", "name": "host", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheHitParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceDnsCacheHitParams", "values": [], "variance": 0}, "slots": ["host"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceDnsCacheMissParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceDnsCacheMissParams", "name": "TraceDnsCacheMissParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 323, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "host"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceDnsCacheMissParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceDnsCacheMissParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__aiohttp_tracing_TraceDnsCacheMissParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceDnsCacheMissParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__aiohttp_tracing_TraceDnsCacheMissParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceDnsCacheMissParams.__aiohttp_tracing_TraceDnsCacheMissParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "host", "name": "host", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceDnsCacheMissParams.__aiohttp_tracing_TraceDnsCacheMissParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceDnsCacheMissParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceDnsCacheMissParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["aiohttp.tracing.TraceDnsCacheMissParams", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceDnsCacheMissParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceDnsCacheMissParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceDnsCacheMissParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsCacheMissParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "host"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.host", "name": "host", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsCacheMissParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceDnsCacheMissParams", "values": [], "variance": 0}, "slots": ["host"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceDnsResolveHostEndParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams", "name": "TraceDnsResolveHostEndParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 309, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "host"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceDnsResolveHostEndParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceDnsResolveHostEndParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__aiohttp_tracing_TraceDnsResolveHostEndParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceDnsResolveHostEndParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__aiohttp_tracing_TraceDnsResolveHostEndParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceDnsResolveHostEndParams.__aiohttp_tracing_TraceDnsResolveHostEndParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "host", "name": "host", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceDnsResolveHostEndParams.__aiohttp_tracing_TraceDnsResolveHostEndParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceDnsResolveHostEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceDnsResolveHostEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["aiohttp.tracing.TraceDnsResolveHostEndParams", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceDnsResolveHostEndParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceDnsResolveHostEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceDnsResolveHostEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "host"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.host", "name": "host", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostEndParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceDnsResolveHostEndParams", "values": [], "variance": 0}, "slots": ["host"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceDnsResolveHostStartParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams", "name": "TraceDnsResolveHostStartParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 302, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "host"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceDnsResolveHostStartParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceDnsResolveHostStartParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__aiohttp_tracing_TraceDnsResolveHostStartParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceDnsResolveHostStartParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__aiohttp_tracing_TraceDnsResolveHostStartParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceDnsResolveHostStartParams.__aiohttp_tracing_TraceDnsResolveHostStartParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "host", "name": "host", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceDnsResolveHostStartParams.__aiohttp_tracing_TraceDnsResolveHostStartParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceDnsResolveHostStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceDnsResolveHostStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["aiohttp.tracing.TraceDnsResolveHostStartParams", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceDnsResolveHostStartParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceDnsResolveHostStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceDnsResolveHostStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceDnsResolveHostStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "host"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.host", "name": "host", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceDnsResolveHostStartParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceDnsResolveHostStartParams", "values": [], "variance": 0}, "slots": ["host"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceRequestChunkSentParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestChunkSentParams", "name": "TraceRequestChunkSentParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 229, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "method"}, {"alias": null, "context_column": 4, "context_line": 230, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "yarl._url.URL", "kw_only": false, "name": "url"}, {"alias": null, "context_column": 4, "context_line": 231, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.bytes", "kw_only": false, "name": "chunk"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestChunkSentParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceRequestChunkSentParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__aiohttp_tracing_TraceRequestChunkSentParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceRequestChunkSentParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__aiohttp_tracing_TraceRequestChunkSentParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestChunkSentParams.__aiohttp_tracing_TraceRequestChunkSentParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "chunk", "name": "chunk", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "method", "name": "method", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "url", "name": "url", "type": {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceRequestChunkSentParams.__aiohttp_tracing_TraceRequestChunkSentParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceRequestChunkSentParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceRequestChunkSentParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "chunk"], "arg_types": ["aiohttp.tracing.TraceRequestChunkSentParams", "builtins.str", "yarl._url.URL", "builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceRequestChunkSentParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceRequestChunkSentParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceRequestChunkSentParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestChunkSentParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "method"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "chunk"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.chunk", "name": "chunk", "type": "builtins.bytes"}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.method", "name": "method", "type": "builtins.str"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.url", "name": "url", "type": "yarl._url.URL"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestChunkSentParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceRequestChunkSentParams", "values": [], "variance": 0}, "slots": ["chunk", "method", "url"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceRequestEndParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestEndParams", "name": "TraceRequestEndParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestEndParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 247, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "method"}, {"alias": null, "context_column": 4, "context_line": 248, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "yarl._url.URL", "kw_only": false, "name": "url"}, {"alias": null, "context_column": 4, "context_line": 249, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "kw_only": false, "name": "headers"}, {"alias": null, "context_column": 4, "context_line": 250, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "aiohttp.client_reqrep.ClientResponse", "kw_only": false, "name": "response"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestEndParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceRequestEndParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestEndParams.__aiohttp_tracing_TraceRequestEndParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceRequestEndParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestEndParams.__aiohttp_tracing_TraceRequestEndParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestEndParams.__aiohttp_tracing_TraceRequestEndParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "headers", "name": "headers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "method", "name": "method", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "response", "name": "response", "type": {".class": "Instance", "args": ["aiohttp.client_reqrep.ClientResponse"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "url", "name": "url", "type": {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestEndParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["aiohttp.client_reqrep.ClientResponse"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceRequestEndParams.__aiohttp_tracing_TraceRequestEndParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestEndParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceRequestEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestEndParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceRequestEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestEndParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "response"], "arg_types": ["aiohttp.tracing.TraceRequestEndParams", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceRequestEndParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestEndParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceRequestEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestEndParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceRequestEndParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestEndParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestEndParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "method"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "headers"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "response"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestEndParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestEndParams.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestEndParams.method", "name": "method", "type": "builtins.str"}}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestEndParams.response", "name": "response", "type": "aiohttp.client_reqrep.ClientResponse"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestEndParams.url", "name": "url", "type": "yarl._url.URL"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestEndParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceRequestEndParams", "values": [], "variance": 0}, "slots": ["headers", "method", "response", "url"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceRequestExceptionParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestExceptionParams", "name": "TraceRequestExceptionParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestExceptionParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 257, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "method"}, {"alias": null, "context_column": 4, "context_line": 258, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "yarl._url.URL", "kw_only": false, "name": "url"}, {"alias": null, "context_column": 4, "context_line": 259, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "kw_only": false, "name": "headers"}, {"alias": null, "context_column": 4, "context_line": 260, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.BaseException", "kw_only": false, "name": "exception"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestExceptionParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceRequestExceptionParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__aiohttp_tracing_TraceRequestExceptionParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceRequestExceptionParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__aiohttp_tracing_TraceRequestExceptionParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestExceptionParams.__aiohttp_tracing_TraceRequestExceptionParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "exception", "name": "exception", "type": {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "headers", "name": "headers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "method", "name": "method", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "url", "name": "url", "type": {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceRequestExceptionParams.__aiohttp_tracing_TraceRequestExceptionParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceRequestExceptionParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceRequestExceptionParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "exception"], "arg_types": ["aiohttp.tracing.TraceRequestExceptionParams", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceRequestExceptionParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceRequestExceptionParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceRequestExceptionParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestExceptionParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "method"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "headers"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "exception"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.exception", "name": "exception", "type": "builtins.BaseException"}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.method", "name": "method", "type": "builtins.str"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestExceptionParams.url", "name": "url", "type": "yarl._url.URL"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestExceptionParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceRequestExceptionParams", "values": [], "variance": 0}, "slots": ["exception", "headers", "method", "url"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceRequestHeadersSentParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams", "name": "TraceRequestHeadersSentParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 330, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "method"}, {"alias": null, "context_column": 4, "context_line": 331, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "yarl._url.URL", "kw_only": false, "name": "url"}, {"alias": null, "context_column": 4, "context_line": 332, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "kw_only": false, "name": "headers"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestHeadersSentParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceRequestHeadersSentParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__aiohttp_tracing_TraceRequestHeadersSentParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceRequestHeadersSentParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__aiohttp_tracing_TraceRequestHeadersSentParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestHeadersSentParams.__aiohttp_tracing_TraceRequestHeadersSentParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "headers", "name": "headers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "method", "name": "method", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "url", "name": "url", "type": {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceRequestHeadersSentParams.__aiohttp_tracing_TraceRequestHeadersSentParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceRequestHeadersSentParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceRequestHeadersSentParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers"], "arg_types": ["aiohttp.tracing.TraceRequestHeadersSentParams", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceRequestHeadersSentParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceRequestHeadersSentParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceRequestHeadersSentParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestHeadersSentParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "method"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "headers"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.method", "name": "method", "type": "builtins.str"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.url", "name": "url", "type": "yarl._url.URL"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestHeadersSentParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceRequestHeadersSentParams", "values": [], "variance": 0}, "slots": ["headers", "method", "url"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceRequestRedirectParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestRedirectParams", "name": "TraceRequestRedirectParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestRedirectParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 267, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "method"}, {"alias": null, "context_column": 4, "context_line": 268, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "yarl._url.URL", "kw_only": false, "name": "url"}, {"alias": null, "context_column": 4, "context_line": 269, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "kw_only": false, "name": "headers"}, {"alias": null, "context_column": 4, "context_line": 270, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "aiohttp.client_reqrep.ClientResponse", "kw_only": false, "name": "response"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestRedirectParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceRequestRedirectParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__aiohttp_tracing_TraceRequestRedirectParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceRequestRedirectParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__aiohttp_tracing_TraceRequestRedirectParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestRedirectParams.__aiohttp_tracing_TraceRequestRedirectParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "headers", "name": "headers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "method", "name": "method", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "response", "name": "response", "type": {".class": "Instance", "args": ["aiohttp.client_reqrep.ClientResponse"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "url", "name": "url", "type": {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["aiohttp.client_reqrep.ClientResponse"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceRequestRedirectParams.__aiohttp_tracing_TraceRequestRedirectParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceRequestRedirectParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceRequestRedirectParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers", "response"], "arg_types": ["aiohttp.tracing.TraceRequestRedirectParams", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "aiohttp.client_reqrep.ClientResponse"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceRequestRedirectParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceRequestRedirectParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceRequestRedirectParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestRedirectParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "method"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "headers"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "response"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.method", "name": "method", "type": "builtins.str"}}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.response", "name": "response", "type": "aiohttp.client_reqrep.ClientResponse"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestRedirectParams.url", "name": "url", "type": "yarl._url.URL"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestRedirectParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceRequestRedirectParams", "values": [], "variance": 0}, "slots": ["headers", "method", "response", "url"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceRequestStartParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestStartParams", "name": "TraceRequestStartParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestStartParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 220, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "method"}, {"alias": null, "context_column": 4, "context_line": 221, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "yarl._url.URL", "kw_only": false, "name": "url"}, {"alias": null, "context_column": 4, "context_line": 222, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}, "kw_only": false, "name": "headers"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestStartParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceRequestStartParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceRequestStartParams.__aiohttp_tracing_TraceRequestStartParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceRequestStartParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestStartParams.__aiohttp_tracing_TraceRequestStartParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceRequestStartParams.__aiohttp_tracing_TraceRequestStartParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "headers", "name": "headers", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "method", "name": "method", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "url", "name": "url", "type": {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestStartParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceRequestStartParams.__aiohttp_tracing_TraceRequestStartParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestStartParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceRequestStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestStartParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceRequestStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestStartParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "headers"], "arg_types": ["aiohttp.tracing.TraceRequestStartParams", "builtins.str", "yarl._url.URL", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceRequestStartParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestStartParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceRequestStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceRequestStartParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceRequestStartParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceRequestStartParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestStartParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "method"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "headers"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceRequestStartParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestStartParams.headers", "name": "headers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "multidict.CIMultiDict"}}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestStartParams.method", "name": "method", "type": "builtins.str"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceRequestStartParams.url", "name": "url", "type": "yarl._url.URL"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceRequestStartParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceRequestStartParams", "values": [], "variance": 0}, "slots": ["headers", "method", "url"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TraceResponseChunkReceivedParams": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams", "name": "TraceResponseChunkReceivedParams", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"attrs": {"attributes": [{"alias": null, "context_column": 4, "context_line": 238, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.str", "kw_only": false, "name": "method"}, {"alias": null, "context_column": 4, "context_line": 239, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "yarl._url.URL", "kw_only": false, "name": "url"}, {"alias": null, "context_column": 4, "context_line": 240, "converter_init_type": null, "has_converter": false, "has_default": false, "init": true, "init_type": "builtins.bytes", "kw_only": false, "name": "chunk"}], "frozen": true}, "attrs_tag": {}}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceResponseChunkReceivedParams", "builtins.object"], "names": {".class": "SymbolTable", "_AT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "name": "_AT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__aiohttp_tracing_TraceResponseChunkReceivedParams_AttrsAttributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "attr.Attribute"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__aiohttp_tracing_TraceResponseChunkReceivedParams_AttrsAttributes__", "name": "__aiohttp_tracing_TraceResponseChunkReceivedParams_AttrsAttributes__", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__aiohttp_tracing_TraceResponseChunkReceivedParams_AttrsAttributes__", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing.TraceResponseChunkReceivedParams.__aiohttp_tracing_TraceResponseChunkReceivedParams_AttrsAttributes__", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "chunk", "name": "chunk", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "method", "name": "method", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "url", "name": "url", "type": {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}}, "plugin_generated": true}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}, "plugin_generated": true}, "__attrs_attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__attrs_attrs__", "name": "__attrs_attrs__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["yarl._url.URL"], "extra_attrs": null, "type_ref": "attr.Attribute"}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "attr.Attribute"}], "partial_fallback": "aiohttp.tracing.TraceResponseChunkReceivedParams.__aiohttp_tracing_TraceResponseChunkReceivedParams_AttrsAttributes__"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of TraceResponseChunkReceivedParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of TraceResponseChunkReceivedParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "cross_ref": "builtins.object.__hash__", "kind": "<PERSON><PERSON><PERSON>"}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "method", "url", "chunk"], "arg_types": ["aiohttp.tracing.TraceResponseChunkReceivedParams", "builtins.str", "yarl._url.URL", "builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TraceResponseChunkReceivedParams", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of TraceResponseChunkReceivedParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of TraceResponseChunkReceivedParams", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams._AT", "id": -1, "name": "_AT", "namespace": "aiohttp.tracing.TraceResponseChunkReceivedParams.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "method"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "chunk"}, "type_ref": "builtins.str"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.chunk", "name": "chunk", "type": "builtins.bytes"}}, "method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.method", "name": "method", "type": "builtins.str"}}, "url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.url", "name": "url", "type": "yarl._url.URL"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing.TraceResponseChunkReceivedParams.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiohttp.tracing.TraceResponseChunkReceivedParams", "values": [], "variance": 0}, "slots": ["chunk", "method", "url"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "URL": {".class": "SymbolTableNode", "cross_ref": "yarl._url.URL", "kind": "Gdef", "module_public": false}, "_ParamT_contra": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing._ParamT_contra", "name": "_ParamT_contra", "upper_bound": "builtins.object", "values": [], "variance": 2}}, "_SignalCallback": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiohttp.tracing._SignalCallback", "name": "_SignalCallback", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing._ParamT_contra", "id": 1, "name": "_ParamT_contra", "namespace": "aiohttp.tracing._SignalCallback", "upper_bound": "builtins.object", "values": [], "variance": 2}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "aiohttp.tracing._SignalCallback", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "aiohttp.tracing", "mro": ["aiohttp.tracing._SignalCallback", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_mypy_only"], "fullname": "aiohttp.tracing._SignalCallback.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing._ParamT_contra", "id": 1, "name": "_ParamT_contra", "namespace": "aiohttp.tracing._SignalCallback", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}, "aiohttp.client.ClientSession", "types.SimpleNamespace", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing._ParamT_contra", "id": 1, "name": "_ParamT_contra", "namespace": "aiohttp.tracing._SignalCallback", "upper_bound": "builtins.object", "values": [], "variance": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SignalCallback", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing._SignalCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiohttp.tracing._ParamT_contra", "id": 1, "name": "_ParamT_contra", "namespace": "aiohttp.tracing._SignalCallback", "upper_bound": "builtins.object", "values": [], "variance": 2}], "extra_attrs": null, "type_ref": "aiohttp.tracing._SignalCallback"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ParamT_contra"], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiohttp.tracing.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.tracing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.tracing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.tracing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.tracing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.tracing.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiohttp.tracing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "attr": {".class": "SymbolTableNode", "cross_ref": "attr", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\aiohttp\\tracing.py"}