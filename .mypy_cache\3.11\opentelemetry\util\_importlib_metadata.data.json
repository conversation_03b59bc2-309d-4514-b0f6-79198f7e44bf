{".class": "MypyFile", "_fullname": "opentelemetry.util._importlib_metadata", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Distribution": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.Distribution", "kind": "Gdef"}, "EntryPoint": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.EntryPoint", "kind": "Gdef"}, "EntryPoints": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.EntryPoints", "kind": "Gdef"}, "PackageNotFoundError": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.PackageNotFoundError", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.util._importlib_metadata.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._importlib_metadata.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._importlib_metadata.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._importlib_metadata.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._importlib_metadata.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._importlib_metadata.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.util._importlib_metadata.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "distributions": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.distributions", "kind": "Gdef"}, "entry_points": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.entry_points", "kind": "Gdef"}, "requires": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.requires", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "importlib_metadata.version", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\util\\_importlib_metadata.py"}