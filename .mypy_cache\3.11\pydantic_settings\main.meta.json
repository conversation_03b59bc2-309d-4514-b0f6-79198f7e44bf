{"data_mtime": 1753366021, "dep_lines": [11, 12, 13, 14, 15, 17, 18, 1, 3, 4, 5, 6, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._signature", "pydantic._internal._utils", "pydantic.dataclasses", "pydantic.main", "pydantic_settings.exceptions", "pydantic_settings.sources", "__future__", "asyncio", "inspect", "threading", "<PERSON><PERSON><PERSON><PERSON>", "types", "typing", "pydantic", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "os", "pathlib", "pydantic._internal", "pydantic._internal._dataclasses", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic_settings.sources.base", "pydantic_settings.sources.providers", "pydantic_settings.sources.providers.cli", "pydantic_settings.sources.providers.env", "pydantic_settings.sources.types", "re"], "hash": "8044a756a381ebb4317fa7d7587fe71ea894a9ab", "id": "pydantic_settings.main", "ignore_all": true, "interface_hash": "f5d170cdb59874cbfb62a76536f03110161ea95c", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\pydantic_settings\\main.py", "plugin_data": null, "size": 28215, "suppressed": [], "version_id": "1.15.0"}