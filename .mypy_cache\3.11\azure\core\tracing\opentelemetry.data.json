{".class": "MypyFile", "_fullname": "azure.core.tracing.opentelemetry", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Attributes": {".class": "SymbolTableNode", "cross_ref": "azure.core.tracing._models.Attributes", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Link": {".class": "SymbolTableNode", "cross_ref": "azure.core.tracing._models.Link", "kind": "Gdef"}, "OpenTelemetryLink": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.Link", "kind": "Gdef"}, "OpenTelemetrySpanKind": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.SpanKind", "kind": "Gdef"}, "OpenTelemetryTracer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer", "name": "OpenTelemetryTracer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.tracing.opentelemetry", "mro": ["azure.core.tracing.opentelemetry.OpenTelemetryTracer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "library_name", "library_version", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "library_name", "library_version", "schema_url", "attributes"], "arg_types": ["azure.core.tracing.opentelemetry.OpenTelemetryTracer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.tracing._models.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OpenTelemetryTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_detach_from_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer._detach_from_context", "name": "_detach_from_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "token"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "_contextvars.Token"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detach_from_context of OpenTelemetryTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer._detach_from_context", "name": "_detach_from_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "token"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "_contextvars.Token"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detach_from_context of OpenTelemetryTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_parse_links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "links"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer._parse_links", "name": "_parse_links", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "links"], "arg_types": ["azure.core.tracing.opentelemetry.OpenTelemetryTracer", {".class": "UnionType", "items": [{".class": "Instance", "args": ["azure.core.tracing._models.Link"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_links of OpenTelemetryTracer", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["opentelemetry.trace.Link"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_suppress_auto_http_instrumentation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer._suppress_auto_http_instrumentation", "name": "_suppress_auto_http_instrumentation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_suppress_auto_http_instrumentation of OpenTelemetryTracer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "_contextvars.Token"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer._suppress_auto_http_instrumentation", "name": "_suppress_auto_http_instrumentation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_suppress_auto_http_instrumentation of OpenTelemetryTracer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "_contextvars.Token"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_tracer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer._tracer", "name": "_tracer", "type": "opentelemetry.trace.Tracer"}}, "get_current_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.get_current_span", "name": "get_current_span", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current_span of OpenTelemetryTracer", "ret_type": "opentelemetry.trace.span.Span", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.get_current_span", "name": "get_current_span", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_current_span of OpenTelemetryTracer", "ret_type": "opentelemetry.trace.span.Span", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_trace_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.get_trace_context", "name": "get_trace_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_trace_context of OpenTelemetryTracer", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.get_trace_context", "name": "get_trace_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_trace_context of OpenTelemetryTracer", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_span_error_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["span", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.set_span_error_status", "name": "set_span_error_status", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["span", "description"], "arg_types": ["opentelemetry.trace.span.Span", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_span_error_status of OpenTelemetryTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.set_span_error_status", "name": "set_span_error_status", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["span", "description"], "arg_types": ["opentelemetry.trace.span.Span", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_span_error_status of OpenTelemetryTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "start_as_current_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "name", "kind", "attributes", "links", "end_on_exit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.start_as_current_span", "name": "start_as_current_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "name", "kind", "attributes", "links", "end_on_exit"], "arg_types": ["azure.core.tracing.opentelemetry.OpenTelemetryTracer", "builtins.str", "azure.core.tracing._models.SpanKind", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.tracing._models.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["azure.core.tracing._models.Link"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_as_current_span of OpenTelemetryTracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.start_as_current_span", "name": "start_as_current_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "name", "kind", "attributes", "links", "end_on_exit"], "arg_types": ["azure.core.tracing.opentelemetry.OpenTelemetryTracer", "builtins.str", "azure.core.tracing._models.SpanKind", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.tracing._models.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["azure.core.tracing._models.Link"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_as_current_span of OpenTelemetryTracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "start_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "name", "kind", "attributes", "links"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.start_span", "name": "start_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "name", "kind", "attributes", "links"], "arg_types": ["azure.core.tracing.opentelemetry.OpenTelemetryTracer", "builtins.str", "azure.core.tracing._models.SpanKind", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "azure.core.tracing._models.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["azure.core.tracing._models.Link"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_span of OpenTelemetryTracer", "ret_type": "opentelemetry.trace.span.Span", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["cls", "span", "end_on_exit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.use_span", "name": "use_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["cls", "span", "end_on_exit"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}, "opentelemetry.trace.span.Span", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "use_span of OpenTelemetryTracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.use_span", "name": "use_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["cls", "span", "end_on_exit"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}, "opentelemetry.trace.span.Span", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "use_span of OpenTelemetryTracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_current_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.with_current_context", "name": "with_current_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "func"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_current_context of OpenTelemetryTracer", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.with_current_context", "name": "with_current_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "func"], "arg_types": [{".class": "TypeType", "item": "azure.core.tracing.opentelemetry.OpenTelemetryTracer"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_current_context of OpenTelemetryTracer", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.tracing.opentelemetry.OpenTelemetryTracer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.tracing.opentelemetry.OpenTelemetryTracer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.Span", "kind": "Gdef"}, "SpanKind": {".class": "SymbolTableNode", "cross_ref": "azure.core.tracing._models.SpanKind", "kind": "Gdef"}, "StatusCode": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.status.StatusCode", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "cross_ref": "_contextvars.Token", "kind": "Gdef"}, "VERSION": {".class": "SymbolTableNode", "cross_ref": "azure.core._version.VERSION", "kind": "Gdef"}, "_DEFAULT_MODULE_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.tracing.opentelemetry._DEFAULT_MODULE_NAME", "name": "_DEFAULT_MODULE_NAME", "type": "builtins.str"}}, "_DEFAULT_SCHEMA_URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "azure.core.tracing.opentelemetry._DEFAULT_SCHEMA_URL", "name": "_DEFAULT_SCHEMA_URL", "type": "builtins.str"}}, "_KIND_MAPPINGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "azure.core.tracing.opentelemetry._KIND_MAPPINGS", "name": "_KIND_MAPPINGS", "type": {".class": "Instance", "args": ["azure.core.tracing._models.SpanKind", "opentelemetry.trace.SpanKind"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SUPPRESS_HTTP_INSTRUMENTATION_KEY": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context._SUPPRESS_HTTP_INSTRUMENTATION_KEY", "kind": "Gdef"}, "_SpanKind": {".class": "SymbolTableNode", "cross_ref": "azure.core.tracing._models.SpanKind", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.tracing.opentelemetry.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.tracing.opentelemetry.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.tracing.opentelemetry.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.tracing.opentelemetry.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.tracing.opentelemetry.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.tracing.opentelemetry.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "extract": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.propagate.extract", "kind": "Gdef"}, "get_current_span_otel": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.propagation.get_current_span", "kind": "Gdef"}, "inject": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.propagate.inject", "kind": "Gdef"}, "otel_context_module": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context", "kind": "Gdef"}, "trace": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\tracing\\opentelemetry.py"}