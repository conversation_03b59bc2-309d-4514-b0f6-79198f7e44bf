{".class": "MypyFile", "_fullname": "azure.core.configuration", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.AnyPolicy", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.AnyPolicy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 4, "fullname": "azure.core.configuration.AnyPolicy", "line": 35, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.AnyPolicy", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.AnyPolicy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.HTTPPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.AnyPolicy", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.AnyPolicy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.AnyPolicy", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.AnyPolicy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy"}], "uses_pep604_syntax": false}}}, "AsyncHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base_async.AsyncHTTPPolicy", "kind": "Gdef"}, "Configuration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.configuration.Configuration", "name": "Configuration", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.configuration.Configuration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.configuration", "mro": ["azure.core.configuration.Configuration", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.configuration.Configuration.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.configuration.Configuration"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Configuration", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "authentication_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.authentication_policy", "name": "authentication_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "custom_hook_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.custom_hook_policy", "name": "custom_hook_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "headers_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.headers_policy", "name": "headers_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "http_logging_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.http_logging_policy", "name": "http_logging_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "logging_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.logging_policy", "name": "logging_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "polling_interval": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.polling_interval", "name": "polling_interval", "type": "builtins.float"}}, "proxy_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.proxy_policy", "name": "proxy_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "redirect_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.redirect_policy", "name": "redirect_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "request_id_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.request_id_policy", "name": "request_id_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "retry_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.retry_policy", "name": "retry_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "user_agent_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "azure.core.configuration.Configuration.user_agent_policy", "name": "user_agent_policy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "azure.core.configuration.AnyPolicy"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.Configuration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "id": 1, "name": "HTTPRequestType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "id": 2, "name": "HTTPResponseType", "namespace": "azure.core.configuration.Configuration", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "azure.core.configuration.Configuration"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["HTTPRequestType", "HTTPResponseType"], "typeddict_type": null}}, "ConnectionConfiguration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "azure.core.configuration.ConnectionConfiguration", "name": "ConnectionConfiguration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "azure.core.configuration.ConnectionConfiguration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "azure.core.configuration", "mro": ["azure.core.configuration.ConnectionConfiguration", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "connection_timeout", "read_timeout", "connection_verify", "connection_cert", "connection_data_block_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "azure.core.configuration.ConnectionConfiguration.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "connection_timeout", "read_timeout", "connection_verify", "connection_cert", "connection_data_block_size", "kwargs"], "arg_types": ["azure.core.configuration.ConnectionConfiguration", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.bool", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionConfiguration", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.configuration.ConnectionConfiguration.cert", "name": "cert", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "data_block_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.configuration.ConnectionConfiguration.data_block_size", "name": "data_block_size", "type": "builtins.int"}}, "read_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.configuration.ConnectionConfiguration.read_timeout", "name": "read_timeout", "type": "builtins.float"}}, "timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.configuration.ConnectionConfiguration.timeout", "name": "timeout", "type": "builtins.float"}}, "verify": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "azure.core.configuration.ConnectionConfiguration.verify", "name": "verify", "type": {".class": "UnionType", "items": ["builtins.bool", "builtins.str"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.ConnectionConfiguration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "azure.core.configuration.ConnectionConfiguration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "HTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.HTTPPolicy", "kind": "Gdef"}, "HTTPRequestType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPRequestType", "name": "HTTPRequestType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "HTTPResponseType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "azure.core.configuration.HTTPResponseType", "name": "HTTPResponseType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SansIOHTTPPolicy": {".class": "SymbolTableNode", "cross_ref": "azure.core.pipeline.policies._base.SansIOHTTPPolicy", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.configuration.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.configuration.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.configuration.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.configuration.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.configuration.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "azure.core.configuration.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\configuration.py"}