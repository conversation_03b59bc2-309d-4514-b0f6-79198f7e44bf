{".class": "MypyFile", "_fullname": "google.auth._default", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "_AUTHORIZED_USER_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth._default._AUTHORIZED_USER_TYPE", "name": "_AUTHORIZED_USER_TYPE", "type": "builtins.str"}}, "_AWS_SUBJECT_TOKEN_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth._default._AWS_SUBJECT_TOKEN_TYPE", "name": "_AWS_SUBJECT_TOKEN_TYPE", "type": "builtins.str"}}, "_CLOUD_SDK_CREDENTIALS_WARNING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth._default._CLOUD_SDK_CREDENTIALS_WARNING", "name": "_CLOUD_SDK_CREDENTIALS_WARNING", "type": "builtins.str"}}, "_CLOUD_SDK_MISSING_CREDENTIALS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth._default._CLOUD_SDK_MISSING_CREDENTIALS", "name": "_CLOUD_SDK_MISSING_CREDENTIALS", "type": "builtins.str"}}, "_EXTERNAL_ACCOUNT_AUTHORIZED_USER_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth._default._EXTERNAL_ACCOUNT_AUTHORIZED_USER_TYPE", "name": "_EXTERNAL_ACCOUNT_AUTHORIZED_USER_TYPE", "type": "builtins.str"}}, "_EXTERNAL_ACCOUNT_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth._default._EXTERNAL_ACCOUNT_TYPE", "name": "_EXTERNAL_ACCOUNT_TYPE", "type": "builtins.str"}}, "_GDCH_SERVICE_ACCOUNT_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth._default._GDCH_SERVICE_ACCOUNT_TYPE", "name": "_GDCH_SERVICE_ACCOUNT_TYPE", "type": "builtins.str"}}, "_IMPERSONATED_SERVICE_ACCOUNT_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth._default._IMPERSONATED_SERVICE_ACCOUNT_TYPE", "name": "_IMPERSONATED_SERVICE_ACCOUNT_TYPE", "type": "builtins.str"}}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.auth._default._LOGGER", "name": "_LOGGER", "type": "logging.Logger"}}, "_SERVICE_ACCOUNT_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.auth._default._SERVICE_ACCOUNT_TYPE", "name": "_SERVICE_ACCOUNT_TYPE", "type": "builtins.str"}}, "_VALID_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.auth._default._VALID_TYPES", "name": "_VALID_TYPES", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth._default.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth._default.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth._default.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth._default.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth._default.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.auth._default.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_apply_quota_project_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["credentials", "quota_project_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._apply_quota_project_id", "name": "_apply_quota_project_id", "type": null}}, "_get_authorized_user_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["filename", "info", "scopes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_authorized_user_credentials", "name": "_get_authorized_user_credentials", "type": null}}, "_get_explicit_environ_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["quota_project_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_explicit_environ_credentials", "name": "_get_explicit_environ_credentials", "type": null}}, "_get_external_account_authorized_user_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["filename", "info", "scopes", "default_scopes", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_external_account_authorized_user_credentials", "name": "_get_external_account_authorized_user_credentials", "type": null}}, "_get_external_account_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["info", "filename", "scopes", "default_scopes", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_external_account_credentials", "name": "_get_external_account_credentials", "type": null}}, "_get_gae_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_gae_credentials", "name": "_get_gae_credentials", "type": null}}, "_get_gce_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["request", "quota_project_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_gce_credentials", "name": "_get_gce_credentials", "type": null}}, "_get_gcloud_sdk_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["quota_project_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_gcloud_sdk_credentials", "name": "_get_gcloud_sdk_credentials", "type": null}}, "_get_gdch_service_account_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["filename", "info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_gdch_service_account_credentials", "name": "_get_gdch_service_account_credentials", "type": null}}, "_get_impersonated_service_account_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["filename", "info", "scopes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_impersonated_service_account_credentials", "name": "_get_impersonated_service_account_credentials", "type": null}}, "_get_service_account_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["filename", "info", "scopes", "default_scopes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._get_service_account_credentials", "name": "_get_service_account_credentials", "type": null}}, "_load_credentials_from_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["filename", "info", "scopes", "default_scopes", "quota_project_id", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._load_credentials_from_info", "name": "_load_credentials_from_info", "type": null}}, "_warn_about_problematic_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default._warn_about_problematic_credentials", "name": "_warn_about_problematic_credentials", "type": null}}, "default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["scopes", "request", "quota_project_id", "default_scopes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default.default", "name": "default", "type": null}}, "environment_vars": {".class": "SymbolTableNode", "cross_ref": "google.auth.environment_vars", "kind": "Gdef"}, "exceptions": {".class": "SymbolTableNode", "cross_ref": "google.auth.exceptions", "kind": "Gdef"}, "get_api_key_credentials": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default.get_api_key_credentials", "name": "get_api_key_credentials", "type": null}}, "google": {".class": "SymbolTableNode", "cross_ref": "google", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load_credentials_from_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["info", "scopes", "default_scopes", "quota_project_id", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default.load_credentials_from_dict", "name": "load_credentials_from_dict", "type": null}}, "load_credentials_from_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["filename", "scopes", "default_scopes", "quota_project_id", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.auth._default.load_credentials_from_file", "name": "load_credentials_from_file", "type": null}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\google\\auth\\_default.py"}