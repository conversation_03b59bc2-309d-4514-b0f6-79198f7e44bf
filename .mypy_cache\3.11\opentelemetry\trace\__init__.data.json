{".class": "MypyFile", "_fullname": "opentelemetry.trace", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef", "module_public": false}, "BoundedAttributes": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.attributes.BoundedAttributes", "kind": "Gdef", "module_public": false}, "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef", "module_public": false}, "DEFAULT_TRACE_OPTIONS": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.DEFAULT_TRACE_OPTIONS", "kind": "Gdef"}, "DEFAULT_TRACE_STATE": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.DEFAULT_TRACE_STATE", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "INVALID_SPAN": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.INVALID_SPAN", "kind": "Gdef"}, "INVALID_SPAN_CONTEXT": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.INVALID_SPAN_CONTEXT", "kind": "Gdef"}, "INVALID_SPAN_ID": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.INVALID_SPAN_ID", "kind": "Gdef"}, "INVALID_TRACE_ID": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.INVALID_TRACE_ID", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "Link": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.trace._LinkBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace.Link", "name": "Link", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.Link", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace.Link", "opentelemetry.trace._LinkBase", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "context", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.Link.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "context", "attributes"], "arg_types": ["opentelemetry.trace.Link", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace.span.SpanContext"}, {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Link", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.trace.Link._attributes", "name": "_attributes", "type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.trace.Link.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace.Link"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of Link", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace.Link.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace.Link"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of Link", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dropped_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.trace.Link.dropped_attributes", "name": "dropped_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace.Link"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dropped_attributes of Link", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace.Link.dropped_attributes", "name": "dropped_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace.Link"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dropped_attributes of Link", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace.Link.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace.Link", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpTracer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.trace.Tracer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace.NoOpTracer", "name": "NoOpTracer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.NoOpTracer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace.NoOpTracer", "opentelemetry.trace.Tracer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "start_as_current_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception", "end_on_exit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "opentelemetry.trace.NoOpTracer.start_as_current_span", "name": "start_as_current_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception", "end_on_exit"], "arg_types": ["opentelemetry.trace.NoOpTracer", "builtins.str", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, "opentelemetry.trace.SpanKind", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace._Links"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_as_current_span of NoOpTracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace.NoOpTracer.start_as_current_span", "name": "start_as_current_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception", "end_on_exit"], "arg_types": ["opentelemetry.trace.NoOpTracer", "builtins.str", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, "opentelemetry.trace.SpanKind", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace._Links"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_as_current_span of NoOpTracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "opentelemetry.util._decorator._AgnosticContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "start_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.NoOpTracer.start_span", "name": "start_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception"], "arg_types": ["opentelemetry.trace.NoOpTracer", "builtins.str", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, "opentelemetry.trace.SpanKind", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace._Links"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_span of NoOpTracer", "ret_type": "opentelemetry.trace.span.Span", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace.NoOpTracer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace.NoOpTracer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoOpTracerProvider": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.trace.TracerProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace.NoOpTracerProvider", "name": "NoOpTracerProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.NoOpTracerProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace.NoOpTracerProvider", "opentelemetry.trace.TracerProvider", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_tracer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "instrumenting_module_name", "instrumenting_library_version", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.NoOpTracerProvider.get_tracer", "name": "get_tracer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "instrumenting_module_name", "instrumenting_library_version", "schema_url", "attributes"], "arg_types": ["opentelemetry.trace.NoOpTracerProvider", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tracer of NoOpTracerProvider", "ret_type": "opentelemetry.trace.Tracer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace.NoOpTracerProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace.NoOpTracerProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NonRecordingSpan": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.NonRecordingSpan", "kind": "Gdef"}, "OTEL_PYTHON_TRACER_PROVIDER": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.environment_variables.OTEL_PYTHON_TRACER_PROVIDER", "kind": "Gdef", "module_public": false}, "Once": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.util._once.Once", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ProxyTracer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.trace.Tracer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace.ProxyTracer", "name": "ProxyTracer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.ProxyTracer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace.ProxyTracer", "opentelemetry.trace.Tracer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "instrumenting_module_name", "instrumenting_library_version", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.ProxyTracer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "instrumenting_module_name", "instrumenting_library_version", "schema_url", "attributes"], "arg_types": ["opentelemetry.trace.ProxyTracer", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProxyTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.trace.ProxyTracer._attributes", "name": "_attributes", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_instrumenting_library_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.trace.ProxyTracer._instrumenting_library_version", "name": "_instrumenting_library_version", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_instrumenting_module_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.trace.ProxyTracer._instrumenting_module_name", "name": "_instrumenting_module_name", "type": "builtins.str"}}, "_noop_tracer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.trace.ProxyTracer._noop_tracer", "name": "_noop_tracer", "type": "opentelemetry.trace.NoOpTracer"}}, "_real_tracer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace.ProxyTracer._real_tracer", "name": "_real_tracer", "type": {".class": "UnionType", "items": ["opentelemetry.trace.Tracer", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_schema_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.trace.ProxyTracer._schema_url", "name": "_schema_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_tracer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.trace.ProxyTracer._tracer", "name": "_tracer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace.ProxyTracer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tracer of ProxyTracer", "ret_type": "opentelemetry.trace.Tracer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace.ProxyTracer._tracer", "name": "_tracer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace.ProxyTracer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tracer of ProxyTracer", "ret_type": "opentelemetry.trace.Tracer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "start_as_current_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "opentelemetry.trace.ProxyTracer.start_as_current_span", "name": "start_as_current_span", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["opentelemetry.trace.ProxyTracer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_as_current_span of ProxyTracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace.ProxyTracer.start_as_current_span", "name": "start_as_current_span", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["opentelemetry.trace.ProxyTracer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_as_current_span of ProxyTracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "opentelemetry.util._decorator._AgnosticContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "start_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.ProxyTracer.start_span", "name": "start_span", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["opentelemetry.trace.ProxyTracer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_span of ProxyTracer", "ret_type": "opentelemetry.trace.span.Span", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace.ProxyTracer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace.ProxyTracer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxyTracerProvider": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.trace.TracerProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace.ProxyTracerProvider", "name": "ProxyTracerProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.ProxyTracerProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace.ProxyTracerProvider", "opentelemetry.trace.TracerProvider", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_tracer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "instrumenting_module_name", "instrumenting_library_version", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.ProxyTracerProvider.get_tracer", "name": "get_tracer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "instrumenting_module_name", "instrumenting_library_version", "schema_url", "attributes"], "arg_types": ["opentelemetry.trace.ProxyTracerProvider", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tracer of ProxyTracerProvider", "ret_type": "opentelemetry.trace.Tracer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace.ProxyTracerProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace.ProxyTracerProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Span": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.Span", "kind": "Gdef"}, "SpanContext": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.SpanContext", "kind": "Gdef"}, "SpanKind": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace.SpanKind", "name": "SpanKind", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "opentelemetry.trace.SpanKind", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace.SpanKind", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CLIENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace.SpanKind.CLIENT", "name": "CLIENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "CONSUMER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace.SpanKind.CONSUMER", "name": "CONSUMER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "INTERNAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace.SpanKind.INTERNAL", "name": "INTERNAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "PRODUCER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace.SpanKind.PRODUCER", "name": "PRODUCER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "SERVER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace.SpanKind.SERVER", "name": "SERVER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace.SpanKind.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace.SpanKind", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Status": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.status.Status", "kind": "Gdef"}, "StatusCode": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.status.StatusCode", "kind": "Gdef"}, "TraceFlags": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.TraceFlags", "kind": "Gdef"}, "TraceState": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.TraceState", "kind": "Gdef"}, "Tracer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["start_as_current_span", 1], ["start_span", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace.Tracer", "name": "Tracer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.trace.Tracer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace.Tracer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "start_as_current_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception", "end_on_exit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.trace.Tracer.start_as_current_span", "name": "start_as_current_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception", "end_on_exit"], "arg_types": ["opentelemetry.trace.Tracer", "builtins.str", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, "opentelemetry.trace.SpanKind", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace._Links"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_as_current_span of Tracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace.Tracer.start_as_current_span", "name": "start_as_current_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception", "end_on_exit"], "arg_types": ["opentelemetry.trace.Tracer", "builtins.str", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, "opentelemetry.trace.SpanKind", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace._Links"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_as_current_span of Tracer", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "opentelemetry.util._decorator._AgnosticContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "start_span": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.trace.Tracer.start_span", "name": "start_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception"], "arg_types": ["opentelemetry.trace.Tracer", "builtins.str", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, "opentelemetry.trace.SpanKind", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace._Links"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_span of Tracer", "ret_type": "opentelemetry.trace.span.Span", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace.Tracer.start_span", "name": "start_span", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "context", "kind", "attributes", "links", "start_time", "record_exception", "set_status_on_exception"], "arg_types": ["opentelemetry.trace.Tracer", "builtins.str", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}, "opentelemetry.trace.SpanKind", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace._Links"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_span of Tracer", "ret_type": "opentelemetry.trace.span.Span", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace.Tracer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace.Tracer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TracerProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_tracer", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace.TracerProvider", "name": "Tracer<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.trace.TracerProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace.TracerProvider", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_tracer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "instrumenting_module_name", "instrumenting_library_version", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "opentelemetry.trace.TracerProvider.get_tracer", "name": "get_tracer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "instrumenting_module_name", "instrumenting_library_version", "schema_url", "attributes"], "arg_types": ["opentelemetry.trace.TracerProvider", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tracer of TracerProvider", "ret_type": "opentelemetry.trace.Tracer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace.TracerProvider.get_tracer", "name": "get_tracer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "instrumenting_module_name", "instrumenting_library_version", "schema_url", "attributes"], "arg_types": ["opentelemetry.trace.TracerProvider", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tracer of TracerProvider", "ret_type": "opentelemetry.trace.Tracer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace.TracerProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace.TracerProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DefaultTracer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.trace.NoOpTracer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace._DefaultTracer", "name": "_DefaultTracer", "type_vars": []}, "deletable_attributes": [], "deprecated": "class opentelemetry.trace._DefaultTracer is deprecated: You should use NoOpTracer. Deprecated since version 1.9.0.", "flags": [], "fullname": "opentelemetry.trace._DefaultTracer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace._DefaultTracer", "opentelemetry.trace.NoOpTracer", "opentelemetry.trace.Tracer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace._DefaultTracer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace._DefaultTracer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DefaultTracerProvider": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["opentelemetry.trace.NoOpTracerProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace._DefaultTracerProvider", "name": "_DefaultTracerProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": "class opentelemetry.trace._DefaultTracerProvider is deprecated: You should use NoOpTracerProvider. Deprecated since version 1.9.0.", "flags": [], "fullname": "opentelemetry.trace._DefaultTracerProvider", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace._DefaultTracerProvider", "opentelemetry.trace.NoOpTracerProvider", "opentelemetry.trace.TracerProvider", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace._DefaultTracerProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace._DefaultTracerProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LinkBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["attributes", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "opentelemetry.trace._LinkBase", "name": "_LinkBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "opentelemetry.trace._LinkBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "opentelemetry.trace", "mro": ["opentelemetry.trace._LinkBase", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace._LinkBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["opentelemetry.trace._LinkBase", {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace.span.SpanContext"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _LinkBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "opentelemetry.trace._LinkBase._context", "name": "_context", "type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace.span.SpanContext"}}}, "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "opentelemetry.trace._LinkBase.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace._LinkBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of _LinkBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace._LinkBase.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace._LinkBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of _LinkBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "opentelemetry.trace._LinkBase.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace._LinkBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of _LinkBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace.span.SpanContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "opentelemetry.trace._LinkBase.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["opentelemetry.trace._LinkBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of _LinkBase", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.trace.span.SpanContext"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "opentelemetry.trace._LinkBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "opentelemetry.trace._LinkBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Links": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "opentelemetry.trace._Links", "line": 159, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["opentelemetry.trace.Link"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_PROXY_TRACER_PROVIDER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace._PROXY_TRACER_PROVIDER", "name": "_PROXY_TRACER_PROVIDER", "type": "opentelemetry.trace.ProxyTracerProvider"}}, "_SPAN_KEY": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.propagation._SPAN_KEY", "kind": "Gdef", "module_public": false}, "_TRACER_PROVIDER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "opentelemetry.trace._TRACER_PROVIDER", "name": "_TRACER_PROVIDER", "type": {".class": "UnionType", "items": ["opentelemetry.trace.TracerProvider", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_TRACER_PROVIDER_SET_ONCE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace._TRACER_PROVIDER_SET_ONCE", "name": "_TRACER_PROVIDER_SET_ONCE", "type": "opentelemetry.util._once.Once"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.trace.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_agnosticcontextmanager": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.util._decorator._agnosticcontextmanager", "kind": "Gdef", "module_public": false}, "_load_provider": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.util._providers._load_provider", "kind": "Gdef", "module_public": false}, "_set_tracer_provider": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tracer_provider", "log"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace._set_tracer_provider", "name": "_set_tracer_provider", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tracer_provider", "log"], "arg_types": ["opentelemetry.trace.TracerProvider", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_tracer_provider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "context_api": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context", "kind": "Gdef", "module_public": false}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_public": false}, "format_span_id": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.format_span_id", "kind": "Gdef"}, "format_trace_id": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.format_trace_id", "kind": "Gdef"}, "getLogger": {".class": "SymbolTableNode", "cross_ref": "logging.getLogger", "kind": "Gdef", "module_public": false}, "get_current_span": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.propagation.get_current_span", "kind": "Gdef"}, "get_tracer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["instrumenting_module_name", "instrumenting_library_version", "tracer_provider", "schema_url", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.get_tracer", "name": "get_tracer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["instrumenting_module_name", "instrumenting_library_version", "tracer_provider", "schema_url", "attributes"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.trace.TracerProvider", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "opentelemetry.util.types.Attributes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tracer", "ret_type": "opentelemetry.trace.Tracer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tracer_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.get_tracer_provider", "name": "get_tracer_provider", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tracer_provider", "ret_type": "opentelemetry.trace.TracerProvider", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "opentelemetry.trace.logger", "name": "logger", "type": "logging.Logger"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "set_span_in_context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.propagation.set_span_in_context", "kind": "Gdef"}, "set_tracer_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tracer_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "opentelemetry.trace.set_tracer_provider", "name": "set_tracer_provider", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tracer_provider"], "arg_types": ["opentelemetry.trace.TracerProvider"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_tracer_provider", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "types": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.util.types", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "use_span": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["span", "end_on_exit", "record_exception", "set_status_on_exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "opentelemetry.trace.use_span", "name": "use_span", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["span", "end_on_exit", "record_exception", "set_status_on_exception"], "arg_types": ["opentelemetry.trace.span.Span", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "use_span", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "opentelemetry.trace.use_span", "name": "use_span", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["span", "end_on_exit", "record_exception", "set_status_on_exception"], "arg_types": ["opentelemetry.trace.span.Span", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "use_span", "ret_type": {".class": "Instance", "args": ["opentelemetry.trace.span.Span"], "extra_attrs": null, "type_ref": "opentelemetry.util._decorator._AgnosticContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\trace\\__init__.py"}