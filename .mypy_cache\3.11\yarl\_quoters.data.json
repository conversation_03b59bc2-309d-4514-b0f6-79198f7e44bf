{".class": "MypyFile", "_fullname": "yarl._quoters", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FRAGMENT_QUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.FRAGMENT_QUOTER", "name": "FRAGMENT_QUOTER", "type": "yarl._quoting_py._Quoter"}}, "FRAGMENT_REQUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.FRAGMENT_REQUOTER", "name": "FRAGMENT_REQUOTER", "type": "yarl._quoting_py._Quoter"}}, "PATH_QUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.PATH_QUOTER", "name": "PATH_QUOTER", "type": "yarl._quoting_py._Quoter"}}, "PATH_REQUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.PATH_REQUOTER", "name": "PATH_REQUOTER", "type": "yarl._quoting_py._Quoter"}}, "PATH_SAFE_UNQUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.PATH_SAFE_UNQUOTER", "name": "PATH_SAFE_UNQUOTER", "type": "yarl._quoting_py._Unquoter"}}, "PATH_UNQUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.PATH_UNQUOTER", "name": "PATH_UNQUOTER", "type": "yarl._quoting_py._Unquoter"}}, "QS_UNQUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.QS_UNQUOTER", "name": "QS_UNQUOTER", "type": "yarl._quoting_py._Unquoter"}}, "QUERY_PART_QUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.QUERY_PART_QUOTER", "name": "QUERY_PART_QUOTER", "type": "yarl._quoting_py._Quoter"}}, "QUERY_QUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.QUERY_QUOTER", "name": "QUERY_QUOTER", "type": "yarl._quoting_py._Quoter"}}, "QUERY_REQUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.QUERY_REQUOTER", "name": "QUERY_REQUOTER", "type": "yarl._quoting_py._Quoter"}}, "QUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.QUOTER", "name": "QUOTER", "type": "yarl._quoting_py._Quoter"}}, "REQUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.REQUOTER", "name": "REQUOTER", "type": "yarl._quoting_py._Quoter"}}, "UNQUOTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.UNQUOTER", "name": "UNQUOTER", "type": "yarl._quoting_py._Unquoter"}}, "UNQUOTER_PLUS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "yarl._quoters.UNQUOTER_PLUS", "name": "UNQUOTER_PLUS", "type": "yarl._quoting_py._Unquoter"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_Quoter": {".class": "SymbolTableNode", "cross_ref": "yarl._quoting_py._Quoter", "kind": "Gdef"}, "_Unquoter": {".class": "SymbolTableNode", "cross_ref": "yarl._quoting_py._Unquoter", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yarl._quoters.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yarl._quoters.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yarl._quoters.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yarl._quoters.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yarl._quoters.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "yarl._quoters.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "human_quote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["s", "unsafe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "yarl._quoters.human_quote", "name": "human_quote", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["s", "unsafe"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "human_quote", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\yarl\\_quoters.py"}