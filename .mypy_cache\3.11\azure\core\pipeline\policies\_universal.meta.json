{"data_mtime": 1753366021, "dep_lines": [45, 48, 47, 50, 34, 42, 44, 49, 34, 39, 41, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 20, 10, 5, 10, 10, 10, 10, 10, 20, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["azure.core.pipeline.policies._base", "azure.core.pipeline.transport._base", "azure.core.pipeline.transport", "azure.core.rest._rest_py3", "xml.etree.ElementTree", "azure.core.exceptions", "azure.core.pipeline", "azure.core.rest", "xml.etree", "urllib.parse", "azure.core", "json", "inspect", "logging", "os", "platform", "xml", "types", "re", "uuid", "typing", "urllib", "builtins", "_frozen_importlib", "_typeshed", "abc", "azure.core.rest._helpers", "enum"], "hash": "d68217f6e208c7d6d4865a6d60acb584fcefc62f", "id": "azure.core.pipeline.policies._universal", "ignore_all": true, "interface_hash": "67833a2d2b93fb935a7dec447e33d3747eb5ef75", "mtime": 1750470991, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\azure\\core\\pipeline\\policies\\_universal.py", "plugin_data": null, "size": 31743, "suppressed": [], "version_id": "1.15.0"}