{"data_mtime": 1753365631, "dep_lines": [7, 6, 7, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["importlib.util", "sys", "importlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "84bb6f639ec11b739b9a2331d34fedb79b2355eb", "id": "test_venv", "ignore_all": false, "interface_hash": "0068fa139280b1b1096bb39908565946f498ac76", "mtime": 1753365630, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\llm_proxy_server\\test_venv.py", "plugin_data": null, "size": 1711, "suppressed": [], "version_id": "1.15.0"}