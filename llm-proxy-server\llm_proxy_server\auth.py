"""Authentication management for LLM Proxy Server"""

import asyncio
from pathlib import Path
from typing import Dict, Optional
import structlog

logger = structlog.get_logger()


class AuthManager:
    """Manages user authentication and authorization"""
    
    def __init__(self, auth_config_path: str):
        self.auth_config_path = Path(auth_config_path)
        self.users: Dict[str, Dict[str, str]] = {}
        self._load_users()
    
    def _load_users(self):
        """Load users from the authorized users file"""
        try:
            if not self.auth_config_path.exists():
                logger.warning("Auth config file not found", path=str(self.auth_config_path))
                return
            
            self.users = {}
            with open(self.auth_config_path, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue
                    
                    try:
                        # Format: username:password:is_admin
                        parts = line.split(':')
                        if len(parts) >= 2:
                            username = parts[0]
                            password = parts[1]
                            is_admin = parts[2].lower() == 'true' if len(parts) > 2 else False
                            
                            self.users[username] = {
                                'password': password,
                                'is_admin': is_admin
                            }
                        else:
                            logger.warning("Invalid user format", line=line_num, content=line)
                    except Exception as e:
                        logger.error("Error parsing user line", line=line_num, error=str(e))
            
            logger.info("Loaded users", count=len(self.users))
            
        except Exception as e:
            logger.error("Failed to load auth config", path=str(self.auth_config_path), error=str(e))
    
    async def authenticate(self, token: str) -> Optional[Dict[str, any]]:
        """
        Authenticate a user with a bearer token
        Token format: username:password
        """
        try:
            if ':' not in token:
                logger.debug("Invalid token format - missing colon")
                return None
            
            username, password = token.split(':', 1)
            
            if username not in self.users:
                logger.debug("User not found", username=username)
                return None
            
            user_info = self.users[username]
            if user_info['password'] != password:
                logger.debug("Invalid password", username=username)
                return None
            
            logger.debug("User authenticated", username=username)
            return {
                'username': username,
                'is_admin': user_info['is_admin']
            }
            
        except Exception as e:
            logger.error("Authentication error", error=str(e))
            return None
    
    def reload_users(self):
        """Reload users from the config file"""
        logger.info("Reloading user configuration")
        self._load_users()
    
    def get_user_count(self) -> int:
        """Get the number of loaded users"""
        return len(self.users)
    
    def is_user_admin(self, username: str) -> bool:
        """Check if a user has admin privileges"""
        if username not in self.users:
            return False
        return self.users[username]['is_admin']
