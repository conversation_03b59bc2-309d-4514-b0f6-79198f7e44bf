"""Configuration management for LLM Proxy Server"""

import os
from pathlib import Path
from typing import Optional
from pydantic import BaseModel


class Settings(BaseModel):
    # Server settings
    host: str = "0.0.0.0"
    port: int = 11434
    debug: bool = False

    # Authentication
    auth_enabled: bool = True
    auth_config_path: str = "authorized_users.txt"

    # Host configuration
    hosts_config_path: str = "hosts.yml"

    # Load balancing
    load_balancer_strategy: str = "least_connections"

    # Monitoring
    metrics_enabled: bool = True
    log_level: str = "INFO"
    log_path: Optional[str] = None

    # Performance
    max_concurrent_requests: int = 100
    request_timeout: int = 300

    # Health checks
    health_check_interval: int = 30
    health_check_timeout: int = 10

    def __init__(self, **kwargs):
        # Load from environment variables
        env_values = {
            'host': os.getenv('HOST', '0.0.0.0'),
            'port': int(os.getenv('PORT', '11434')),
            'debug': os.getenv('DEBUG', 'false').lower() == 'true',
            'auth_enabled': os.getenv('AUTH_ENABLED', 'true').lower() == 'true',
            'auth_config_path': os.getenv('AUTH_CONFIG_PATH', 'authorized_users.txt'),
            'hosts_config_path': os.getenv('HOSTS_CONFIG_PATH', 'hosts.yml'),
            'load_balancer_strategy': os.getenv('LOAD_BALANCER_STRATEGY', 'least_connections'),
            'metrics_enabled': os.getenv('METRICS_ENABLED', 'true').lower() == 'true',
            'log_level': os.getenv('LOG_LEVEL', 'INFO'),
            'log_path': os.getenv('LOG_PATH'),
            'max_concurrent_requests': int(os.getenv('MAX_CONCURRENT_REQUESTS', '100')),
            'request_timeout': int(os.getenv('REQUEST_TIMEOUT', '300')),
            'health_check_interval': int(os.getenv('HEALTH_CHECK_INTERVAL', '30')),
            'health_check_timeout': int(os.getenv('HEALTH_CHECK_TIMEOUT', '10')),
        }

        # Override with any provided kwargs
        env_values.update(kwargs)
        super().__init__(**env_values)


def get_settings() -> Settings:
    """Get application settings"""
    return Settings()