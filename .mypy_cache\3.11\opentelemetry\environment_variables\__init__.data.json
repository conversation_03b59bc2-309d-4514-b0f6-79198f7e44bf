{".class": "MypyFile", "_fullname": "opentelemetry.environment_variables", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "OTEL_LOGS_EXPORTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables.OTEL_LOGS_EXPORTER", "name": "OTEL_LOGS_EXPORTER", "type": "builtins.str"}}, "OTEL_METRICS_EXPORTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables.OTEL_METRICS_EXPORTER", "name": "OTEL_METRICS_EXPORTER", "type": "builtins.str"}}, "OTEL_PROPAGATORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables.OTEL_PROPAGATORS", "name": "OTEL_PROPAGATORS", "type": "builtins.str"}}, "OTEL_PYTHON_CONTEXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables.OTEL_PYTHON_CONTEXT", "name": "OTEL_PYTHON_CONTEXT", "type": "builtins.str"}}, "OTEL_PYTHON_ID_GENERATOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables.OTEL_PYTHON_ID_GENERATOR", "name": "OTEL_PYTHON_ID_GENERATOR", "type": "builtins.str"}}, "OTEL_PYTHON_METER_PROVIDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables.OTEL_PYTHON_METER_PROVIDER", "name": "OTEL_PYTHON_METER_PROVIDER", "type": "builtins.str"}}, "OTEL_PYTHON_TRACER_PROVIDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables.OTEL_PYTHON_TRACER_PROVIDER", "name": "OTEL_PYTHON_TRACER_PROVIDER", "type": "builtins.str"}}, "OTEL_TRACES_EXPORTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables.OTEL_TRACES_EXPORTER", "name": "OTEL_TRACES_EXPORTER", "type": "builtins.str"}}, "_OTEL_PYTHON_EVENT_LOGGER_PROVIDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables._OTEL_PYTHON_EVENT_LOGGER_PROVIDER", "name": "_OTEL_PYTHON_EVENT_LOGGER_PROVIDER", "type": "builtins.str"}}, "_OTEL_PYTHON_LOGGER_PROVIDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "opentelemetry.environment_variables._OTEL_PYTHON_LOGGER_PROVIDER", "name": "_OTEL_PYTHON_LOGGER_PROVIDER", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.environment_variables.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.environment_variables.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.environment_variables.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.environment_variables.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.environment_variables.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.environment_variables.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "opentelemetry.environment_variables.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\opentelemetry\\environment_variables\\__init__.py"}